# Dropright Shipping Application - Codebase Context

## Overview

Dropright is a comprehensive shipping management platform that allows businesses to create, manage, and track shipping orders while leveraging better pricing through carrier partnerships. The application serves as a bridge between e-commerce platforms and shipping carriers, offering competitive rates through EasyPost integration.

## Business Model

- **Core Value Proposition**: Better shipping rates than competitors (ShipStation, etc.) through direct carrier partnerships
- **Target Users**: E-commerce businesses using platforms like Amazon, Etsy, eBay, Shopify, WooCommerce
- **Revenue Model**: Wallet-based system where users pre-fund their account to purchase shipping labels
- **Competitive Advantage**: Direct relationships with USPS, FedEx, and DHL providing better pricing than aggregators

## Architecture

### Monorepo Structure

- **Framework**: Turbo monorepo with pnpm workspace
- **Backend**: NestJS API (Fastify-based)
- **Frontend**: Next.js 15 with React 19
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: NextAuth.js with JWT strategy

### Key Packages

- `@repo/database`: Prisma schema and database client
- `@repo/easypost-types`: TypeScript types for EasyPost integration
- `@repo/shared-data`: Shared carrier data and utilities
- `@repo/transactional`: Email templates
- `@repo/typescript-config`: Shared TypeScript configuration

## Core Features

### 1. Order Management

- **Order Creation**: Manual order entry with product details, addresses, and parcel information
- **Batch Processing**: Group orders into batches for efficient processing
- **Order Status Tracking**: Open → Processing → Shipped → Cancelled workflow
- **Split Orders**: Support for dividing large orders into multiple shipments
- **Store Integration**: Connect multiple e-commerce platforms (Amazon, Etsy, eBay, etc.)

### 2. Shipping & Label Management

- **EasyPost Integration**: Core shipping label creation through EasyPost API
- **Carrier Support**: USPS, FedEx, DHL with custom pricing
- **Rate Shopping**: Compare rates across carriers and services
- **Label Generation**: PDF and ZPL format support
- **Tracking**: Real-time shipment status updates
- **Returns Management**: Create return labels and process returns

### 3. Financial Management

- **Wallet System**: Pre-funded account for purchasing labels
- **Payment Methods**: Credit cards, ACH, wire transfers
- **Auto-recharge**: Configurable automatic wallet top-up
- **Transaction History**: Detailed tracking of all financial movements
- **Stripe Integration**: Credit card processing
- **Manual Approval**: Admin approval for ACH and wire transfers

### 4. User & Organization Management

- **Multi-tenant**: Organization-based user management
- **Role-based Access**: Permission system for different features
- **User Invites**: Email-based user invitation system
- **Team Management**: Multiple users per organization with different access levels

### 5. Claims & Insurance

- **Insurance Claims**: File claims for lost, damaged, or stolen packages
- **Document Management**: Upload supporting documents and evidence
- **Claim Processing**: Status tracking from submission to resolution
- **EasyPost Integration**: Leverage EasyPost's insurance system

### 6. Address Management

- **Address Book**: Store and manage frequently used addresses
- **Validation**: Address verification and standardization
- **Multiple Formats**: Support for domestic and international addresses

## Database Schema

### Core Entities

- **Organization**: Top-level entity representing a business
- **User**: Individual users within an organization
- **Wallet**: Financial account for each organization
- **Store**: Connected e-commerce platforms
- **Order**: Customer orders to be fulfilled
- **Shipment**: Shipping labels and tracking
- **Return**: Return shipping labels
- **Claim**: Insurance claims
- **Payment**: Financial transactions

### Key Relationships

- Organization → Users (1:many)
- Organization → Wallet (1:1)
- Organization → Stores (1:many)
- Organization → Orders (1:many)
- Order → Shipments (1:many)
- Order → Returns (1:many)
- Shipment → Claims (1:many)

## API Structure

### Backend Modules (NestJS)

- **AuthModule**: Authentication and authorization
- **UsersModule**: User management
- **OrdersModule**: Order CRUD operations
- **ShipmentsModule**: Shipping label creation and management
- **ReturnsModule**: Return label processing
- **ClaimsModule**: Insurance claim handling
- **FinanceModule**: Wallet and payment management
- **StoresModule**: E-commerce platform integration
- **BatchesModule**: Order batch processing
- **AddressesModule**: Address management
- **ParcelsModule**: Package dimension management
- **RatesModule**: Shipping rate calculation
- **RefundsModule**: Label refund processing
- **WebhookModule**: External service integrations

### Key Services

- **EasypostService**: Integration with EasyPost API
- **WalletService**: Financial account management
- **ShipmentsService**: Shipping label operations
- **OrdersService**: Order processing logic
- **ClaimsService**: Insurance claim processing

## Frontend Structure

### Protected Routes

- **Dashboard**: Overview with analytics and shortcuts
- **Orders**: Order management and batch processing
- **Shipments**: Shipping label management and tracking
- **Returns**: Return label creation and management
- **Claims**: Insurance claim filing and tracking
- **Wallet**: Financial account management
- **User Management**: Team member administration
- **Settings**: Profile and organization configuration
- **Reports**: Financial and operational reporting
- **Support**: Help documentation and contact

### UI Components

- **Radix UI**: Accessible component primitives
- **Tailwind CSS**: Utility-first styling
- **React Hook Form**: Form management
- **TanStack Table**: Data table components
- **Redux Toolkit**: State management
- **NextAuth**: Authentication integration

## Integrations

### EasyPost

- **Purpose**: Primary shipping label provider
- **Features**: Rate shopping, label generation, tracking
- **Carriers**: USPS, FedEx, DHL with custom accounts
- **Services**: Shipments, returns, claims, refunds

### E-commerce Platforms

- **ShipStation**: Inventory and order management integration
- **Shopify**: Store connection and order import
- **WooCommerce**: WordPress e-commerce integration
- **Amazon**: Marketplace order processing
- **Etsy**: Handmade goods platform
- **eBay**: Auction and fixed-price sales
- **TikTok**: Social commerce integration

### Payment Processing

- **Stripe**: Credit card processing
- **ACH**: Automated clearing house transfers
- **Wire Transfers**: Bank-to-bank transfers

## Current Implementation Status

### ✅ Completed Features

- User authentication and organization management
- Order creation and management system
- Shipping label generation through EasyPost
- Wallet system with multiple payment methods
- Address management and validation
- Basic claims filing system
- Returns processing
- Batch order processing
- User permission system
- Multi-store integration framework

### 🔄 In Progress

- Advanced reporting and analytics
- Enhanced claims processing workflow
- Bulk operations and automation
- Advanced filtering and search capabilities

### 🚧 Planned Features

- Super admin dashboard for Dropright staff
- Advanced analytics and business intelligence
- Enhanced automation and workflow management
- Mobile application
- API for third-party integrations
- Advanced fraud detection and prevention

## Technical Stack

### Backend

- **Runtime**: Node.js 18+
- **Framework**: NestJS 11 with Fastify
- **Database**: PostgreSQL with Prisma 6.5
- **Authentication**: JWT with custom guards
- **File Storage**: AWS S3 integration
- **Email**: Transactional email templates
- **Validation**: Class-validator with DTOs

### Frontend

- **Framework**: Next.js 15 with App Router
- **UI Library**: React 19 with TypeScript
- **Styling**: Tailwind CSS 4 with custom components
- **State Management**: Redux Toolkit with RTK Query
- **Forms**: React Hook Form with Zod validation
- **Authentication**: NextAuth.js 5

### Development Tools

- **Package Manager**: pnpm with workspace support
- **Build Tool**: Turbo for monorepo management
- **Linting**: ESLint with custom configurations
- **Formatting**: Prettier
- **Testing**: Jest for backend, built-in Next.js testing

## Security Features

### Authentication & Authorization

- JWT-based authentication
- Role-based access control
- Organization-level data isolation
- Session management with configurable expiration

### Data Protection

- Input validation and sanitization
- SQL injection prevention through Prisma
- XSS protection through React
- CSRF protection with NextAuth

### Financial Security

- Stripe integration for secure payments
- Encrypted sensitive data storage
- Audit trails for all financial transactions
- Multi-factor authentication support (planned)

## Deployment & Infrastructure

### Environment

- **Development**: Local development with Docker Compose
- **Production**: Cloud deployment (AWS/GCP/Azure)
- **Database**: PostgreSQL with connection pooling
- **File Storage**: AWS S3 for document storage
- **CDN**: Static asset delivery optimization

### Monitoring & Logging

- Structured logging with Pino
- Error tracking and monitoring
- Performance metrics collection
- User activity analytics

## Business Workflow

### 1. User Onboarding

1. Organization registration
2. User account creation
3. Store platform connections
4. Address book setup
5. Wallet funding

### 2. Order Processing

1. Order import from connected platforms
2. Manual order creation (if needed)
3. Address validation and parcel configuration
4. Rate shopping across carriers
5. Label purchase and printing

### 3. Shipping Operations

1. Label generation and printing
2. Package pickup or drop-off
3. Real-time tracking updates
4. Delivery confirmation
5. Customer communication

### 4. Financial Management

1. Wallet balance monitoring
2. Automatic recharge triggers
3. Payment processing and reconciliation
4. Transaction reporting and analytics

## Competitive Advantages

### Pricing

- Direct carrier relationships providing better rates
- Volume-based pricing optimization
- Transparent fee structure
- No hidden markups

### Integration

- Seamless connection with major e-commerce platforms
- ShipStation compatibility for existing users
- API-first architecture for custom integrations
- Real-time data synchronization

### User Experience

- Intuitive dashboard and workflow
- Batch processing capabilities
- Comprehensive reporting and analytics
- Mobile-responsive design

### Support

- Dedicated customer support
- Comprehensive documentation and tutorials
- Training and onboarding assistance
- Community and knowledge base

## Future Roadmap

### Phase 1: Core Platform Enhancement

- Advanced analytics dashboard
- Enhanced automation workflows
- Mobile application development
- API marketplace for third-party integrations

### Phase 2: Business Intelligence

- Predictive analytics for shipping optimization
- Cost analysis and optimization tools
- Customer behavior insights
- Market trend analysis

### Phase 3: Enterprise Features

- Multi-warehouse management
- Advanced inventory integration
- Custom workflow automation
- White-label solutions

### Phase 4: Global Expansion

- International shipping optimization
- Multi-currency support
- Global carrier partnerships
- Localized compliance and regulations

## Conclusion

Dropright represents a modern, comprehensive shipping management solution that leverages technology to provide better pricing and user experience than traditional shipping aggregators. The platform's architecture supports scalability, security, and extensibility while maintaining a focus on user experience and operational efficiency.

The application successfully bridges the gap between e-commerce platforms and shipping carriers, providing businesses with the tools they need to optimize their shipping operations while reducing costs through better carrier relationships and pricing.
