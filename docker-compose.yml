services:
  api:
    container_name: api
    build:
      context: .
      dockerfile: apps/api/Dockerfile
      platforms:
        - linux/amd64
    image: graphiostudio/deployment:dropright-api
    ports:
      - "3001:3001"
    env_file:
      - apps/api/.env.production
    restart: unless-stopped
    networks:
      - app-network
    dns:
      - *******
    sysctls:
      - net.ipv6.conf.all.disable_ipv6=1

  web:
    container_name: web
    build:
      context: .
      dockerfile: apps/web/Dockerfile
      platforms:
        - linux/amd64
    image: graphiostudio/deployment:dropright-web
    ports:
      - "3000:3000"
    env_file:
      - apps/web/.env.production
    restart: unless-stopped
    depends_on:
      - api
    networks:
      - app-network

networks:
  app-network:
    driver: bridge
