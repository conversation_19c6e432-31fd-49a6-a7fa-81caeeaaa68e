# # Option 2: Skip turbo prune, copy everything properly
# FROM node:20-alpine

# RUN apk update && apk add --no-cache libc6-compat
# WORKDIR /app
# RUN npm install -g pnpm turbo@^2

# # Copy package.json files first for better caching
# COPY package.json pnpm-lock.yaml pnpm-workspace.yaml turbo.json ./
# COPY apps/api/package.json ./apps/api/
# COPY packages ./packages

# # Install dependencies
# RUN pnpm install

# # Copy all source code
# COPY . .

# # Generate Prisma client
# RUN if [ -f "packages/database/prisma/schema.prisma" ]; then \
#       cd packages/database && npx prisma generate; \
#     fi

# # Build the application (this will build all dependencies too)
# RUN pnpm turbo run build --filter=api

# EXPOSE 3001

# CMD ["node", "./apps/api/dist/main.js"]

# Stage 1
FROM node:20-alpine AS base

# Stage 2
FROM base AS builder
RUN apk update
RUN apk add --no-cache libc6-compat
# Set working directory
WORKDIR /app

RUN npm install -g pnpm
RUN npm install -g turbo@^2
COPY . .

RUN turbo prune api --docker

# Stage 3
FROM base AS installer
RUN apk update
RUN apk add --no-cache libc6-compat
WORKDIR /app

RUN npm install -g pnpm

COPY --from=builder /app/out/json/ .
COPY --from=builder /app/apps/api/.env.production ./apps/api/.env.production
RUN pnpm install 

COPY --from=builder /app/out/full/ .

# Generate Prisma client
RUN if [ -f "packages/database/prisma/schema.prisma" ]; then \
      cd packages/database && npx prisma generate; \
    fi

RUN pnpm turbo run build --filter=api

# Stage 4
FROM base AS runner
WORKDIR /app

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nestjs
RUN apk add --no-cache curl

COPY --from=installer --chown=nestjs:nodejs /app/node_modules ./node_modules
COPY --from=installer --chown=nestjs:nodejs /app/apps/api/node_modules ./apps/api/node_modules
COPY --from=installer --chown=nestjs:nodejs /app/packages ./packages
COPY --from=installer --chown=nestjs:nodejs /app/apps/api/dist ./apps/api/dist
COPY --from=installer --chown=nestjs:nodejs /app/apps/api/.env.production ./.env.production
COPY --from=installer --chown=nestjs:nodejs /app/package.json ./package.json



USER nestjs

EXPOSE 3001


# HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
#   CMD curl -f http://localhost:3001/health || exit 1

CMD ["node", "./apps/api/dist/main.js"]

