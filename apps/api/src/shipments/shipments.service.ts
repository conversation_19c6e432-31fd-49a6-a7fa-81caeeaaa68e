import {
  BadRequestException,
  HttpException,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import {
  CounterType,
  OrderStatus,
  Prisma,
  ShipmentStatus,
  WalletTransactionCategory,
  WalletTransactionType,
} from '@repo/database';
import { format } from 'date-fns';
import { Decimal } from 'node_modules/@repo/database/generated/prisma/runtime/library';
import { PrismaTransaction } from 'src/common/types/prisma.type';
import { CreateTransactionProps } from 'src/finance/wallet/types/create-transaction.interface';
import { WalletTransactionService } from 'src/finance/wallet/wallet-transaction.service';
import { EasypostService } from 'src/infrastructure/easypost/easypost.service';
import { PrismaService } from 'src/infrastructure/prisma/prisma.service';
import { RatesService } from 'src/rates/rates.service';
import { ObjectTransformer } from 'src/utils/object-transformer.util';
import { filterBuilder } from 'src/utils/queryTools.util';
import { BuyNewLabelDto } from './dto/buy-new-label.dto';
import { BuyShipmentDto } from './dto/buy-shipment.dto';
import { CreateShipmentDto } from './dto/create-shipment.dto';
import { ShipmentQueryDto } from './dto/shipment-query.dto';

@Injectable()
export class ShipmentsService {
  private readonly logger = new Logger(ShipmentsService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly easypostService: EasypostService,
    private readonly ratesService: RatesService,
    private readonly walletTransactionService: WalletTransactionService,
  ) {}

  async createShipment(
    tx: PrismaTransaction,
    organizationId: string,
    createShipmentDto: CreateShipmentDto,
  ) {
    try {
      const shipmentNo = await this.generateShipmentNo(tx, organizationId);
      const shipment = await tx.shipment.create({
        data: {
          ...createShipmentDto,
          shipmentNo,
          organizationId,
          // Convert to JSON objects
          toAddress: JSON.parse(JSON.stringify(createShipmentDto.toAddress)),
          returnAddress: JSON.parse(
            JSON.stringify(createShipmentDto.returnAddress),
          ),
          parcel: JSON.parse(JSON.stringify(createShipmentDto.parcel)),
          postageLabel: JSON.parse(
            JSON.stringify(createShipmentDto.postageLabel),
          ),
        },
      });

      return shipment;
    } catch (error) {
      this.logger.error(`Could not create shipment ${error}`);
      throw new InternalServerErrorException(error);
    }
  }

  // ✅
  async findAll(organizationId: string, query: ShipmentQueryDto) {
    try {
      const { limit, offset, filter, search } = query;

      // Quick search filter
      const allowedQuickSearchFields = [
        'shipmentNo',
        'trackingCode',
        'order.orderNo',
      ];
      const addressAllowedJsonFields = [
        'name',
        'company',
        'email',
        'street1',
        'city',
        'state',
        'country',
        'zip',
      ];
      // Advance filter
      const fuzzyFields = [
        'shipmentNo',
        'order.orderNo',
        'order.storeId',
        'trackingCode',
      ];
      const jsonFields = ['toAddress.name'];

      const modifiedFilter = filterBuilder(filter ?? {}, {
        fuzzyFields,
        jsonFields,
      });

      const where: Prisma.ShipmentWhereInput = {
        organizationId,
        ...modifiedFilter,
      };

      if (search) {
        const searchConditions: Prisma.ShipmentWhereInput['OR'] = [];

        allowedQuickSearchFields.forEach((field) => {
          if (field === 'order.orderNo') {
            searchConditions.push({
              order: { orderNo: { contains: search, mode: 'insensitive' } },
            });
          } else {
            searchConditions.push({
              [field]: { contains: search, mode: 'insensitive' },
            });
          }
        });

        // Search in JSON fields - each field needs its own condition
        addressAllowedJsonFields.forEach((jsonField) => {
          searchConditions.push({
            toAddress: {
              path: [jsonField],
              string_contains: search,
              mode: 'insensitive',
            },
          });
        });
        where.OR = searchConditions;
      }

      const [data, count] = await Promise.all([
        this.prisma.client.shipment.findMany({
          where,
          orderBy: [{ createdAt: 'desc' }, { id: 'desc' }],
          take: limit,
          skip: offset ?? undefined,
          include: {
            order: { select: { orderNo: true } },
            fromAddress: true,
          },
        }),
        this.prisma.client.shipment.count({ where }),
      ]);
      return {
        data,
        pagination: {
          total: count,
          limit,
          offset: offset ?? 0,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to retrieve shipments: ${error.message}`);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to retrieve shipments');
    }
  }

  // ✅
  async findOne(organizationId: string, id: string) {
    try {
      const shipment = await this.prisma.client.shipment.findUnique({
        where: { id, organizationId },
        include: {
          order: {
            select: {
              orderNo: true,
              orderItems: { include: { product: true } },
              notes: true,
            },
          },
          fromAddress: true,
          labelRefunds: true,
        },
      });

      if (!shipment) {
        throw new NotFoundException(`Shipment with ID ${id} not found`);
      }

      return shipment;
    } catch (error) {
      this.logger.error(`Failed to retrieve shipment: ${error.message}`);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to retrieve shipment');
    }
  }

  // ✅
  async findEasypostShipment(id: string) {
    try {
      const epShipment = await this.easypostService.getShipment(id);
      return ObjectTransformer.snakeToCamel(epShipment);
    } catch (error) {
      this.logger.error(`Could not find shipment ${error}`);
      throw new InternalServerErrorException(error);
    }
  }

  // ✅
  async buyShipment(organizationId: string, buyShipmentDto: BuyShipmentDto) {
    const { epShipmentId, rateId, orderId, insuranceAmount } = buyShipmentDto;

    try {
      // 1. Buy shipment from EasyPost
      const epShipment = await this.easypostService.buyShipment(
        epShipmentId,
        rateId,
        insuranceAmount,
      );

      // 2. Update order and create shipment in database
      const { order, shipment } = await this.prisma.client.$transaction(
        async (tx) => {
          const order = await tx.order.update({
            where: { id: orderId, organizationId },
            data: {
              epShipmentId: epShipment.id,
              status: OrderStatus.processing,
              carrier: epShipment.selected_rate.carrier,
              service: epShipment.selected_rate.service,
            },
            include: { organization: { select: { walletId: true } } },
          });

          // Transform rate for pricing
          const transformedRate =
            await this.ratesService.getPricingAndTransformRate(
              organizationId,
              epShipment.selected_rate,
            );

          // Create shipment record
          const shipment = await this.createShipment(tx, organizationId, {
            orderId,
            epShipmentId: epShipment.id,
            fromAddressId: order.fromAddressId,
            status: epShipment.status as ShipmentStatus,
            toAddress: ObjectTransformer.snakeToCamel(epShipment.to_address),
            returnAddress: ObjectTransformer.snakeToCamel(
              epShipment.return_address,
            ),
            parcel: ObjectTransformer.snakeToCamel(epShipment.parcel),
            carrier: epShipment.selected_rate.carrier,
            carrierAccountId: epShipment.selected_rate.carrier_account_id,
            service: epShipment.selected_rate.service,
            rate: transformedRate.rate,
            trackingCode: epShipment.tracking_code,
            shipDate: epShipment.options?.label_date ?? undefined,
            postageLabel: {
              labelUrl: epShipment.postage_label.label_url,
              labelPdfUrl: epShipment.postage_label.label_pdf_url,
              labelZplUrl: epShipment.postage_label.label_zpl_url,
            },
            insurance: epShipment.insurance as unknown as string, // inconsistent type
            fees: ObjectTransformer.snakeToCamel(epShipment.fees),
          });

          // Create wallet transaction
          await this.walletTransactionService.createTransaction({
            walletId: order.organization.walletId,
            type: WalletTransactionType.label_purchase,
            category: WalletTransactionCategory.settled,
            amount: new Decimal(transformedRate.rate),
            referenceType: 'label',
            referenceId: shipment.shipmentNo,
            description: `Purchased ${transformedRate.carrier} label for order ${order.orderNo}`,
          });

          return { order, shipment };
        },
      );

      return { order, shipment };
    } catch (error) {
      this.logger.error(
        `Could not complete shipment purchase: ${error.message || error}`,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      throw new InternalServerErrorException(
        'Failed to process shipment purchase',
        error.message,
      );
    }
  }

  // ❌ TODO: Maybe don't need because shipment now has labelUrls
  // async downloadLabel(epShipmentId: string) {
  //   const epShipment = await this.easypostService.getShipment(epShipmentId);
  //   const labelUrl = epShipment.postage_label.label_url;

  //   if (!labelUrl)
  //     throw new NotFoundException(
  //       `Label not found for shipment ${epShipmentId}`,
  //     );

  //   try {
  //     const response = await fetch(labelUrl);
  //     if (!response.ok || !response.body) {
  //       throw new Error('Failed to fetch label from EasyPost');
  //     }

  //     const buffer = Buffer.from(await response.arrayBuffer());

  //     return new StreamableFile(buffer, {
  //       type: 'image/png',
  //       disposition: `attachment; filename="label-${epShipmentId}.png"`,
  //     });
  //   } catch (error) {
  //     this.logger.error(`Failed to download label: ${error}`);
  //     throw new InternalServerErrorException('Could not download label');
  //   }
  // }

  // ✅
  async convertShipmentLabel(
    organizationId: string,
    id: string,
    format: 'PDF' | 'ZPL',
  ) {
    try {
      const shipment = await this.findOne(organizationId, id);

      const updatedEpShipment = await this.easypostService.convertShipmentLabel(
        shipment.epShipmentId,
        format,
      );

      const updatedShipment = await this.prisma.client.shipment.update({
        where: { organizationId, id },
        data: {
          postageLabel: {
            labelUrl: updatedEpShipment.postage_label.label_url,
            labelPdfUrl: updatedEpShipment.postage_label.label_pdf_url,
            labelZplUrl: updatedEpShipment.postage_label.label_zpl_url,
          },
        },
      });

      return updatedShipment;
    } catch (error) {
      // Prisma error
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2025') {
          throw new NotFoundException(`Shipment with ID ${id} not found`);
        }
      }
      this.logger.error(`Could not convert shipment label ${error}`);
      throw new InternalServerErrorException(error);
    }
  }

  async refundShipment(organizationId: string, id: string) {
    try {
      // Check if shipment exists
      const shipment = await this.prisma.client.shipment.findUnique({
        where: { id, organizationId },
      });
      if (!shipment) {
        throw new NotFoundException('Shipment not found');
      }
      // Create Easypost refund
      const epRefund = await this.easypostService.createRefund({
        carrier: shipment.carrier,
        trackingCodes: [shipment.trackingCode],
      });
      if (epRefund.length === 0) {
        throw new InternalServerErrorException(
          'Failed to create refund with Easypost',
        );
      }
      //TODO: Update shipment status in $transaction
      const voidedDate = new Date();
      const [updatedShipment, createdRefund] =
        await this.prisma.client.$transaction([
          this.prisma.client.shipment.update({
            where: { id, organizationId },
            data: { status: ShipmentStatus.voided, voidedAt: voidedDate },
          }),
          this.prisma.client.labelRefund.create({
            data: {
              createdAt: voidedDate,
              organizationId,
              shipmentId: shipment.id,
              epRefundId: epRefund[0].id,
              epShipmentId: shipment.epShipmentId,
              trackingCode: epRefund[0].tracking_code,
              status: epRefund[0].status,
              confirmationNumber: epRefund[0].confirmation_number,
              carrier: epRefund[0].carrier,
              carrierAccountId: shipment.carrierAccountId,
              service: shipment.service,
              rate: shipment.rate,
            },
          }),
        ]);
      console.log(updatedShipment);
      return { createdRefund };
    } catch (error) {
      this.logger.error(`Could not refund shipment ${error}`);
      if (error instanceof HttpException) {
        throw error;
      }
      // Easypost error codes
      if (error.code === 'SHIPMENT.REFUND.UNAVAILABLE') {
        throw new BadRequestException(error.message);
      }
      throw new InternalServerErrorException('Failed to refund shipment');
    }
  }

  async buyNewLabel(
    organizationId: string,
    id: string,
    buyNewLabelDto: BuyNewLabelDto,
  ) {
    const {
      epShipmentId,
      selectedRateId,
      insuranceAmount,
      orderId,
      fromAddressId,
    } = buyNewLabelDto;
    try {
      // 1. Buy new label from EasyPost
      const epShipment = await this.easypostService.buyShipment(
        epShipmentId,
        selectedRateId,
        insuranceAmount,
      );

      // 2. Create shipment and update order in database
      const { order, shipment } = await this.prisma.client.$transaction(
        async (tx) => {
          // Transform rate for pricing
          const transformedRate =
            await this.ratesService.getPricingAndTransformRate(
              organizationId,
              epShipment.selected_rate,
            );

          // Create replacement shipment
          const shipment = await this.createShipment(tx, organizationId, {
            orderId,
            epShipmentId: epShipment.id,
            fromAddressId,
            toAddress: ObjectTransformer.snakeToCamel(epShipment.to_address),
            returnAddress: ObjectTransformer.snakeToCamel(
              epShipment.return_address,
            ),
            parcel: ObjectTransformer.snakeToCamel(epShipment.parcel),
            carrier: epShipment.selected_rate.carrier,
            carrierAccountId: epShipment.selected_rate.carrier_account_id,
            service: epShipment.selected_rate.service,
            rate: transformedRate.rate,
            trackingCode: epShipment.tracking_code,
            postageLabel: {
              labelUrl: epShipment.postage_label.label_url,
              labelPdfUrl: epShipment.postage_label.label_pdf_url,
              labelZplUrl: epShipment.postage_label.label_zpl_url,
            },
            insurance: epShipment.insurance as unknown as string, // inconsistent type
            fees: ObjectTransformer.snakeToCamel(epShipment.fees),
            replacementForId: id,
          });

          // Update order with new shipment
          const order = await tx.order.update({
            where: { organizationId, id: orderId },
            data: {
              epShipmentId: epShipment.id,
              status: OrderStatus.processing,
              carrier: epShipment.selected_rate.carrier,
              service: epShipment.selected_rate.service,
            },
            include: {
              organization: { select: { walletId: true } },
            },
          });

          // Create wallet transaction
          await this.walletTransactionService.createTransaction({
            walletId: order.organization.walletId,
            type: WalletTransactionType.label_purchase,
            category: WalletTransactionCategory.settled,
            amount: new Decimal(transformedRate.rate),
            referenceType: 'label',
            referenceId: shipment.shipmentNo,
            description: `Purchased replacement ${transformedRate.carrier} label for order ${order.orderNo}`,
          });

          return { order, shipment };
        },
      );

      return { order, shipment };
    } catch (error) {
      this.logger.error(`Could not buy new label: ${error.message || error}`);

      if (error instanceof HttpException) {
        throw error;
      }

      throw new InternalServerErrorException(
        'Failed to purchase new label',
        error.message,
      );
    }
  }

  // ========== INTERNAL FUNCTIONS ==========
  async generateShipmentNo(
    tx: Prisma.TransactionClient,
    organizationId: string,
  ) {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const dateStr = format(today, 'MMddyyyy');
    const prefix = `SHP-${dateStr}-`;

    const counterRecord = await tx.counter.upsert({
      where: {
        organizationId_type_date: {
          organizationId,
          type: CounterType.shipment,
          date: today,
        },
      },
      update: { count: { increment: 1 } },
      create: {
        organizationId,
        type: CounterType.shipment,
        date: today,
        count: 1,
      },
    });

    const sequenceStr = counterRecord.count.toString().padStart(5, '0');
    return prefix + sequenceStr;
  }
}
