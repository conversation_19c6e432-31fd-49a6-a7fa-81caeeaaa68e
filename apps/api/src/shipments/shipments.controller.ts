import { Body, Controller, Get, Param, Post, Query } from '@nestjs/common';
import { CurrentUser } from 'src/common/decorators/user.decorator';
import { BuyShipmentDto } from './dto/buy-shipment.dto';
import { ConvertShipmentLabelDto } from './dto/convert-shipment-label.dto';
import { ShipmentQueryDto } from './dto/shipment-query.dto';
import { ShipmentsService } from './shipments.service';
import { BuyNewLabelDto } from './dto/buy-new-label.dto';

@Controller('shipments')
export class ShipmentsController {
  constructor(private readonly shipmentsService: ShipmentsService) {}

  // ✅
  @Get()
  findAll(
    @CurrentUser('organizationId') organizationId: string,
    @Query() query: ShipmentQueryDto,
  ) {
    return this.shipmentsService.findAll(organizationId, query);
  }

  // ✅
  @Post('buy')
  buyShipment(
    @CurrentUser('organizationId') organizationId: string,
    @Body() buyShipmentDto: BuyShipmentDto,
  ) {
    return this.shipmentsService.buyShipment(organizationId, buyShipmentDto);
  }

  // ✅ Easypost shipment
  @Get('easypost/:epShipmentId')
  findEasypostShipment(@Param('epShipmentId') epShipmentId: string) {
    return this.shipmentsService.findEasypostShipment(epShipmentId);
  }

  // ✅ Easypost convert label format
  @Post(':id/convert-label')
  convertShipmentLabel(
    @CurrentUser('organizationId') organizationId: string,
    @Param('id') id: string,
    @Body() convertShipmentLabelDto: ConvertShipmentLabelDto,
  ) {
    return this.shipmentsService.convertShipmentLabel(
      organizationId,
      id,
      convertShipmentLabelDto.format,
    );
  }

  @Post(':id/refund')
  refundShipment(
    @CurrentUser('organizationId') organizationId: string,
    @Param('id') id: string,
  ) {
    return this.shipmentsService.refundShipment(organizationId, id);
  }

  @Post(':id/buy-new-label')
  buyNewLabel(
    @CurrentUser('organizationId') organizationId: string,
    @Param('id') id: string,
    @Body() buyNewLabelDto: BuyNewLabelDto,
  ) {
    return this.shipmentsService.buyNewLabel(
      organizationId,
      id,
      buyNewLabelDto,
    );
  }

  // ✅
  @Get(':id')
  findOne(
    @CurrentUser('organizationId') organizationId: string,
    @Param('id') id: string,
  ) {
    return this.shipmentsService.findOne(organizationId, id);
  }

  // ❌ TODO: Maybe don't need because shipment now has labelUrls
  // @Public()
  // @Get(':epShipmentId/download-label')
  // async downloadLabel(@Param('epShipmentId') epShipmentId: string) {
  //   return await this.shipmentsService.downloadLabel(epShipmentId);
  // }
}
