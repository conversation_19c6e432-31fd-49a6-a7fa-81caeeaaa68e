import { ShipmentStatus } from '@repo/database';
import { Type } from 'class-transformer';
import {
  IsEnum,
  IsNotEmpty,
  IsObject,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { AddressDto } from 'src/common/dto/address.dto';
import { ParcelDto } from 'src/common/dto/parcel.dto';
import { PostageLabelDto } from 'src/common/dto/postage-label.dto';

export class CreateShipmentDto {
  @IsString()
  @IsNotEmpty()
  orderId: string;

  @IsString()
  @IsNotEmpty()
  epShipmentId: string;

  @IsEnum(ShipmentStatus)
  @IsOptional()
  status?: ShipmentStatus;

  @IsString()
  @IsNotEmpty()
  fromAddressId: string;

  @ValidateNested()
  @Type(() => AddressDto)
  toAddress: AddressDto;

  @ValidateNested()
  @Type(() => AddressDto)
  returnAddress: AddressDto;

  @ValidateNested()
  @Type(() => ParcelDto)
  parcel: ParcelDto;

  @IsString()
  @IsNotEmpty()
  carrier: string;

  @IsString()
  @IsNotEmpty()
  carrierAccountId: string;

  @IsString()
  @IsNotEmpty()
  service: string;

  @IsString()
  @IsNotEmpty()
  rate: string;

  @IsString()
  @IsOptional()
  labelPrinted?: Date;

  @IsString()
  @IsNotEmpty()
  trackingCode: string;

  @IsString()
  @IsOptional()
  shipDate?: string;

  @ValidateNested()
  @Type(() => PostageLabelDto)
  postageLabel: PostageLabelDto;

  @IsString()
  @IsOptional()
  insurance?: string;

  @IsObject()
  @IsNotEmpty()
  fees: object;

  @IsString()
  @IsOptional()
  replacementForId?: string;
}
