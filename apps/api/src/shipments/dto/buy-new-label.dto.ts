import { ShipmentStatus } from '@repo/database';
import {
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';

export class BuyNewLabelDto {
  @IsString()
  @IsNotEmpty()
  epShipmentId: string;

  @IsString()
  @IsNotEmpty()
  selectedRateId: string;

  @IsString()
  @IsNotEmpty()
  orderId: string;

  @IsString()
  @IsNotEmpty()
  fromAddressId: string;

  @IsNumber()
  @IsOptional()
  insuranceAmount?: number;

  @IsEnum(ShipmentStatus)
  @IsOptional()
  status?: ShipmentStatus;
}
