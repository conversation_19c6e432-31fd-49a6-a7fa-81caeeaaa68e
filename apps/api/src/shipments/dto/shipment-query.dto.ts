import { ShipmentStatus } from '@repo/database';
import { Type } from 'class-transformer';
import {
  IsEnum,
  IsObject,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { PaginationQueryDto } from 'src/common/dto/pagination-query.dto';

class ShipmentFilterDto {
  @IsEnum(ShipmentStatus)
  @IsOptional()
  status?: ShipmentStatus;

  @IsString()
  @IsOptional()
  shipmentNo?: string;

  @IsObject()
  @IsOptional()
  order?: {
    orderNo?: string;
    storeId?: string;
  };

  @IsString()
  @IsOptional()
  trackingCode?: string;

  @IsObject()
  @IsOptional()
  toAddress?: {
    name?: string;
  };
}

export class ShipmentQueryDto extends PaginationQueryDto {
  @ValidateNested()
  @Type(() => ShipmentFilterDto)
  @IsOptional()
  filter?: ShipmentFilterDto;

  @IsString()
  @IsOptional()
  search?: string;
}
