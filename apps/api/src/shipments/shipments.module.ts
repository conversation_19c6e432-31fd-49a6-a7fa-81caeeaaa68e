import { Module } from '@nestjs/common';
import { ShipmentsService } from './shipments.service';
import { ShipmentsController } from './shipments.controller';
import { EasypostModule } from 'src/infrastructure/easypost/easypost.module';
import { RatesModule } from 'src/rates/rates.module';
import { WalletModule } from 'src/finance/wallet/wallet.module';

@Module({
  imports: [EasypostModule, RatesModule, WalletModule],
  controllers: [ShipmentsController],
  providers: [ShipmentsService],
  exports: [ShipmentsService],
})
export class ShipmentsModule {}
