import { Module } from '@nestjs/common';
import { StripeWebhookController } from './stripe/stripe-webhook.controller';
import { StripeWebhookService } from './stripe/stripe-webhook.service';
import { PaymentMethodsModule } from 'src/finance/payment-methods/payment-methods.module';
import { StripeModule } from 'src/infrastructure/stripe/stripe.module';

@Module({
  controllers: [StripeWebhookController],
  providers: [StripeWebhookService],
  imports: [PaymentMethodsModule, StripeModule],
})
export class WebhookModule {}
