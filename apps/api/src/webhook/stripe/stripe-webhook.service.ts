import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { CreditCardBrand, PaymentMethodStatus } from '@repo/database';
import { PrismaService } from 'src/infrastructure/prisma/prisma.service';
import { StripeService } from 'src/infrastructure/stripe/stripe.service';
import { PaymentMethodsService } from 'src/finance/payment-methods/payment-methods.service';
import Stripe from 'stripe';

type WebhookHandler = (event: Stripe.Event) => Promise<void>;

@Injectable()
export class StripeWebhookService {
  private readonly logger = new Logger(StripeWebhookService.name);

  private readonly handlers: Record<string, WebhookHandler> = {
    'setup_intent.succeeded': this.handleSetupIntentSucceeded.bind(this),
    // Future events
    // 'payment_intent.succeeded': this.handlePaymentIntentSucceeded.bind(this),
    // 'customer.subscription.deleted': this.handleSubscriptionDeleted.bind(this),
  };

  constructor(
    private readonly paymentMethodsService: PaymentMethodsService,
    private readonly stripeService: StripeService,
    private readonly prisma: PrismaService,
  ) {}

  async handleWebhook(payload: Buffer<ArrayBufferLike>, signature: string) {
    let event: Stripe.Event;
    try {
      event = this.stripeService.verifyWebhookSignature(payload, signature);
    } catch (error) {
      this.logger.error('Webhook signature verification failed', error);
      throw new BadRequestException('Webhook signature verification failed');
    }

    const handler = this.handlers[event.type];
    if (!handler) {
      this.logger.warn(`Unhandled webhook event type: ${event.type}`);
      return { success: true };
    }

    try {
      await handler(event);
      return { success: true };
    } catch (error) {
      this.handleError(error, event);
    }
  }

  private async handleSetupIntentSucceeded(event: Stripe.Event) {
    const setupIntent = event.data.object as Stripe.SetupIntent;
    this.logger.log(`Processing setup_intent.succeeded: ${setupIntent.id}`);

    const paymentMethodId = this.extractPaymentMethodId(
      setupIntent.payment_method,
    );
    if (!paymentMethodId) return this.skip(`No payment method found`);

    const stripePaymentMethod =
      await this.stripeService.getPaymentMethod(paymentMethodId);
    if (!stripePaymentMethod.card) return this.skip(`No card info found`);

    const stripeCustomerId = this.extractCustomerId(
      stripePaymentMethod.customer,
    );
    if (!stripeCustomerId) return this.skip(`No customer ID found`);

    // This will return existing non-expired cards with same fingerprint
    const organization = await this.getOrganizationExistingCreditCards(
      stripeCustomerId,
      stripePaymentMethod.card.fingerprint || undefined,
      paymentMethodId,
    );

    if (!organization)
      return this.skip(`No organization found for ${stripeCustomerId}`);
    if (organization.paymentMethods.length > 0) {
      await this.stripeService.detachPaymentMethod(paymentMethodId);
      this.logger.log(
        `Detached payment method ${paymentMethodId} from Stripe for ${organization.name}`,
      );
      return this.skip(
        `The same card already exists for org ${organization.id}`,
      );
    }

    try {
      const paymentMethod = await this.paymentMethodsService.createCreditCard({
        organizationId: organization.id,
        stripePaymentMethodId: paymentMethodId,
        cardFingerprint: stripePaymentMethod.card.fingerprint || undefined,
        cardLast4: stripePaymentMethod.card.last4,
        cardBrand: stripePaymentMethod.card.brand as CreditCardBrand,
        cardExpMonth: stripePaymentMethod.card.exp_month,
        cardExpYear: stripePaymentMethod.card.exp_year,
        isDefault: false,
        verifiedBy: 'stripe',
        verifiedAt: new Date(event.created * 1000),
      });

      this.logger.log(`Payment method saved: ${paymentMethod.id}`);
    } catch (error) {
      this.logger.error(
        `Failed to save payment method in DB for ${paymentMethodId}, rolling back Stripe...`,
        error,
      );
      try {
        await this.stripeService.detachPaymentMethod(paymentMethodId);
        this.logger.log(
          `Rolled back Stripe payment method: ${paymentMethodId}`,
        );
      } catch (rollbackError) {
        this.logger.error(
          `Rollback failed for Stripe payment method ${paymentMethodId}`,
          rollbackError,
        );
      }
      throw new InternalServerErrorException(
        'Failed to persist payment method',
      );
    }
  }

  // --- Domain helpers ---
  private async getOrganizationExistingCreditCards(
    customerId: string,
    fingerprint?: string,
    paymentMethodId?: string,
  ) {
    // Handle different duplicate detection strategies
    let paymentMethodsWhere: {
      cardFingerprint?: string;
      stripePaymentMethodId?: string;
      id?: string;
    };
    if (fingerprint) {
      // Primary: Use fingerprint for duplicate detection
      paymentMethodsWhere = { cardFingerprint: fingerprint };
    } else if (paymentMethodId) {
      // Fallback: Use payment method ID (since each PM ID should be unique)
      paymentMethodsWhere = { stripePaymentMethodId: paymentMethodId };
    } else {
      // No reliable way to detect duplicates - skip the check
      paymentMethodsWhere = { id: 'impossible-id' }; // Will return empty array
    }

    return this.prisma.client.organization.findUnique({
      where: { stripeCustomerId: customerId },
      select: {
        id: true,
        name: true,
        paymentMethods: {
          where: {
            ...paymentMethodsWhere,
            status: {
              not: PaymentMethodStatus.expired, // Allow same card if previous is expired
            },
          },
        },
      },
    });
  }

  private extractPaymentMethodId(
    paymentMethod: string | Stripe.PaymentMethod | null,
  ): string | null {
    return paymentMethod && typeof paymentMethod !== 'string'
      ? paymentMethod.id
      : paymentMethod;
  }

  private extractCustomerId(
    customer: string | Stripe.Customer | Stripe.DeletedCustomer | null,
  ): string | null {
    return customer && typeof customer !== 'string' ? customer.id : customer;
  }

  private skip(reason: string) {
    this.logger.warn(reason);
  }

  private handleError(error: any, event: Stripe.Event) {
    this.logger.error(`Failed to process event ${event.type}`, error);
    if (this.isRetryableError(error)) {
      throw new InternalServerErrorException('Temporary error, please retry');
    }
    this.logger.error('Non-retryable error, marking as processed');
  }

  private isRetryableError(error: any): boolean {
    return (
      error.code === 'ECONNRESET' ||
      error.code === 'ETIMEDOUT' ||
      error.status >= 500 ||
      error.message?.includes('timeout') ||
      error.message?.includes('connection')
    );
  }
}
