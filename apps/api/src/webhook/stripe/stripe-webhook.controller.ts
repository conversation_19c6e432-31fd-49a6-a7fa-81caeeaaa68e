import {
  BadRequestException,
  Controller,
  HttpCode,
  HttpException,
  InternalServerErrorException,
  Logger,
  Post,
  RawBodyRequest,
  Req,
} from '@nestjs/common';
import { Public } from 'src/common/decorators/public.decorator';
import { StripeWebhookService } from './stripe-webhook.service';

@Public()
@Controller('webhook/stripe')
export class StripeWebhookController {
  private readonly logger = new Logger(StripeWebhookController.name);

  constructor(private readonly stripeService: StripeWebhookService) {}

  @Post()
  @HttpCode(200)
  async handleWebhook(@Req() req: RawBodyRequest<Request>) {
    try {
      const signature = req.headers['stripe-signature'];

      if (!signature) {
        throw new BadRequestException('Stripe signature is required');
      }

      const payload = req.rawBody;

      if (!payload) {
        throw new BadRequestException('Request body is required');
      }

      this.logger.log('Processing Stripe webhook');

      await this.stripeService.handleWebhook(payload, signature);
    } catch (error) {
      this.logger.error(
        `Error handling stripe webhook: ${error.message}`,
        error.stack,
      );
      if (error instanceof HttpException) throw error;
      throw new InternalServerErrorException('Error handling stripe webhook');
    }
  }
}
