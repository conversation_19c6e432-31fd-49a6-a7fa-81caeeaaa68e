import { Controller, Get } from '@nestjs/common';
import { CurrentUser } from 'src/common/decorators/user.decorator';
import { RefundsService } from './refunds.service';

@Controller('refunds')
export class RefundsController {
  constructor(private readonly refundsService: RefundsService) {}

  @Get()
  findAll(@CurrentUser('organizationId') organizationId: string) {
    return this.refundsService.findAll(organizationId);
  }

  // TODO: Create refund many
}
