import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { PrismaService } from 'src/infrastructure/prisma/prisma.service';
import { EasypostService } from 'src/infrastructure/easypost/easypost.service';

@Injectable()
export class RefundsService {
  private readonly logger = new Logger(RefundsService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly easypostService: EasypostService,
  ) {}

  async findAll(organizationId: string) {
    try {
      return await this.prisma.client.labelRefund.findMany({
        where: { organizationId },
      });
    } catch (error) {
      console.log(error);
      this.logger.error(`Failed to retrieve refunds: ${error.message}`);
      throw new InternalServerErrorException('Failed to retrieve refunds');
    }
  }
}
