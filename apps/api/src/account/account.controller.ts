import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  Patch,
  Put,
} from '@nestjs/common';
import { User } from '@repo/database';
import { File } from 'src/common/decorators/file.decorator';
import { CurrentUser } from 'src/common/decorators/user.decorator';
import { FilesToUpload } from 'src/common/types/upload-files';
import { AccountService } from './account.service';
import { UpdateBillingAddressDto } from './dto/update-billing-address.dto';
import { UpdateProfileInfoDto } from './dto/update-profile-info.dto';

@Controller('account')
export class AccountController {
  constructor(private readonly accountService: AccountService) {}

  @Get()
  getAccountData(@CurrentUser() user: User) {
    return this.accountService.getAccountData(user);
  }

  @Patch('profile/info')
  updateProfileInfo(
    @CurrentUser() user: User,
    @Body() updateProfileInfoDto: UpdateProfileInfoDto,
  ) {
    return this.accountService.updateProfile(user, updateProfileInfoDto);
  }

  @Patch('profile/image')
  updateProfileImage(@CurrentUser() user: User, @File() file: FilesToUpload) {
    if (!file) throw new BadRequestException('No image provided');
    return this.accountService.updateProfileImage(user, file);
  }

  @Delete('profile/image')
  removeProfileImage(@CurrentUser() user: User) {
    return this.accountService.removeProfileImage(user);
  }

  @Get('billing-address')
  getBillingAddress(@CurrentUser() user: User) {
    return this.accountService.getBillingAddress(user);
  }

  @Put('billing-address')
  updateBillingAddress(
    @CurrentUser('organizationId') organizationId: string,
    @Body() updateBillingAddressDto: UpdateBillingAddressDto,
  ) {
    return this.accountService.updateBillingAddress(
      organizationId,
      updateBillingAddressDto,
    );
  }

  @Get('dashboard/wallet')
  getDashboardData(@CurrentUser('organizationId') organizationId: string) {
    return this.accountService.getDashboardData(organizationId);
  }
}
