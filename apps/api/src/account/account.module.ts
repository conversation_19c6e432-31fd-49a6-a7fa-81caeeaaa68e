import { Module } from '@nestjs/common';
import { AccountService } from './account.service';
import { AccountController } from './account.controller';
import { StorageModule } from 'src/infrastructure/storage/storage.module';
import { ImageProcessingModule } from 'src/infrastructure/image-processing/image-processing.module';

@Module({
  imports: [StorageModule, ImageProcessingModule],
  controllers: [AccountController],
  providers: [AccountService],
})
export class AccountModule {}
