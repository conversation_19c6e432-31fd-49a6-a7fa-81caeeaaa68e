import {
  BadRequestException,
  HttpException,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import {
  PaymentMethodStatus,
  User,
  WalletTransactionCategory,
} from '@repo/database';
import { PrismaClientKnownRequestError } from 'node_modules/@repo/database/generated/prisma/runtime/library';
import { ImageProcessingOptions } from 'src/common/types/image-processing-options.interface';
import { FilesToUpload } from 'src/common/types/upload-files';
import { ImageProcessingService } from 'src/infrastructure/image-processing/image-processing.service';
import { PrismaService } from 'src/infrastructure/prisma/prisma.service';
import { StorageService } from 'src/infrastructure/storage/storage.service';
import { UpdateBillingAddressDto } from './dto/update-billing-address.dto';
import { UpdateProfileInfoDto } from './dto/update-profile-info.dto';

const SELECT_USER_FIELDS = {
  id: true,
  firstName: true,
  lastName: true,
  email: true,
  status: true,
  image: true,
  createdAt: true,
};

@Injectable()
export class AccountService {
  private readonly logger = new Logger(AccountService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly storageService: StorageService,
    private readonly imageProcessingService: ImageProcessingService,
  ) {}

  async getAccountData(user: User) {
    try {
      const result = await this.prisma.client.user.findUnique({
        where: { id: user.id },
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          status: true,
          image: true,
          createdAt: true,
          organization: {
            select: {
              id: true,
              name: true,
              phone: true,
              shipmentVolume: true,
              wallet: {
                select: {
                  id: true,
                  balance: true,
                  reserved: true,
                  currency: true,
                },
              },
            },
          },
          permissions: {
            select: {
              permissionId: true,
            },
          },
        },
      });

      if (!result)
        throw new NotFoundException('Could not find user account data');

      const wallet = result.organization.wallet;
      const availableBalance = wallet.balance.minus(wallet.reserved);

      return {
        ...result,
        organization: {
          ...result?.organization,
          wallet: {
            id: result?.organization.wallet.id,
            currency: result?.organization.wallet.currency,
            availableBalance,
          },
        },
        permissions: result.permissions.map(
          (permission) => permission.permissionId,
        ),
      };
    } catch (error) {
      this.logger.error(`Failed to get account data for user: ${user.id}`);

      if (error instanceof HttpException) throw error;
      // Prisma error
      if (error instanceof PrismaClientKnownRequestError) {
        switch (error.code) {
          case 'P2025':
            throw new NotFoundException('User not found');
          default:
            throw new InternalServerErrorException('Database error');
        }
      }
      throw new InternalServerErrorException('Failed to get account data');
    }
  }

  async updateProfile(user: User, updateProfileInfoDto: UpdateProfileInfoDto) {
    const { id } = user;

    if (
      !updateProfileInfoDto ||
      Object.keys(updateProfileInfoDto).length === 0
    ) {
      throw new BadRequestException('No fields provided to update');
    }

    try {
      return await this.prisma.client.user.update({
        where: { id },
        data: updateProfileInfoDto,
        select: SELECT_USER_FIELDS,
      });
    } catch (error) {
      this.logger.error(`Failed to update profile info for user: ${user.id}`);
      if (error instanceof HttpException) throw error;
      // Prisma error
      if (error instanceof PrismaClientKnownRequestError) {
        switch (error.code) {
          case 'P2025':
            throw new NotFoundException('User not found');
          default:
            throw new InternalServerErrorException('Database error');
        }
      }
      throw new InternalServerErrorException('Failed to update profile info');
    }
  }

  async updateProfileImage(user: User, file: FilesToUpload) {
    const { id } = user;

    const options: ImageProcessingOptions = {
      maxWidth: 200,
      maxHeight: 200,
      quality: 85,
      fit: 'cover',
      format: 'webp',
    };
    const keyPrefix = `users/${id}`;

    try {
      const currentUser = await this.prisma.client.user.findUnique({
        where: { id },
        select: { image: true },
      });

      if (!currentUser) throw new NotFoundException('User not found');

      const processedFiles = await this.imageProcessingService.compressImages(
        [file],
        options,
      );
      const response = await this.storageService.uploadFiles(
        processedFiles,
        keyPrefix,
      );

      if (response.length !== 1) {
        throw new InternalServerErrorException('Failed to upload image');
      }

      const newImageUrl = response[0].url;
      const newImageKey = response[0].key;

      try {
        const updatedUser = await this.prisma.client.user.update({
          where: { id },
          data: { image: newImageUrl },
          select: SELECT_USER_FIELDS,
        });

        if (currentUser.image) {
          const oldImageKey = this.storageService.extractKeyFromUrl(
            currentUser.image,
          );
          if (oldImageKey) {
            this.storageService
              .deleteFile(oldImageKey)
              .catch((err) =>
                this.logger.warn(`Failed to delete old image: ${err.message}`),
              );
          }
        }

        return updatedUser;
      } catch (dbError) {
        this.storageService
          .deleteFile(newImageKey)
          .catch((cleanupErr) =>
            this.logger.warn(
              `Failed to cleanup uploaded image after DB error: ${cleanupErr.message}`,
            ),
          );
        throw dbError;
      }
    } catch (error) {
      this.logger.error(`Failed to update profile image for user: ${user.id}`);
      if (error instanceof HttpException) throw error;
      throw new InternalServerErrorException('Failed to update profile image');
    }
  }

  async removeProfileImage(user: User) {
    const { id } = user;
    try {
      const currentUser = await this.prisma.client.user.findUnique({
        where: { id },
        select: { image: true },
      });

      if (!currentUser) throw new NotFoundException('User not found');
      if (!currentUser.image)
        throw new BadRequestException('No image to remove');

      const imageKey = this.storageService.extractKeyFromUrl(currentUser.image);

      if (imageKey) {
        await this.storageService.deleteFile(imageKey);
      }

      return await this.prisma.client.user.update({
        where: { id },
        data: { image: null },
        select: SELECT_USER_FIELDS,
      });
    } catch (error) {
      this.logger.error(`Failed to remove profile image for user: ${user.id}`);
      if (error instanceof HttpException) throw error;
      throw new InternalServerErrorException('Failed to remove profile image');
    }
  }

  async getBillingAddress(user: User) {
    try {
      return await this.prisma.client.billingAddress.findUnique({
        where: { organizationId: user.organizationId },
      });
    } catch (error) {
      this.logger.error(`Failed to get billing address for user: ${user.id}`);
      throw new InternalServerErrorException('Failed to get billing address');
    }
  }

  async updateBillingAddress(
    organizationId: string,
    updateBillingAddressDto: UpdateBillingAddressDto,
  ) {
    try {
      return await this.prisma.client.billingAddress.upsert({
        where: { organizationId },
        update: updateBillingAddressDto,
        create: { ...updateBillingAddressDto, organizationId },
      });
    } catch (error) {
      this.logger.error(
        `Failed to update billing address for org: ${organizationId}`,
      );
      if (error instanceof PrismaClientKnownRequestError) {
        switch (error.code) {
          case 'P2025':
            throw new NotFoundException('Billing address not found');
          default:
            throw new InternalServerErrorException('Database error');
        }
      }
      throw new InternalServerErrorException(
        'Failed to update billing address',
      );
    }
  }

  async getDashboardData(organizationId: string) {
    try {
      const result = await this.prisma.client.organization.findUnique({
        where: { id: organizationId },
        include: {
          wallet: {
            include: {
              transactions: {
                where: { category: WalletTransactionCategory.settled },
                orderBy: { createdAt: 'desc' },
                take: 10,
                include: {
                  payment: { select: { type: true } },
                },
              },
            },
          },
          paymentMethods: {
            where: {
              status: {
                in: [
                  PaymentMethodStatus.verified,
                  PaymentMethodStatus.pending,
                  PaymentMethodStatus.expired,
                ],
              },
            },
            orderBy: { createdAt: 'asc' },
          },
          rechargePlan: true,
          payments: {
            orderBy: { createdAt: 'desc' },
            take: 10,
            select: {
              id: true,
              type: true,
              status: true,
              paymentMethod: true,
              amount: true,
              fee: true,
              initiatedAt: true,
              user: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  image: true,
                },
              },
            },
          },
          _count: {
            select: { users: true },
          },
        },
      });

      if (!result) throw new NotFoundException('Organization not found');

      if (result.wallet) {
        (result.wallet as any).availableBalance = result.wallet.balance.minus(
          result.wallet.reserved,
        );
      }

      return result;
    } catch (error) {
      this.logger.error(
        `Failed to get dashboard data for org: ${organizationId}`,
      );
      if (error instanceof HttpException) throw error;
      throw new InternalServerErrorException('Failed to get dashboard data');
    }
  }
}
