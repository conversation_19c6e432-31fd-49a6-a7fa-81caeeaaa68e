import {
  BadRequestException,
  HttpException,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { BatchStatus, CounterType, Prisma } from '@repo/database';
import { format } from 'date-fns';
import { PrismaService } from 'src/infrastructure/prisma/prisma.service';
import { FilesToUpload } from 'src/common/types/upload-files';
import { OrdersService } from 'src/orders/orders.service';
import { filterBuilder } from 'src/utils/queryTools.util';
import { BatchQueryDto } from './dto/batch-query.dto';
import { CreateBatchDto } from './dto/create-batch.dto';
import { UpdateBatchDto } from './dto/update-batch.dto';
import { ExcelParserService } from './excel-parser.service';

@Injectable()
export class BatchesService {
  private readonly logger = new Logger(BatchesService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly excelParser: ExcelParserService,
    private readonly ordersService: OrdersService,
  ) {}

  // ✅
  async create(organizationId: string, createBatchDto: CreateBatchDto) {
    try {
      const response = await this.prisma.client.$transaction(
        async (tx: Prisma.TransactionClient) => {
          const batchNo = await this.generateBatchNo(tx, organizationId);
          const name = createBatchDto?.name ?? batchNo;

          const newBatch = await tx.batch.create({
            data: {
              ...createBatchDto,
              organizationId,
              name,
              batchNo,
            },
          });
          return newBatch;
        },
      );
      return response;
    } catch (error) {
      console.log(error);
      this.logger.error(`Failed to create batch: ${error.message}`);
      throw new InternalServerErrorException('Failed to create batch');
    }
  }

  // ✅
  async findAll(organizationId: string, query: BatchQueryDto) {
    try {
      const { limit, offset, filter, search } = query;

      // Quick search filter
      const allowedQuickSearchFields = ['batchNo', 'name'];
      // Advance filter
      const fuzzyFields = ['batchNo', 'name'];

      const modifiedFilter = filterBuilder(filter ?? {}, {
        fuzzyFields,
        jsonFields: [],
      });

      const where: Prisma.BatchWhereInput = {
        organizationId,
        ...modifiedFilter,
      };

      if (search) {
        const searchConditions: Prisma.BatchWhereInput['OR'] = [];

        allowedQuickSearchFields.forEach((field) => {
          searchConditions.push({
            [field]: { contains: search, mode: 'insensitive' },
          });
        });
        where.OR = searchConditions;
      }

      const [data, count] = await Promise.all([
        this.prisma.client.batch.findMany({
          where,
          orderBy: [{ createdAt: 'desc' }],
          take: limit,
          skip: offset ? offset : undefined,
          include: {
            _count: {
              select: {
                orders: true,
              },
            },
          },
        }),
        this.prisma.client.batch.count({ where }),
      ]);

      return {
        data,
        pagination: {
          total: count,
          limit,
          offset: offset ? offset : 0,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to retrieve batches: ${error.message}`);
      throw new InternalServerErrorException('Failed to retrieve batches');
    }
  }

  // ✅
  async findOne(organizationId: string, id: string, includeOrders: boolean) {
    try {
      const batch = await this.prisma.client.batch.findUnique({
        where: { id, organizationId },
        include: {
          orders: includeOrders
            ? {
                include: {
                  batch: { select: { name: true } },
                  orderItems: { include: { product: true } },
                  store: { select: { name: true } },
                },
              }
            : false,
        },
      });
      if (!batch) {
        throw new NotFoundException(`Batch with ID ${id} not found`);
      }
      return batch;
    } catch (error) {
      this.logger.error(`Failed to retrieve batch: ${error.message}`);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to retrieve batch');
    }
  }

  // ✅
  async update(
    organizationId: string,
    id: string,
    updateBatchDto: UpdateBatchDto,
  ) {
    try {
      // If trying to change status to 'processed', then we need to check if all orders in the batch are 'processing'
      if (updateBatchDto.status === BatchStatus.processed) {
        const nonProcessingCount = await this.prisma.client.order.count({
          where: {
            organizationId,
            batchId: id,
            status: {
              not: 'processing',
            },
          },
        });
        if (nonProcessingCount > 0) {
          throw new BadRequestException(
            `Cannot mark batch as processed. ${nonProcessingCount} orders are not in processing status.`,
          );
        }
      }

      return await this.prisma.client.batch.update({
        where: { id, organizationId },
        data: updateBatchDto,
      });
    } catch (error) {
      this.logger.error(`Failed to update batch: ${error.message}`);

      if (error instanceof HttpException) throw error;

      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2025') {
          throw new NotFoundException(`Batch with ID ${id} not found`);
        }
      }

      throw new InternalServerErrorException('Failed to update batch');
    }
  }

  async archiveBatch(organizationId: string, id: string) {
    try {
      const batch = await this.prisma.client.batch.findUnique({
        where: { id, organizationId },
        select: { status: true },
      });

      if (!batch) {
        throw new NotFoundException(`Batch with ID ${id} not found`);
      }

      if (batch.status !== BatchStatus.processed) {
        throw new BadRequestException(`Cannot archive ${batch.status} batch`);
      }

      await this.prisma.client.batch.update({
        where: { id, organizationId },
        data: { status: BatchStatus.archived },
      });
    } catch (error) {
      this.logger.error(`Failed to archive batch: ${error.message}`);
      if (error instanceof HttpException) throw error;
      throw new InternalServerErrorException('Failed to archive batch');
    }
  }

  async uploadBatchExcel(organizationId: string, files: FilesToUpload[]) {
    try {
      // Only one file is allowed
      const file = files[0];
      const fileName = file.originalname; // Batch name

      const processedExcelFile = await this.excelParser.parseFile(file);
      const totalOrders = processedExcelFile.length;

      this.logger.log(`Processing ${totalOrders} orders`);

      const processingStats = await this.prisma.client.$transaction(
        async (tx: Prisma.TransactionClient) => {
          // Create batch
          const batchNo = await this.generateBatchNo(tx, organizationId);
          const newBatch = await tx.batch.create({
            data: {
              organizationId,
              batchNo,
              name: fileName.replace(/\.(csv|xlsx|xls)$/i, ''), // Remove file extension (csv, xlsx, xls)
            },
          });

          const completeOrders: Prisma.OrderCreateInput[] = [];
          const processingStats = {
            total: totalOrders,
            successful: 0,
            failed: 0,
            errors: [] as string[],
          };

          for (const [index, order] of processedExcelFile.entries()) {
            try {
              if (index > 0 && index % 50 === 0) {
                this.logger.log(`Processed ${index}/${totalOrders} orders`);
              }

              const mappedOrderItems =
                await this.ordersService.mapOrderItemsToProducts(
                  tx,
                  organizationId,
                  order.items,
                );

              completeOrders.push({
                orderNo:
                  order.orderNo ??
                  (await this.ordersService.generateOrderNo(
                    tx,
                    organizationId,
                  )),
                organization: { connect: { id: organizationId } },
                batch: { connect: { id: newBatch.id } },
                store: { connect: { id: 'cmcep1zxb00018zy7ebneydtm' } },
                orderDate: order.orderDate,
                carrier: order.carrier,
                service: order.service ?? undefined,
                fromAddress: { connect: { id: order.fromAddressId } },
                toAddress: JSON.parse(JSON.stringify(order.toAddress)),
                parcel: JSON.parse(JSON.stringify(order.parcel)),
                orderItems: {
                  createMany: {
                    data: mappedOrderItems.map(
                      ({ product, quantity, price }) => ({
                        productId: product.id,
                        quantity,
                        price,
                      }),
                    ),
                  },
                },
                notes: order.notes,
              });

              processingStats.successful++;
            } catch (error) {
              processingStats.failed++;
              processingStats.errors.push(
                `Order ${order.orderNo}: ${error.message}`,
              );
              this.logger.error(
                `Error processing order ${order.orderNo}:`,
                error,
              );
            }
          }

          this.logger.log(
            `Successfully processed ${processingStats.successful}/${processingStats.total} orders`,
          );

          // If all orders are invalid, then just return the processing stats and delete batch
          if (completeOrders.length === 0) {
            await tx.batch.delete({
              where: { id: newBatch.id },
            });
            return processingStats;
          }

          for (const order of completeOrders) {
            await tx.order.create({ data: order });
          }
          return processingStats;
        },
        {
          timeout: 120000, // 2 minute timeout for large batches
        },
      );

      return processingStats;
    } catch (error) {
      this.logger.error(`Failed to upload batch excel: ${error.message}`);
      throw new InternalServerErrorException('Failed to upload batch excel');
    }
  }

  async removeBatch(organizationId: string, id: string) {
    try {
      const batch = await this.prisma.client.batch.findUnique({
        where: { id, organizationId },
        select: { status: true },
      });

      if (!batch) {
        throw new NotFoundException(`Batch with ID ${id} not found`);
      }

      if (batch.status !== BatchStatus.open) {
        throw new BadRequestException(`Cannot delete ${batch.status} batch`);
      }

      await this.prisma.client.batch.delete({
        where: { id, organizationId },
      });
    } catch (error) {
      this.logger.error(`Failed to remove batch: ${error.message}`);
      if (error instanceof HttpException) throw error;
      throw new InternalServerErrorException('Failed to remove batch');
    }
  }

  // ========== INTERNAL FUNCTIONS ==========
  private async generateBatchNo(
    tx: Prisma.TransactionClient,
    organizationId: string,
  ) {
    const today = new Date();
    today.setDate(1);
    today.setHours(0, 0, 0, 0);
    const dateStr = format(today, 'MMyyyy');
    const prefix = `BCH-${dateStr}-`;

    const counterRecord = await tx.counter.upsert({
      where: {
        organizationId_type_date: {
          organizationId,
          type: CounterType.batch,
          date: today,
        },
      },
      update: { count: { increment: 1 } },
      create: {
        organizationId,
        type: CounterType.batch,
        date: today,
        count: 1,
      },
    });

    const sequenceStr = counterRecord.count.toString().padStart(5, '0');
    return prefix + sequenceStr;
  }
}
