import { BatchStatus } from '@repo/database';
import { Type } from 'class-transformer';
import { IsEnum, IsOptional, IsString, ValidateNested } from 'class-validator';
import { PaginationQueryDto } from 'src/common/dto/pagination-query.dto';

class BatchFilterDto {
  @IsEnum(BatchStatus)
  @IsOptional()
  status?: BatchStatus;

  @IsString()
  @IsOptional()
  batchNo?: string;

  @IsString()
  @IsOptional()
  name?: string;
}

export class BatchQueryDto extends PaginationQueryDto {
  @ValidateNested()
  @Type(() => BatchFilterDto)
  @IsOptional()
  filter?: BatchFilterDto;

  @IsString()
  @IsOptional()
  search?: string;
}
