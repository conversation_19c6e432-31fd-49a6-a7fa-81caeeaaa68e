import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { Files } from 'src/common/decorators/files.decorator';
import { CurrentUser } from 'src/common/decorators/user.decorator';
import { FilesToUpload } from 'src/common/types/upload-files';
import { BatchesService } from './batches.service';
import { BatchQueryDto } from './dto/batch-query.dto';
import { CreateBatchDto } from './dto/create-batch.dto';
import { UpdateBatchDto } from './dto/update-batch.dto';

@Controller('batches')
export class BatchesController {
  constructor(private readonly batchesService: BatchesService) {}

  // ✅
  @Post()
  create(
    @CurrentUser('organizationId') organizationId: string,
    @Body() createBatchDto: CreateBatchDto,
  ) {
    return this.batchesService.create(organizationId, createBatchDto);
  }

  // ✅
  @Get()
  findAll(
    @Query() query: BatchQueryDto,
    @CurrentUser('organizationId') organizationId: string,
  ) {
    return this.batchesService.findAll(organizationId, query);
  }

  // ✅
  @Get(':id')
  findOne(
    @CurrentUser('organizationId') organizationId: string,
    @Query('include') include: string,
    @Param('id') id: string,
  ) {
    const includeOrders = !!include?.includes('orders');

    return this.batchesService.findOne(organizationId, id, includeOrders);
  }

  // ✅
  @Put(':id')
  update(
    @CurrentUser('organizationId') organizationId: string,
    @Param('id') id: string,
    @Body() updateBatchDto: UpdateBatchDto,
  ) {
    return this.batchesService.update(organizationId, id, updateBatchDto);
  }

  @Post('upload')
  uploadBatchExcel(
    @CurrentUser('organizationId') organizationId: string,
    @Files() files: FilesToUpload[],
  ) {
    return this.batchesService.uploadBatchExcel(organizationId, files);
  }

  @Patch(':id/archive')
  archiveBatch(
    @CurrentUser('organizationId') organizationId: string,
    @Param('id') id: string,
  ) {
    return this.batchesService.archiveBatch(organizationId, id);
  }

  @Delete(':id')
  removeBatch(
    @CurrentUser('organizationId') organizationId: string,
    @Param('id') id: string,
  ) {
    return this.batchesService.removeBatch(organizationId, id);
  }
}
