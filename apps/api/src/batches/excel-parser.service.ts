import { FilesToUpload } from 'src/common/types/upload-files';
import * as XLSX from 'xlsx';
import {
  CombinedOrder,
  ExcelTemplateRow,
  ProcessedRow,
} from './types/batch-upload.interface';

const EXCEL_EPOCH_DIFF = 25569;

export class ExcelParserService {
  async parseFile(file: FilesToUpload) {
    const workbook = XLSX.read(file.buffer, { type: 'buffer' });
    const sheet = workbook.Sheets[workbook.SheetNames[0]];

    const json = this.parseToJson(sheet) as ExcelTemplateRow[];

    const processedRows = json.map((row) => this.processRow(row));

    const combinedOrders = this.combineSameOrders(processedRows);

    return combinedOrders.sort(
      (a, b) => a.orderDate.getTime() - b.orderDate.getTime(),
    );
  }

  private processRow(row: ExcelTemplateRow): ProcessedRow {
    return {
      orderNo: row['Order No'] || null,
      orderDate: new Date(
        (row['Order Date'] - EXCEL_EPOCH_DIFF) * 24 * 60 * 60 * 1000,
      ),
      carrier: row['Carrier'],
      service: row['Service Level'],
      parcel: {
        length: row['Length(in)'],
        width: row['Width(in)'],
        height: row['Height(in)'],
        weight: row['Weight(oz)'],
        predefinedPackage: row['Predefined Package'],
      },
      toAddress: {
        name: row['Recipient First Name'] + ' ' + row['Recipient Last Name'],
        company: row['Recipient Company'],
        street1: row['Recipient Street 1'],
        street2: row['Recipient Street 2'],
        city: row['Recipient City'],
        state: row['Recipient State'],
        zip: `${row['Recipient Postal Code']}`,
        country: row['Recipient Country Code'],
        phone: `${row['Recipient Phone']}`,
        email: row['Recipient Email'],
        residential: row['Is Residential'].toLowerCase() === 'yes',
      },
      fromAddressId: row['Warehouse Address ID'],

      item: {
        product: {
          sku: row['Item SKU'],
          name: row['Item Name'],
        },
        quantity: row['Item Quantity'],
        price: row['Item Unit Price'],
      },
      notes: row['Notes'],
    };
  }

  private combineSameOrders(orders: ProcessedRow[]): CombinedOrder[] {
    const grouped = orders.reduce((acc, order) => {
      const key = `${order.orderNo}`;
      if (!acc[key]) {
        acc[key] = [];
      }
      acc[key].push(order);
      return acc;
    }, {});

    return Object.keys(grouped).map((key) => {
      const { item, ...rest } = grouped[key][0];
      const items = grouped[key].map((order) => order.item);
      return { ...rest, items };
    });
  }

  private parseToJson(sheet: XLSX.WorkSheet) {
    const json = XLSX.utils.sheet_to_json(sheet);
    return json;
  }
}
