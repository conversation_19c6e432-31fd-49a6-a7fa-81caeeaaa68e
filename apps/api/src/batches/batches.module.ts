import { Module } from '@nestjs/common';
import { BatchesService } from './batches.service';
import { BatchesController } from './batches.controller';
import { ExcelParserService } from './excel-parser.service';
import { OrdersModule } from 'src/orders/orders.module';

@Module({
  imports: [OrdersModule],
  controllers: [BatchesController],
  providers: [BatchesService, ExcelParserService],
})
export class BatchesModule {}
