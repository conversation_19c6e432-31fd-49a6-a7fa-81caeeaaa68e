import { Address } from '@repo/database';

// This corresponds with the excel template column header
export type ExcelTemplateRow = {
  'Order No': string;
  'Order Date': number;

  Carrier: string;
  'Service Level': string;

  'Height(in)': number;
  'Length(in)': number;
  'Width(in)': number;
  'Weight(oz)': number;

  'Predefined Package': string;
  'Recipient First Name': string;
  'Recipient Last Name': string;
  'Recipient Company': string;
  'Recipient Street 1': string;
  'Recipient Street 2': string;
  'Recipient City': string;
  'Recipient State': string;
  'Recipient Postal Code': number;
  'Recipient Country Code': string;
  'Recipient Phone': string;
  'Recipient Email': string;
  'Is Residential': string;

  'Warehouse Address ID': string;

  'Item SKU': string;
  'Item Name': string;
  'Item Quantity': number;
  'Item Unit Price': number;

  Notes: string;
};

export type ProcessedRow = {
  orderNo: string | null;
  orderDate: Date;
  carrier: string;
  service: string;
  toAddress: Partial<Address>;
  fromAddressId: string;
  parcel: {
    length: number;
    width: number;
    height: number;
    weight: number;
    predefinedPackage: string;
  };

  item: {
    product: {
      sku: string;
      name: string;
    };
    quantity: number;
    price: number;
  };
  notes: string;
};

export type CombinedOrder = Omit<ProcessedRow, 'item'> & {
  items: ProcessedRow['item'][];
};
