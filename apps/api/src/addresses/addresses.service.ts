import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  UnprocessableEntityException,
} from '@nestjs/common';
import { PrismaService } from 'src/infrastructure/prisma/prisma.service';
import { EasypostService } from 'src/infrastructure/easypost/easypost.service';
import { CreateAddressDto } from './dto/create-address.dto';
import { UpdateAddressDto } from './dto/update-address.dto';
import { VerifyAddressDto } from './dto/verify-address.dto';

@Injectable()
export class AddressesService {
  private readonly logger = new Logger(AddressesService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly easypostService: EasypostService,
  ) {}

  // ✅
  async create(organizationId: string, createAddressDto: CreateAddressDto) {
    try {
      const epAddress = await this.easypostService.createAndVerifyAddress({
        street1: createAddressDto.street1,
        street2: createAddressDto.street2,
        city: createAddressDto.city,
        state: createAddressDto.state,
        zip: createAddressDto.zip,
        country: createAddressDto.country,
        name: createAddressDto.name,
        company: createAddressDto.company,
        phone: createAddressDto.phone,
        email: createAddressDto.email,
        residential: createAddressDto.residential,
        verify: true,
      });

      const isVerified = epAddress.verifications?.delivery?.success === true;

      const address = await this.prisma.client.address.create({
        data: {
          ...createAddressDto,
          organizationId,
          epAddressId: epAddress.id,
          verified: isVerified,
        },
      });

      return address;
    } catch (error) {
      this.logger.error(`Failed to create address: ${error.message}`);

      // Handle EasyPost verification errors specifically
      if (error.code === 'ADDRESS.VERIFY.FAILURE') {
        const errorMessages =
          error.errors
            ?.map((err: { message: string }) => err.message)
            .join(', ') || 'Address verification failed';
        throw new UnprocessableEntityException(
          `Address verification failed: ${errorMessages}`,
        );
      } else throw new InternalServerErrorException('Failed to create address');
    }
  }

  // ✅
  async findAll(organizationId: string) {
    try {
      const response = await this.prisma.client.address.findMany({
        where: { organizationId },
      });

      return response;
    } catch (error) {
      this.logger.error(`Failed to retrieve addresses: ${error.message}`);
      throw new InternalServerErrorException('Failed to retrieve addresses');
    }
  }

  // ✅
  async update(
    organizationId: string,
    id: string,
    updateAddressDto: UpdateAddressDto,
  ) {
    try {
      const epAddress = await this.easypostService.createAndVerifyAddress({
        street1: updateAddressDto.street1,
        street2: updateAddressDto.street2,
        city: updateAddressDto.city,
        state: updateAddressDto.state,
        zip: updateAddressDto.zip,
        country: updateAddressDto.country,
        name: updateAddressDto.name,
        company: updateAddressDto.company,
        phone: updateAddressDto.phone,
        email: updateAddressDto.email,
        residential: updateAddressDto.residential,
        verify: true,
      });

      const isVerified = epAddress.verifications?.delivery?.success === true;

      const updatedAddress = await this.prisma.client.address.update({
        where: { id, organizationId },
        data: {
          ...updateAddressDto,
          epAddressId: epAddress.id,
          verified: isVerified,
        },
      });

      return updatedAddress;
    } catch (error) {
      this.logger.error(`Failed to update address: ${error.message}`);
      throw new InternalServerErrorException('Failed to update address');
    }
  }

  // ✅
  async remove(organizationId: string, id: string) {
    try {
      const response = await this.prisma.client.address.delete({
        where: { id, organizationId },
      });
      return response;
    } catch (error) {
      console.log(error);
      this.logger.error(`Failed to delete address: ${error.message}`);

      if (error.code === 'P2025') {
        throw new NotFoundException(`Address with ID ${id} not found`);
      }
      if (error.code === 'P2003') {
        throw new BadRequestException(
          'Address is associated with at least one order shipment. Cannot be deleted',
        );
      }

      throw new InternalServerErrorException('Failed to delete address');
    }
  }

  // ✅
  async verify(verifyAddressDto: VerifyAddressDto) {
    const epAddress = await this.easypostService.createAndVerifyAddress({
      street1: verifyAddressDto.street1,
      street2: verifyAddressDto.street2,
      city: verifyAddressDto.city,
      state: verifyAddressDto.state,
      zip: verifyAddressDto.zip,
      country: verifyAddressDto.country,
      name: verifyAddressDto.name,
      company: verifyAddressDto.company,
      phone: verifyAddressDto.phone,
      email: verifyAddressDto.email,
      residential: verifyAddressDto.residential,
      verify: true,
    });
    const verified = epAddress.verifications?.delivery?.success === true;
    const epAddressId = epAddress.id;

    return { verified, epAddressId };
  }
}
