import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  Param,
  Post,
  Put,
} from '@nestjs/common';
import { CurrentUser } from 'src/common/decorators/user.decorator';
import { AddressesService } from './addresses.service';
import { CreateAddressDto } from './dto/create-address.dto';
import { UpdateAddressDto } from './dto/update-address.dto';
import { VerifyAddressDto } from './dto/verify-address.dto';

@Controller('addresses')
export class AddressesController {
  constructor(private readonly addressesService: AddressesService) {}

  // ✅
  @Post()
  create(
    @CurrentUser('organizationId') organizationId: string,
    @Body() createAddressDto: CreateAddressDto,
  ) {
    return this.addressesService.create(organizationId, createAddressDto);
  }

  // ✅
  @Get()
  findAll(@CurrentUser('organizationId') organizationId: string) {
    return this.addressesService.findAll(organizationId);
  }

  // ✅
  @Put(':id')
  update(
    @CurrentUser('organizationId') organizationId: string,
    @Param('id') id: string,
    @Body() updateAddressDto: UpdateAddressDto,
  ) {
    return this.addressesService.update(organizationId, id, updateAddressDto);
  }

  // ✅
  @Delete(':id')
  remove(
    @CurrentUser('organizationId') organizationId: string,
    @Param('id') id: string,
  ) {
    return this.addressesService.remove(organizationId, id);
  }

  // ✅
  @Post('verify')
  @HttpCode(200)
  verify(@Body() verifyAddressDto: VerifyAddressDto) {
    return this.addressesService.verify(verifyAddressDto);
  }
}
