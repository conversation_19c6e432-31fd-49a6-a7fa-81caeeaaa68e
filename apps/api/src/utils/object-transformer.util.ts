export class ObjectTransformer {
  static snakeToCamel(obj: any): any {
    if (obj === null || obj === undefined || typeof obj !== 'object') {
      return obj;
    }
    // Handle arrays
    if (Array.isArray(obj)) {
      return obj.map((item) => ObjectTransformer.snakeToCamel(item));
    }
    // Handle objects
    return Object.fromEntries(
      Object.entries(obj).map(([key, value]) => {
        // Convert the key (preserve keys starting with _)
        const newKey = key.startsWith('_')
          ? key
          : key
              .toLowerCase()
              .replace(/[-_][a-z]/g, (group) => group.slice(-1).toUpperCase());

        // Recursively convert the value
        const newValue = ObjectTransformer.snakeToCamel(value);

        return [newKey, newValue];
      }),
    );
  }

  static camelToSnake(obj: any): any {
    // Add reverse function if needed
  }
}
