type Config = {
  fuzzyFields: string[];
  jsonFields: string[];
};

export const filterBuilder = (
  filter: Record<string, any>,
  config: Config,
  parentKey?: string,
) => {
  const result = {};

  for (const [key, value] of Object.entries(filter)) {
    if (value === undefined || value === null) continue;

    const fullKey = parentKey ? `${parentKey}.${key}` : key;

    if (typeof value === 'object' && !Array.isArray(value)) {
      result[key] = filterBuilder(value, config, fullKey);
    } else {
      if (config.jsonFields.includes(fullKey)) {
        result['path'] = [key];
        result['string_contains'] = value;
        result['mode'] = 'insensitive';
      } else {
        result[key] = config.fuzzyFields.includes(fullKey)
          ? { contains: value, mode: 'insensitive' }
          : value;
      }
    }
  }
  return result;
};
