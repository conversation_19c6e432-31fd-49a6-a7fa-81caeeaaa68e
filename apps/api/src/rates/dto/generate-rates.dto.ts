import { Type } from 'class-transformer';
import {
  IsArray,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { ParcelDto } from 'src/common/dto/parcel.dto';
import { AddressDtoWithId } from 'src/infrastructure/easypost/dto/create-shipment.dto';

export class GenerateRatesDto {
  @ValidateNested()
  @Type(() => AddressDtoWithId)
  @IsNotEmpty()
  fromAddress: AddressDtoWithId;

  @ValidateNested()
  @Type(() => AddressDtoWithId)
  @IsNotEmpty()
  toAddress: AddressDtoWithId;

  @ValidateNested()
  @Type(() => ParcelDto)
  @IsNotEmpty()
  parcel: ParcelDto;

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  carrierAccountIds?: string[];
}
