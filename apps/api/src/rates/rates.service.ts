import { IRate } from '@easypost/api';
import {
  BadRequestException,
  HttpException,
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { OrganizationCarrierPricing, User } from '@repo/database';
import { AddressDto as AddressDtoFromDB } from 'src/common/dto/address.dto';
import { ParcelDto } from 'src/common/dto/parcel.dto';
import { PrismaService } from 'src/infrastructure/prisma/prisma.service';
import { AddressDtoWithId } from 'src/infrastructure/easypost/dto/create-shipment.dto';
import { EasypostService } from 'src/infrastructure/easypost/easypost.service';
import { ObjectTransformer } from 'src/utils/object-transformer.util';
import { sortFromCheapest } from 'src/utils/rates';
import { GenerateRatesDto } from './dto/generate-rates.dto';

const LABEL_COST = 0.03; // Fixed cost from EasyPost

@Injectable()
export class RatesService {
  private readonly logger = new Logger(RatesService.name);

  constructor(
    private readonly easypostService: EasypostService,
    private readonly prisma: PrismaService,
  ) {}

  // ✅
  async getRates(user: User, epShipmentId: string) {
    try {
      const epShipment = await this.easypostService.getShipment(epShipmentId);

      const userPricing = await this.getUserPricing(user.organizationId);
      const transformedRates = this.transformRates(
        epShipment.rates,
        userPricing,
      );

      return ObjectTransformer.snakeToCamel(sortFromCheapest(transformedRates));
    } catch (error) {
      this.logger.error(`Failed to get rates: ${error}`);
      throw new InternalServerErrorException('Failed to get rates');
    }
  }

  // ✅
  async generateRates(user: User, generateRatesDto: GenerateRatesDto) {
    try {
      // Validate parcel
      const parcel = this.validateParcel(generateRatesDto.parcel);
      // Parse from address
      const fromAddress = await this.parseAddress(generateRatesDto.fromAddress);
      const toAddress = await this.parseAddress(generateRatesDto.toAddress);

      const userPricing = await this.getUserPricing(user.organizationId);
      const availableCarrierIds = userPricing.map(
        (pricing) => pricing.carrierId,
      );

      let carrier_accounts: string[] = [];

      if (generateRatesDto.carrierAccountIds) {
        carrier_accounts = availableCarrierIds.filter((id) =>
          generateRatesDto.carrierAccountIds?.includes(id),
        );
      } else {
        carrier_accounts = availableCarrierIds;
      }

      const shipment = await this.easypostService.createShipment({
        from_address: fromAddress,
        to_address: toAddress,
        parcel: parcel,
        carrier_accounts,
      });

      const { id, rates } = shipment;

      const transformedRates = this.transformRates(rates, userPricing);

      return {
        epShipmentId: id,
        rates: ObjectTransformer.snakeToCamel(
          sortFromCheapest(transformedRates),
        ),
      };
    } catch (error) {
      this.logger.error(`Failed to generate rates: ${error}`);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to generate rates');
    }
  }

  // =========== INTERNAL METHODS ===========
  /*
  This method retrieves the user pricing for the organization.
  */
  private async getUserPricing(organizationId: string) {
    return await this.prisma.client.organizationCarrierPricing.findMany({
      where: { organizationId },
    });
  }

  /*
  This method transforms the rates by adding a markup to the original rate.
  It also adds a fixed cost for the label.
  The total amount is the original rate plus the markup plus the label cost.
  The total amount is rounded to 2 decimal places.
  */
  private transformRates(
    rates: IRate[],
    userPricings: OrganizationCarrierPricing[],
  ) {
    return rates.map((rate) => {
      const carrierPricing = userPricings.find(
        (pricing) => pricing.carrierId === rate.carrier_account_id,
      );
      if (!carrierPricing) return { ...rate, rate: `${rate.rate} (no markup)` };

      const { percentageMarkup } = carrierPricing;
      const originalRate = parseFloat(rate.rate);
      const markupAmount = originalRate * (percentageMarkup / 100);
      const totalAmount = (originalRate + markupAmount + LABEL_COST).toFixed(2);

      return { ...rate, rate: totalAmount };
    });
  }

  /*
  This method gets the pricing for the organization and transforms the rate.
  Available for other services to use.
  */
  async getPricingAndTransformRate(organizationId: string, rate: IRate) {
    const userPricings = await this.getUserPricing(organizationId);
    const transformedRate = this.transformRates([rate], userPricings);
    return transformedRate[0];
  }

  /*
  This method validates and simplifies the parcel.
  */
  private validateParcel(parcel: ParcelDto): ParcelDto {
    if (parcel.predefinedPackage) {
      return {
        predefinedPackage: parcel.predefinedPackage,
        weight: parcel.weight,
      };
    }
    if (!parcel.length || !parcel.width || !parcel.height) {
      throw new BadRequestException(
        'Parcel must have length, width, and height',
      );
    }
    return {
      length: parcel.length,
      width: parcel.width,
      height: parcel.height,
      weight: parcel.weight,
    };
  }

  private async parseAddress(address: AddressDtoWithId) {
    const { id, ...rest } = address;
    if (address.id) {
      const existingAddress = await this.prisma.client.address.findUnique({
        where: { id },
      });
      if (!existingAddress) throw new BadRequestException('Address not found');
      return { id: existingAddress.epAddressId };
    } else return { ...rest } as AddressDtoFromDB;
  }
}
