import { Body, Controller, Get, Param, Post } from '@nestjs/common';
import { User } from '@repo/database';
import { CurrentUser } from 'src/common/decorators/user.decorator';
import { GenerateRatesDto } from './dto/generate-rates.dto';
import { RatesService } from './rates.service';

@Controller('rates')
export class RatesController {
  constructor(private readonly ratesService: RatesService) {}

  // ☑️
  @Post('/generate')
  generateRates(
    @CurrentUser() user: User,
    @Body() generateRatesDto: GenerateRatesDto,
  ) {
    return this.ratesService.generateRates(user, generateRatesDto);
  }

  // ☑️
  @Get(':epShipmentId')
  getRates(
    @CurrentUser() user: User,
    @Param('epShipmentId') epShipmentId: string,
  ) {
    return this.ratesService.getRates(user, epShipmentId);
  }
}
