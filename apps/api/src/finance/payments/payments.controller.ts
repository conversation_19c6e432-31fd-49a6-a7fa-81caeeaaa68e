import { Controller, Get, Query } from '@nestjs/common';
import { CurrentUser } from 'src/common/decorators/user.decorator';
import { PaymentsService } from './payments.service';
import { PaymentQueryDto } from './dto/payment-query.dto';

@Controller('payments')
export class PaymentsController {
  constructor(private readonly paymentsService: PaymentsService) {}

  @Get()
  getPayments(
    @CurrentUser('organizationId') organizationId: string,
    @Query() query: PaymentQueryDto,
  ) {
    return this.paymentsService.getPayments(organizationId, query);
  }
}
