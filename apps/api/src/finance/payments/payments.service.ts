import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import {
  Currency,
  Payment,
  PaymentStatus,
  PaymentType,
  Prisma,
  ProcessingMethod,
} from '@repo/database';
import { PrismaService } from 'src/infrastructure/prisma/prisma.service';
import { StripeService } from 'src/infrastructure/stripe/stripe.service';
import { CreatePaymentDto } from './dto/create-payment.dto';
import { PaymentQueryDto } from './dto/payment-query.dto';
import { filterBuilder } from 'src/utils/queryTools.util';
import { endOfDay, startOfDay } from 'date-fns';

@Injectable()
export class PaymentsService {
  private readonly logger = new Logger(PaymentsService.name);

  private readonly createPaymentHandlers: Record<
    PaymentType,
    (dto: CreatePaymentDto) => Promise<Payment>
  > = {
    [PaymentType.credit_card]: this.handleCreditCard.bind(this),
    [PaymentType.ach]: this.handleAchPayment.bind(this),
    [PaymentType.wire_transfer]: this.handleWireTransfer.bind(this),
    // Other payment types
    [PaymentType.ach_auto]: this.handleAchPayment.bind(this),
    [PaymentType.credit_card_auto]: this.handleCreditCard.bind(this),
    [PaymentType.paypal]: this.handlePaypal.bind(this),
  };

  constructor(
    private readonly stripeService: StripeService,
    private readonly prisma: PrismaService,
  ) {}

  async getPayments(organizationId: string, query: PaymentQueryDto) {
    const { limit, offset = 0, fromDate, toDate, filter } = query;

    const modifiedFilter = filterBuilder(filter ?? {}, {
      fuzzyFields: [],
      jsonFields: [],
    });

    const where: Prisma.PaymentWhereInput = {
      organizationId,
      ...(fromDate || toDate
        ? {
            createdAt: {
              ...(fromDate && { gte: startOfDay(new Date(fromDate)) }),
              ...(toDate && { lte: endOfDay(new Date(toDate)) }),
            },
          }
        : {}),
      ...modifiedFilter,
    };

    try {
      const [data, total] = await Promise.all([
        this.prisma.client.payment.findMany({
          where,
          orderBy: { createdAt: 'desc' },
          take: limit,
          skip: offset,
          include: {
            paymentMethod: true,
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                image: true,
              },
            },
          },
        }),
        this.prisma.client.payment.count({ where }),
      ]);

      return {
        data,
        pagination: {
          total,
          limit,
          offset,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get payments: ${error}`);
    }
    throw new InternalServerErrorException('Failed to get payments');
  }

  async createPayment(createPaymentDto: CreatePaymentDto): Promise<Payment> {
    const handler = this.createPaymentHandlers[createPaymentDto.type];
    if (!handler) {
      this.logger.error(`Unsupported payment type: ${createPaymentDto.type}`);
      throw new Error(
        `Missing handler for payment type: ${createPaymentDto.type}`,
      );
    }

    return handler(createPaymentDto);
  }

  private async handleCreditCard(createPaymentDto: CreatePaymentDto) {
    const { organizationId, userId, type, amount, fee } = createPaymentDto;
    // 1. Get Stripe customer and payment method ID
    const organization = await this.prisma.client.organization.findUnique({
      where: { id: organizationId },
      select: {
        stripeCustomerId: true,
        paymentMethods: {
          where: {
            id:
              createPaymentDto.paymentMethodId ??
              'non-existent-payment-method-id',
          },
          select: { stripePaymentMethodId: true },
        },
      },
    });

    if (!organization) {
      throw new BadRequestException('Organization not found');
    }

    if (organization.paymentMethods.length === 0) {
      throw new BadRequestException(
        'Payment method not found or does not belong to organization',
      );
    }

    const customerId = organization.stripeCustomerId;
    const paymentMethodId =
      organization.paymentMethods[0].stripePaymentMethodId;

    if (!customerId || !paymentMethodId) {
      throw new BadRequestException('Customer or payment method not found');
    }

    const amountToCharge = amount + (fee ?? 0);
    const amountToChargeInCents = amountToCharge * 100;

    const paymentIntent = await this.stripeService.createPaymentIntent({
      amount: amountToChargeInCents,
      currency: Currency.usd,
      customer: customerId,
      payment_method: paymentMethodId,
    });

    return await this.prisma.client.payment.create({
      data: {
        organizationId,
        userId,
        type,
        status: PaymentStatus.completed,
        processingMethod: ProcessingMethod.stripe,
        paymentMethodId: createPaymentDto.paymentMethodId,
        amount: createPaymentDto.amount,
        fee: createPaymentDto.fee,
        stripePaymentIntentId: paymentIntent.id,
        metadata: {
          chargedAt: new Date(paymentIntent.created),
          currency: paymentIntent.currency,
        },
        description: `Top up wallet by credit card`,
        initiatedAt: new Date(paymentIntent.created),
        completedAt: new Date(paymentIntent.created),
      },
    });
  }

  private async handleAchPayment(createPaymentDto: CreatePaymentDto) {
    return this.prisma.client.payment.create({
      data: {
        ...createPaymentDto,
        status: PaymentStatus.pending,
        processingMethod: ProcessingMethod.manual,
        description: 'ACH payment submission',
        initiatedAt: new Date(),
      },
    });
  }

  private async handleWireTransfer(createPaymentDto: CreatePaymentDto) {
    return this.prisma.client.payment.create({
      data: {
        ...createPaymentDto,
        status: PaymentStatus.pending,
        processingMethod: ProcessingMethod.manual,
        description: 'Wire transfer payment submission',
        initiatedAt: new Date(),
      },
    });
  }

  private async handlePaypal(createPaymentDto: CreatePaymentDto) {
    console.log(createPaymentDto);
    return { status: 'pending' };
  }
}
