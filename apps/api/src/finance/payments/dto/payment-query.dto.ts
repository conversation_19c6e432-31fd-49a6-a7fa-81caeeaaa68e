import { PaymentStatus } from '@repo/database';
import { Type } from 'class-transformer';
import {
  IsDateString,
  IsEnum,
  IsOptional,
  ValidateNested,
} from 'class-validator';
import { PaginationQueryDto } from 'src/common/dto/pagination-query.dto';

class PaymentFilterDto {
  @IsEnum(PaymentStatus)
  @IsOptional()
  status?: PaymentStatus;
}

export class PaymentQueryDto extends PaginationQueryDto {
  @ValidateNested()
  @Type(() => PaymentFilterDto)
  @IsOptional()
  filter?: PaymentFilterDto;

  @IsOptional()
  @IsDateString()
  fromDate?: string;

  @IsOptional()
  @IsDateString()
  toDate?: string;
}
