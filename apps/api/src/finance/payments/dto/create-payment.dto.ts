import { PaymentType } from '@repo/database';
import {
  Is<PERSON>num,
  IsJSON,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Max,
  Min,
} from 'class-validator';

export class CreatePaymentDto {
  @IsString()
  @IsNotEmpty()
  organizationId: string;

  @IsString()
  @IsNotEmpty()
  userId: string;

  @IsEnum(PaymentType)
  type: PaymentType;

  @IsNumber({ maxDecimalPlaces: 4 })
  @Min(0.01)
  @Max(999999)
  amount: number;

  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 4 })
  @Min(0.01)
  @Max(999999)
  fee?: number;

  @IsString()
  @IsNotEmpty()
  paymentMethodId?: string;

  @IsOptional()
  @IsJSON()
  metadata?: Record<string, any>;
}
