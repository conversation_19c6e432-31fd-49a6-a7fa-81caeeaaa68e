import { Module } from '@nestjs/common';
import { PaymentMethodsModule } from './payment-methods/payment-methods.module';
import { PaymentsModule } from './payments/payments.module';
import { WalletModule } from './wallet/wallet.module';

@Module({
  imports: [WalletModule, PaymentMethodsModule, PaymentsModule],
  exports: [WalletModule, PaymentMethodsModule, PaymentsModule],
})
export class FinanceModule {}
