import { Module } from '@nestjs/common';
import { WalletService } from './wallet.service';
import { WalletController } from './wallet.controller';
import { PaymentsModule } from '../payments/payments.module';
import { WalletTransactionService } from './wallet-transaction.service';
import { StorageModule } from 'src/infrastructure/storage/storage.module';

@Module({
  imports: [PaymentsModule, StorageModule],
  controllers: [WalletController],
  providers: [WalletService, WalletTransactionService],
  exports: [WalletTransactionService],
})
export class WalletModule {}
