import { PaymentType } from '@repo/database';
import {
  <PERSON><PERSON><PERSON>,
  IsNotEmpty,
  IsN<PERSON>ber,
  Is<PERSON><PERSON>al,
  IsString,
  <PERSON>,
  <PERSON>,
} from 'class-validator';

const TopUpPaymentTypes = {
  credit_card: PaymentType.credit_card,
  credit_card_auto: PaymentType.credit_card_auto,
  ach: PaymentType.ach,
  ach_auto: PaymentType.ach_auto,
  paypal: PaymentType.paypal,
} as const;
export class TopUpWalletDto {
  @IsEnum(TopUpPaymentTypes)
  paymentType: Exclude<PaymentType, 'wire_transfer'>;

  @IsNumber({ maxDecimalPlaces: 4 })
  @Min(0.01)
  @Max(999999)
  amount: number;

  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 4 })
  @Min(0.01)
  @Max(999999)
  fee?: number;

  @IsString()
  @IsNotEmpty()
  paymentMethodId: string;
}
