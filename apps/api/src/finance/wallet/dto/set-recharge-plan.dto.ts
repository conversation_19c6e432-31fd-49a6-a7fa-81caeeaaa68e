import {
  IsBoolean,
  IsInt,
  <PERSON><PERSON><PERSON>ber,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
} from 'class-validator';

export class SetRechargePlanDto {
  @IsBoolean()
  @IsOptional()
  enabled?: boolean;

  @IsNumber({ maxDecimalPlaces: 4 })
  @Min(0.01)
  @Max(999999)
  threshold: number;

  @IsNumber({ maxDecimalPlaces: 4 })
  @Min(0.01)
  @Max(999999)
  amount: number;

  @IsInt()
  @Min(1)
  maxPerDay: number;
}
