import { Transform } from 'class-transformer';
import {
  IsDateString,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Max,
  Min,
} from 'class-validator';
import { StringToNumber } from 'src/common/decorators/number-transform.decorator';

export class SubmitWireTransferDto {
  @IsDateString()
  @IsNotEmpty()
  date: string;

  @StringToNumber()
  @IsNumber({ maxDecimalPlaces: 4 })
  @Min(0.01)
  @Max(999999)
  amount: number;

  @IsOptional()
  @IsString()
  bankReference?: string;

  @IsOptional()
  @IsString()
  bankName?: string;
}
