import {
  BadRequestException,
  HttpException,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import {
  PaymentMethodStatus,
  PaymentStatus,
  PaymentType,
  User,
  Wallet,
  WalletTransactionCategory,
  WalletTransactionType,
} from '@repo/database';
import { endOfDay, startOfDay } from 'date-fns';
import {
  Decimal,
  PrismaClientKnownRequestError,
} from 'node_modules/@repo/database/generated/prisma/runtime/library';
import { PaginationQueryDto } from 'src/common/dto/pagination-query.dto';
import { FilesToUpload, UploadedFile } from 'src/common/types/upload-files';
import { PrismaService } from 'src/infrastructure/prisma/prisma.service';
import { StorageService } from 'src/infrastructure/storage/storage.service';
import { PaymentsService } from '../payments/payments.service';
import { SetRechargePlanDto } from './dto/set-recharge-plan.dto';
import { SubmitWireTransferDto } from './dto/submit-wire-transfer.dto';
import { TopUpWalletDto } from './dto/top-up-wallet.dto';
import { TopUpWalletResponse } from './types/top-up-wallet.interface';
import { WalletTransactionService } from './wallet-transaction.service';
import { WalletTransactionQueryDto } from './dto/wallet-transaction-query.dto';

@Injectable()
export class WalletService {
  private readonly logger = new Logger(WalletService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly paymentsService: PaymentsService,
    private readonly walletTransactionService: WalletTransactionService,
    private readonly storageService: StorageService,
  ) {}

  async findOne(organizationId: string, includes: string[]) {
    let data: Wallet & { pendingDeposits?: Decimal };
    try {
      const organization = await this.prisma.client.organization.findUnique({
        where: { id: organizationId },
        select: { wallet: true },
      });

      if (!organization) throw new NotFoundException('Organization not found');
      data = organization.wallet;

      if (includes.includes('pending_deposits')) {
        const pendingPayments = await this.prisma.client.payment.findMany({
          where: { organizationId, status: PaymentStatus.pending },
          select: { amount: true },
        });
        const combinedPendingDeposits = pendingPayments.reduce(
          (acc, payment) => {
            return acc.add(payment.amount);
          },
          new Decimal(0),
        );
        data.pendingDeposits = combinedPendingDeposits.toDP(
          2,
          Decimal.ROUND_DOWN,
        );
      }

      return organization.wallet;
    } catch (error) {
      this.logger.error(
        `Failed to get account wallet data for org: ${organizationId}`,
      );

      if (error instanceof HttpException) throw error;
      throw new InternalServerErrorException(
        'Failed to get account wallet data',
      );
    }
  }

  async findAllWalletTransactions(
    organizationId: string,
    query: WalletTransactionQueryDto,
  ) {
    const { limit, offset = 0, fromDate, toDate } = query;

    const where = {
      wallet: {
        organization: { id: organizationId },
      },
      category: WalletTransactionCategory.settled,
      ...(fromDate || toDate
        ? {
            createdAt: {
              ...(fromDate && { gte: startOfDay(new Date(fromDate)) }),
              ...(toDate && { lte: endOfDay(new Date(toDate)) }),
            },
          }
        : {}),
    };

    try {
      const [data, total] = await Promise.all([
        this.prisma.client.walletTransaction.findMany({
          where,
          take: limit,
          skip: offset,
          orderBy: { createdAt: 'desc' },
          include: {
            payment: true,
          },
        }),
        this.prisma.client.walletTransaction.count({ where }),
      ]);
      return {
        data,
        pagination: {
          total,
          limit,
          offset: offset ?? 0,
        },
      };
    } catch (error) {
      this.logger.error(
        `Failed to get wallet transactions for org ${organizationId}: ${error}`,
      );
      throw new InternalServerErrorException(
        'Failed to get wallet transactions',
      );
    }
  }

  async getRechargePlan(organizationId: string) {
    try {
      return await this.prisma.client.rechargePlan.findUnique({
        where: { organizationId },
      });
    } catch (error) {
      this.logger.error(
        `Failed to get recharge plan for org: ${organizationId}`,
      );
      if (error instanceof HttpException) throw error;
      throw new InternalServerErrorException('Failed to get recharge plan');
    }
  }

  async setRechargePlan(
    organizationId: string,
    setRechargePlanDto: SetRechargePlanDto,
  ) {
    const { enabled } = setRechargePlanDto;

    try {
      const result = await this.prisma.client.$transaction(async (tx) => {
        if (enabled) {
          const defaultPaymentMethod = await tx.paymentMethod.findFirst({
            where: {
              organizationId,
              isDefault: true,
              status: PaymentMethodStatus.verified,
            },
          });
          if (!defaultPaymentMethod)
            throw new BadRequestException(
              'An active payment method is required to set a recharge plan',
            );
        }

        return await tx.rechargePlan.upsert({
          where: { organizationId },
          update: setRechargePlanDto,
          create: { ...setRechargePlanDto, organizationId },
        });
      });

      return result;
    } catch (error) {
      this.logger.error(
        `Failed to set recharge plan for org: ${organizationId}`,
        error,
      );
      if (error instanceof HttpException) throw error;
      // Handle prisma error
      if (error instanceof PrismaClientKnownRequestError) {
        switch (error.code) {
          case 'P2003':
            throw new NotFoundException(
              `Organization with ID ${organizationId} not found`,
            );
          default:
            this.logger.error(
              `Unhandled Prisma error code: ${error.code}`,
              error,
            );
            throw new InternalServerErrorException('Database operation failed');
        }
      }
      throw new InternalServerErrorException('Failed to set recharge plan');
    }
  }

  async topUpWallet(user: User, topUpWalletDto: TopUpWalletDto) {
    const { organizationId, id: userId } = user;

    try {
      // 1. Get organization wallet
      const organization = await this.prisma.client.organization.findUnique({
        where: { id: organizationId },
        select: {
          wallet: { select: { id: true } },
        },
      });
      if (!organization?.wallet) {
        throw new NotFoundException('Organization or wallet not found');
      }

      // 2. Create a payment
      const newPayment = await this.paymentsService.createPayment({
        organizationId,
        userId,
        type: topUpWalletDto.paymentType,
        amount: topUpWalletDto.amount,
        fee: topUpWalletDto.fee,
        paymentMethodId: topUpWalletDto.paymentMethodId,
      });

      let response: TopUpWalletResponse = {
        paymentStatus: newPayment.status,
        payment: newPayment,
      };

      // 3. Create a wallet transaction if payment is completed
      if (newPayment.status === PaymentStatus.completed) {
        const result = await this.walletTransactionService.createTransaction({
          walletId: organization.wallet.id,
          paymentId: newPayment.id,
          type: WalletTransactionType.topup_payment,
          category: WalletTransactionCategory.settled,
          amount: newPayment.amount,
          description: newPayment.description ?? undefined,
        });
        Object.assign(response, {
          wallet: result.wallet,
          transaction: result.transaction,
          message: 'Money has been added to your wallet',
        });
      } else {
        response.message = 'Top-up request awaiting approval';
      }

      return response;
    } catch (error) {
      this.logger.error(
        `Failed to top up wallet. Payment type: ${topUpWalletDto.paymentType}`,
        error,
      );

      if (error instanceof HttpException) throw error;

      throw new InternalServerErrorException(`Failed to top up wallet.`);
    }
  }

  async submitWireTransfer(
    user: User,
    submitWireTransferDto: SubmitWireTransferDto,
    files: FilesToUpload[],
  ) {
    const { organizationId, id: userId } = user;

    let uploadedImages: UploadedFile[] | null = null;

    try {
      // 1. Upload transfer confirmation documents
      const keyPrefix = `wire-transfers/${organizationId}`;
      uploadedImages = await this.storageService.uploadFiles(files, keyPrefix);

      // 2. Create payment
      const newPayment = await this.paymentsService.createPayment({
        organizationId,
        userId,
        type: PaymentType.wire_transfer,
        amount: submitWireTransferDto.amount,
        metadata: {
          documents: uploadedImages.map((image) => image.url),
          submittedAmount: submitWireTransferDto.amount,
          wireTransferDate: submitWireTransferDto.date,
          bankReference: submitWireTransferDto.bankReference,
          bankName: submitWireTransferDto.bankName,
        },
      });
      return newPayment;
    } catch (error) {
      this.logger.error(
        `Failed to submit wire transfer for org: ${organizationId}`,
        error,
      );
      if (uploadedImages && uploadedImages.length > 0) {
        await Promise.all(
          uploadedImages.map((img) => this.storageService.deleteFile(img.key)),
        );
      }

      if (error instanceof HttpException) throw error;

      if (error instanceof PrismaClientKnownRequestError) {
        switch (error.code) {
          default:
            throw new InternalServerErrorException('Database error');
        }
      }

      throw new InternalServerErrorException('Failed to submit wire transfer');
    }
  }
}
