import { Body, Controller, Get, Post, Put, Query } from '@nestjs/common';
import { User } from '@repo/database';
import { CurrentUser } from 'src/common/decorators/user.decorator';
import { PaginationQueryDto } from 'src/common/dto/pagination-query.dto';
import { SetRechargePlanDto } from './dto/set-recharge-plan.dto';
import { TopUpWalletDto } from './dto/top-up-wallet.dto';
import { WalletService } from './wallet.service';
import { TypedMultipart } from 'src/common/decorators/typed-multipart.decorator';
import { SubmitWireTransferDto } from './dto/submit-wire-transfer.dto';
import { FilesToUpload } from 'src/common/types/upload-files';
import { WalletTransactionQueryDto } from './dto/wallet-transaction-query.dto';

@Controller('wallet')
export class WalletController {
  constructor(private readonly walletService: WalletService) {}

  @Get()
  findOne(
    @CurrentUser('organizationId') organizationId: string,
    @Query('include') include?: string,
  ) {
    const includes = include?.split(',') || [];
    return this.walletService.findOne(organizationId, includes);
  }

  @Get('transactions')
  getTransactions(
    @CurrentUser('organizationId') organizationId: string,
    @Query() query: WalletTransactionQueryDto,
  ) {
    return this.walletService.findAllWalletTransactions(organizationId, query);
  }

  @Get('recharge-plan')
  getRechargePlan(@CurrentUser('organizationId') organizationId: string) {
    return this.walletService.getRechargePlan(organizationId);
  }

  @Put('recharge-plan')
  setRechargePlan(
    @CurrentUser('organizationId') organizationId: string,
    @Body() setRechargePlanDto: SetRechargePlanDto,
  ) {
    return this.walletService.setRechargePlan(
      organizationId,
      setRechargePlanDto,
    );
  }

  @Post('top-up')
  topUpWallet(
    @CurrentUser() user: User,
    @Body() walletTopUpDto: TopUpWalletDto,
  ) {
    return this.walletService.topUpWallet(user, walletTopUpDto);
  }

  @Post('wire-transfer')
  submitWireTransfer(
    @CurrentUser() user: User,
    @(TypedMultipart<SubmitWireTransferDto>({
      minFiles: 1,
      allowedMimeTypes: ['image/jpeg', 'image/png', 'application/pdf'],
      maxFileSize: 10 * 1024 * 1024, // 10MB
    })(SubmitWireTransferDto))
    multipartData: {
      fields: SubmitWireTransferDto;
      files: FilesToUpload[];
    },
  ) {
    const { fields, files } = multipartData;
    return this.walletService.submitWireTransfer(user, fields, files);
  }
}
