import {
  PrismaClient,
  WalletTransactionCategory,
  WalletTransactionType,
} from '@repo/database';
import { Decimal } from 'node_modules/@repo/database/generated/prisma/runtime/library';

export interface CreateTransactionProps {
  walletId: string;
  paymentId?: string;
  type: WalletTransactionType;
  category: WalletTransactionCategory;
  amount: Decimal; // always positive
  referenceType?: string;
  referenceId?: string;
  description?: string;
}

export type Tx = Omit<
  PrismaClient,
  '$connect' | '$disconnect' | '$on' | '$transaction' | '$use' | '$extends'
>;
