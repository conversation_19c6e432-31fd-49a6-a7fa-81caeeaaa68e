import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import {
  Wallet,
  WalletTransaction,
  WalletTransactionCategory,
  WalletTransactionType,
} from '@repo/database';
import { PrismaService } from 'src/infrastructure/prisma/prisma.service';
import {
  CreateTransactionProps,
  Tx,
} from './types/create-transaction.interface';

@Injectable()
export class WalletTransactionService {
  private readonly CREDIT_TRANSACTION_TYPES: WalletTransactionType[] = [
    WalletTransactionType.topup_payment,
    WalletTransactionType.topup_auto,
    WalletTransactionType.refund_cancelled_label,
    WalletTransactionType.adjustment_credit,
  ];

  constructor(private readonly prisma: PrismaService) {}

  async createTransaction(
    props: CreateTransactionProps,
  ): Promise<{ wallet: Wallet; transaction: WalletTransaction }> {
    const transactionCategoryHandler = {
      [WalletTransactionCategory.settled]: this.settleTransaction.bind(this),
      [WalletTransactionCategory.reserved]: this.reserveFunds.bind(this),
      [WalletTransactionCategory.released]: this.releaseFunds.bind(this),
      [WalletTransactionCategory.returned]: this.returnFunds.bind(this),
    };

    const { walletId, category, amount } = props;

    if (amount.isZero() || amount.isNegative()) {
      throw new BadRequestException('Amount must be positive');
    }

    return await this.prisma.client.$transaction(async (tx) => {
      const wallet = await tx.wallet.findUnique({
        where: { id: walletId },
      });

      if (!wallet) throw new NotFoundException(`Wallet not found: ${walletId}`);

      const handlerFunction = transactionCategoryHandler[category];

      if (!handlerFunction) {
        throw new BadRequestException(`Unsupported category: ${category}`);
      }

      return await handlerFunction(tx, wallet, props);
    });
  }

  private async settleTransaction(
    tx: Tx,
    wallet: Wallet,
    props: CreateTransactionProps,
  ): Promise<{ wallet: Wallet; transaction: WalletTransaction }> {
    const {
      paymentId,
      type,
      category,
      amount,
      referenceType,
      referenceId,
      description,
    } = props;
    // balance => paid
    const balanceChange =
      this.getTransactionDirection(type) === 'credit' ? amount : amount.neg();
    const previousBalance = wallet.balance;
    const newBalance = previousBalance.add(balanceChange);

    const updatedWallet = {
      balance: newBalance,
    };

    const transactionData = {
      paymentId,
      type,
      category,
      balanceChange,
      previousBalance,
      newBalance,
      referenceType,
      referenceId,
      description,
    };

    return this.executeWalletUpdate(
      tx,
      wallet.id,
      updatedWallet,
      transactionData,
    );
  }

  private async reserveFunds(
    tx: Tx,
    wallet: Wallet,
    props: CreateTransactionProps,
  ): Promise<{ wallet: Wallet; transaction: WalletTransaction }> {
    const {
      paymentId,
      type,
      category,
      amount,
      referenceType,
      referenceId,
      description,
    } = props;
    // balance => reserved
    const balanceChange = amount.neg();
    const previousBalance = wallet.balance;
    const newBalance = previousBalance.add(balanceChange);

    const reservedChange = amount;
    const previousReserved = wallet.reserved;
    const newReserved = previousReserved.add(reservedChange);

    const updatedWallet = {
      balance: newBalance,
      reserved: newReserved,
    };

    const transactionData = {
      paymentId,
      type,
      category,
      balanceChange,
      previousBalance,
      newBalance,
      reservedChange,
      previousReserved,
      newReserved,
      referenceType,
      referenceId,
      description,
    };

    return this.executeWalletUpdate(
      tx,
      wallet.id,
      updatedWallet,
      transactionData,
    );
  }

  private async releaseFunds(
    tx: Tx,
    wallet: Wallet,
    props: CreateTransactionProps,
  ): Promise<{ wallet: Wallet; transaction: WalletTransaction }> {
    const {
      paymentId,
      type,
      category,
      amount,
      referenceType,
      referenceId,
      description,
    } = props;
    // reserved => paid
    const reservedChange = amount.neg();
    const previousReserved = wallet.reserved;
    const newReserved = previousReserved.add(reservedChange);

    const updatedWallet = {
      reserved: newReserved,
    };

    const transactionData = {
      paymentId,
      type,
      category,
      reservedChange,
      previousReserved,
      newReserved,
      referenceType,
      referenceId,
      description,
    };

    return this.executeWalletUpdate(
      tx,
      wallet.id,
      updatedWallet,
      transactionData,
    );
  }

  private async returnFunds(
    tx: Tx,
    wallet: Wallet,
    props: CreateTransactionProps,
  ): Promise<{ wallet: Wallet; transaction: WalletTransaction }> {
    const {
      paymentId,
      type,
      category,
      amount,
      referenceType,
      referenceId,
      description,
    } = props;
    // reserved => balance
    const reservedChange = amount.neg();
    const previousReserved = wallet.reserved;
    const newReserved = previousReserved.add(reservedChange);

    const balanceChange = amount;
    const previousBalance = wallet.balance;
    const newBalance = previousBalance.add(balanceChange);

    const updatedWallet = {
      balance: newBalance,
      reserved: newReserved,
    };

    const transactionData = {
      paymentId,
      type,
      category,
      balanceChange,
      previousBalance,
      newBalance,
      reservedChange,
      previousReserved,
      newReserved,
      referenceType,
      referenceId,
      description,
    };

    return this.executeWalletUpdate(
      tx,
      wallet.id,
      updatedWallet,
      transactionData,
    );
  }

  private async executeWalletUpdate(
    tx: Tx,
    walletId: string,
    walletUpdates: Partial<Pick<Wallet, 'balance' | 'reserved'>>,
    transactionData: Pick<WalletTransaction, 'type' | 'category'> &
      Partial<
        Omit<
          WalletTransaction,
          'id' | 'walletId' | 'createdAt' | 'updatedAt' | 'type' | 'category'
        >
      >,
  ) {
    const result = await tx.wallet.update({
      where: { id: walletId },
      data: {
        ...walletUpdates,
        transactions: { create: transactionData },
      },
      include: { transactions: { orderBy: { createdAt: 'desc' }, take: 1 } },
    });
    return { wallet: result, transaction: result.transactions[0] };
  }

  private getTransactionDirection(
    type: WalletTransactionType,
  ): 'credit' | 'debit' {
    return this.CREDIT_TRANSACTION_TYPES.includes(type) ? 'credit' : 'debit';
  }
}
