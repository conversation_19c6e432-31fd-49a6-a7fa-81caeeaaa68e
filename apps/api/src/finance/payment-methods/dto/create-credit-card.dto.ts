import { CreditCardBrand } from '@repo/database';
import {
  IsBoolean,
  IsDate,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Length,
  Max,
  Min,
} from 'class-validator';

export class CreateCreditCardDto {
  @IsString()
  @IsNotEmpty()
  organizationId: string;

  @IsString()
  @IsOptional()
  cardFingerprint?: string;

  @IsString()
  @IsNotEmpty()
  stripePaymentMethodId: string;

  @IsString()
  @Length(4, 4)
  cardLast4: string;

  @IsEnum(CreditCardBrand)
  @IsNotEmpty()
  cardBrand: CreditCardBrand;

  @IsNumber()
  @Min(1)
  @Max(12)
  cardExpMonth: number;

  @IsNumber()
  @Min(new Date().getFullYear())
  @Max(new Date().getFullYear() + 50)
  cardExpYear: number;

  @IsBoolean()
  @IsOptional()
  isDefault?: boolean;

  @IsString()
  @IsOptional()
  verifiedBy?: string;

  @IsString()
  @IsDate()
  @IsOptional()
  verifiedAt: Date;
}
