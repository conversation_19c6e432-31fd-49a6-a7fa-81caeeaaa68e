import {
  IsDate<PERSON><PERSON>,
  <PERSON>Enum,
  IsNotEmpty,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>Optional,
  IsString,
  Max,
  Min,
} from 'class-validator';
import { StringToNumber } from 'src/common/decorators/number-transform.decorator';

enum BankAccountType {
  checking = 'checking',
  saving = 'saving',
}

enum BankHolderType {
  personal = 'personal',
  business = 'business',
}

export class CreateAchDto {
  // CUSTOMER INFO
  @IsString()
  @IsNotEmpty()
  customerName: string;

  @IsString()
  @IsOptional()
  customerCompany?: string;

  @IsString()
  @IsNotEmpty()
  customerEmail: string;

  @IsString()
  @IsNotEmpty()
  customerPhone: string;

  @IsString()
  @IsNotEmpty()
  customerAddress: string;

  // BANK INFO
  @IsString()
  @IsNotEmpty()
  bankName: string;

  @IsString()
  @IsNotEmpty()
  bankAccountName: string;

  @IsString()
  @IsNotEmpty()
  bankAccountNo: string;

  @IsString()
  @IsNotEmpty()
  bankRoutingNo: string;

  @IsString()
  @IsNotEmpty()
  bankBillingAddress: string;

  @IsEnum(BankAccountType)
  @IsNotEmpty()
  bankAccountType: BankAccountType;

  @IsEnum(BankHolderType)
  @IsNotEmpty()
  bankHolderType: BankHolderType;

  // FUNDING INFO
  @StringToNumber()
  @IsNumber({ maxDecimalPlaces: 4 })
  @Min(50)
  @Max(999999)
  initialFunding: number;

  //   @StringToNumber()
  //   @IsNumber({ maxDecimalPlaces: 4 })
  //   @Min(0.01)
  //   @Max(999999)
  //   rechargeThreshold: number;

  //   @StringToNumber()
  //   @IsNumber({ maxDecimalPlaces: 4 })
  //   @Min(0.01)
  //   @Max(999999)
  //   rechargeAmount: number;

  // PAYMENT AUTHORIZATION INFO
  @IsDateString()
  @IsNotEmpty()
  authorizedAt: Date;
}
