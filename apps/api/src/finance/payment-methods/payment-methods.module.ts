import { Module } from '@nestjs/common';
import { PaymentMethodsService } from './payment-methods.service';
import { PaymentMethodsController } from './payment-methods.controller';
import { StorageModule } from 'src/infrastructure/storage/storage.module';
import { StripeModule } from 'src/infrastructure/stripe/stripe.module';

@Module({
  imports: [StorageModule, StripeModule],
  controllers: [PaymentMethodsController],
  providers: [PaymentMethodsService],
  exports: [PaymentMethodsService],
})
export class PaymentMethodsModule {}
