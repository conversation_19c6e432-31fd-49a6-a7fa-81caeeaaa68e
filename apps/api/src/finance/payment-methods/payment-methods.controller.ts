import { Controller, Delete, Get, Param, Post, Put } from '@nestjs/common';
import { PaymentMethodsService } from './payment-methods.service';
import { CurrentUser } from 'src/common/decorators/user.decorator';
import { CreateAchDto } from './dto/create-ach.dto';
import { TypedMultipart } from 'src/common/decorators/typed-multipart.decorator';
import { FilesToUpload } from 'src/common/types/upload-files';
import { User } from '@repo/database';

@Controller('payment-methods')
export class PaymentMethodsController {
  constructor(private readonly paymentMethodsService: PaymentMethodsService) {}

  @Get()
  getPaymentMethods(@CurrentUser('organizationId') organizationId: string) {
    return this.paymentMethodsService.findAll(organizationId);
  }

  @Post('/ach')
  createAch(
    @CurrentUser() user: User,
    @(TypedMultipart<CreateAchDto>()(CreateAchDto))
    multipartData: {
      fields: CreateAchDto;
      files: FilesToUpload[];
    },
  ) {
    const { fields, files } = multipartData;
    return this.paymentMethodsService.createAch(user, fields, files);
  }

  @Get('setup-intent')
  getSetupIntent(@CurrentUser() user: User) {
    return this.paymentMethodsService.getSetupIntent(user);
  }

  @Put(':id/default')
  setAsDefault(
    @CurrentUser('organizationId') organizationId: string,
    @Param('id') id: string,
  ) {
    return this.paymentMethodsService.setAsDefault(organizationId, id);
  }

  @Delete(':id')
  removePaymentMethod(
    @CurrentUser('organizationId') organizationId: string,
    @Param('id') id: string,
  ) {
    return this.paymentMethodsService.removePaymentMethod(organizationId, id);
  }
}
