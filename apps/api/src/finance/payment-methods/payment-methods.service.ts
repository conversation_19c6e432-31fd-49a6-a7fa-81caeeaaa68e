import {
  BadRequestException,
  HttpException,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import {
  PaymentMethodStatus,
  PaymentMethodType,
  PaymentStatus,
  PaymentType,
  ProcessingMethod,
  User,
} from '@repo/database';
import { omit } from 'lodash';
import { PrismaClientKnownRequestError } from 'node_modules/@repo/database/generated/prisma/runtime/library';
import {
  ACH_AUTHORIZATION_TEXT,
  ACH_AUTHORIZATION_VERSION,
} from 'src/common/constants/legal-text.constants';
import { FilesToUpload, UploadedFile } from 'src/common/types/upload-files';
import { PrismaService } from 'src/infrastructure/prisma/prisma.service';
import { StorageService } from 'src/infrastructure/storage/storage.service';
import { StripeService } from 'src/infrastructure/stripe/stripe.service';
import { CreateAchDto } from './dto/create-ach.dto';
import { CreateCreditCardDto } from './dto/create-credit-card.dto';

@Injectable()
export class PaymentMethodsService {
  private readonly logger = new Logger(PaymentMethodsService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly storageService: StorageService,
    private readonly stripeService: StripeService,
  ) {}

  async findAll(organizationId: string) {
    try {
      return await this.prisma.client.paymentMethod.findMany({
        where: { organizationId },
        orderBy: {
          createdAt: 'asc',
        },
      });
    } catch (error) {
      this.logger.error(
        `Failed to get payment methods for org: ${organizationId}`,
      );
      throw new InternalServerErrorException('Failed to get payment methods');
    }
  }

  async createAch(
    user: User,
    createAchDto: CreateAchDto,
    files: FilesToUpload[],
  ) {
    const { organizationId, id: userId } = user;

    let signatureImage: UploadedFile | null = null;

    try {
      await this.validateACHBankAccount(
        organizationId,
        createAchDto.bankAccountNo,
      );

      // Upload signature file
      const keyPrefix = `ach-signatures/${organizationId}`;
      const imageUpload = await this.storageService.uploadFiles(
        files,
        keyPrefix,
      );
      signatureImage = imageUpload[0];

      if (!signatureImage)
        throw new InternalServerErrorException(
          'Failed to upload signature file',
        );

      const [paymentMethod] = await this.prisma.client.$transaction([
        // 1. Create payment method and pending payment
        this.prisma.client.paymentMethod.create({
          data: {
            ...omit(createAchDto, [
              'initialFunding',
              'bankAccountNo', // needs to be encrypted
            ]),
            organizationId,
            type: PaymentMethodType.ach,
            status: PaymentMethodStatus.pending,
            bankLast4: createAchDto.bankAccountNo.slice(-4),
            bankAccountNo: createAchDto.bankAccountNo, // TODO: encrypt this,
            authorizationText: ACH_AUTHORIZATION_TEXT,
            authorizationVersion: ACH_AUTHORIZATION_VERSION,
            signatureFile: signatureImage.url,
            payment: {
              create: {
                organizationId,
                userId,
                type: PaymentType.ach,
                status: PaymentStatus.pending,
                processingMethod: ProcessingMethod.manual,
                amount: createAchDto.initialFunding,
                description: 'Initial funding and account verification',
                initiatedAt: new Date(),
              },
            },
          },
        }),
      ]);
      return {
        success: true,
        paymentMethodId: paymentMethod.id,
        message:
          'ACH account submitted for verification. Your initial funding will be processed to verify the account.',
      };
    } catch (error) {
      if (signatureImage) {
        await this.storageService.deleteFile(signatureImage.key);
      }

      this.logger.error(`Failed to create ACH payment method: ${error}`);
      if (error instanceof HttpException) throw error;
      throw new InternalServerErrorException('Failed to create ACH payment');
    }
  }

  async createCreditCard(createCreditCardDto: CreateCreditCardDto) {
    try {
      return await this.prisma.client.paymentMethod.create({
        data: {
          ...createCreditCardDto,
          type: PaymentMethodType.credit_card,
          status: PaymentMethodStatus.verified,
        },
      });
    } catch (error) {
      this.logger.error(
        `Failed to create credit card payment method: ${error}`,
      );
      // Handle Prisma error
      if (error instanceof PrismaClientKnownRequestError) {
        switch (error.code) {
          default:
            throw new InternalServerErrorException('Unknown database error');
        }
      }
      throw new InternalServerErrorException('Failed to create credit card');
    }
  }

  async getSetupIntent(user: User) {
    const { organizationId, email } = user;

    try {
      // 1. Check if organization has a stripe customer id
      const organization = await this.prisma.client.organization.findUnique({
        where: { id: organizationId },
        select: { id: true, stripeCustomerId: true, name: true },
      });

      if (!organization)
        throw new NotFoundException(
          `Could not find organization with id ${organizationId}`,
        );

      let stripeCustomerId: string;
      if (!organization.stripeCustomerId) {
        // Create new customer if doesn't already exist
        const stripeCustomer = await this.stripeService.createCustomer(
          organization.name,
          email,
        );
        // Update organization with new stripe customer id
        await this.prisma.client.organization.update({
          where: { id: organizationId },
          data: { stripeCustomerId: stripeCustomer.id },
        });
        stripeCustomerId = stripeCustomer.id;
      } else stripeCustomerId = organization.stripeCustomerId;

      // 2. Setup intent
      const setupIntent =
        await this.stripeService.createSetupIntent(stripeCustomerId);

      // return setupIntent;
      return { client_secret: setupIntent.client_secret };
    } catch (error) {
      this.logger.error(`Failed to get setup intent: ${error}`);
      if (error instanceof HttpException) throw error;
      throw new InternalServerErrorException('Failed to get setup intent');
    }
  }

  async setAsDefault(organizationId: string, id: string) {
    try {
      // 1. Verify if payment method is verified
      const paymentMethod = await this.prisma.client.paymentMethod.findUnique({
        where: { id },
      });
      if (paymentMethod?.status !== PaymentMethodStatus.verified) {
        throw new BadRequestException(
          'Payment method is not verified and cannot be set as default',
        );
      }

      // 2. Get existing default payment methods
      const existingDefaultPaymentMethods =
        await this.prisma.client.paymentMethod.findMany({
          where: { organizationId, isDefault: true },
        });
      // 3. Update default payment method(s) to false
      if (existingDefaultPaymentMethods.length > 0) {
        await this.prisma.client.paymentMethod.updateMany({
          where: { organizationId, isDefault: true },
          data: { isDefault: false },
        });
      }
      // 4. Update the current payment method default to true
      return await this.prisma.client.paymentMethod.update({
        where: { id },
        data: { isDefault: true },
      });
    } catch (error) {
      this.logger.error(`Failed to set payment method as default: ${error}`);
      if (error instanceof HttpException) throw error;

      // Prisma error
      if (error instanceof PrismaClientKnownRequestError) {
        switch (error.code) {
          default:
            this.logger.error(
              `Unhandled Prisma error code: ${error.code}`,
              error,
            );
            throw new InternalServerErrorException('Database operation failed');
        }
      }
      throw new InternalServerErrorException(
        'Failed to set payment method as default',
      );
    }
  }

  async removePaymentMethod(organizationId: string, id: string) {
    try {
      const paymentMethod = await this.prisma.client.paymentMethod.delete({
        where: { organizationId, id },
      });

      if (paymentMethod.stripePaymentMethodId) {
        await this.stripeService.detachPaymentMethod(
          paymentMethod.stripePaymentMethodId,
        );
      }
      this.logger.log(
        `Detached payment method ${paymentMethod.stripePaymentMethodId} from Stripe`,
      );

      if (paymentMethod.signatureFile) {
        const key = this.storageService.extractKeyFromUrl(
          paymentMethod.signatureFile,
        );
        if (key) await this.storageService.deleteFile(key);
      }
      // TODO: if credit card detach stripe payment method
      return {
        success: true,
        paymentMethodId: paymentMethod.id,
        message: 'Payment method removed successfully',
      };
    } catch (error) {
      this.logger.error(`Failed to remove payment method: ${error}`);
      // Prisma error
      if (error instanceof PrismaClientKnownRequestError) {
        switch (error.code) {
          case 'P2025':
            throw new NotFoundException('Payment method not found');
          default:
            this.logger.error(
              `Unhandled Prisma error code: ${error.code}`,
              error,
            );
            throw new InternalServerErrorException('Database operation failed');
        }
      }
      throw new InternalServerErrorException('Failed to delete payment method');
    }
  }

  private async validateACHBankAccount(
    organizationId: string,
    bankAccountNo: string,
  ) {
    const organizationWithSameAch =
      await this.prisma.client.paymentMethod.findFirst({
        where: { organizationId, bankAccountNo, type: PaymentMethodType.ach },
      });

    if (organizationWithSameAch) {
      throw new BadRequestException(
        'An ACH account with the same account number already exists',
      );
    }
  }
}
