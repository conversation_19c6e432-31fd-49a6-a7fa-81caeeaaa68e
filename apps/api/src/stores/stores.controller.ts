import { Controller, Get } from '@nestjs/common';
import { StoresService } from './stores.service';
import { CurrentUser } from 'src/common/decorators/user.decorator';

@Controller('stores')
export class StoresController {
  constructor(private readonly storesService: StoresService) {}

  @Get('/list')
  findAll(@CurrentUser('organizationId') organizationId: string) {
    return this.storesService.findAll(organizationId);
  }
}
