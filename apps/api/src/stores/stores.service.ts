import {
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { PrismaService } from 'src/infrastructure/prisma/prisma.service';

@Injectable()
export class StoresService {
  private readonly logger = new Logger(StoresService.name);

  constructor(private readonly prisma: PrismaService) {}

  async findAll(organizationId: string) {
    try {
      return this.prisma.client.store.findMany({
        where: { organizationId },
        select: {
          id: true,
          organizationId: true,
          name: true,
          provider: true,
          status: true,
          storeUrl: true,
        },
      });
    } catch (error) {
      this.logger.error(`Failed to retrieve stores: ${error.message}`);
      throw new InternalServerErrorException('Failed to retrieve stores');
    }
  }
}
