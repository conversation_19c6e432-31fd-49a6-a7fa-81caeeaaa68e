import {
  ArrayMaxSize,
  ArrayMinSize,
  IsArray,
  IsEmail,
  IsString,
} from 'class-validator';

export class CreateUserInviteDto {
  @IsArray()
  @ArrayMinSize(1, { message: 'At least one email is required' })
  @ArrayMaxSize(5, { message: 'Maximum 5 invitations at once' })
  @IsEmail({}, { each: true })
  emails: string[];

  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(1)
  permissionIds: string[];
}
