import {
  HttpException,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import {
  Prisma,
  UserInvite,
  UserInviteStatus,
  type User,
} from '@repo/database';
import { PrismaService } from 'src/infrastructure/prisma/prisma.service';
import { v4 as uuidv4 } from 'uuid';
import { CreateUserInviteDto } from './dto/create-user-invite.dto';
import { UpdateUserDto } from './dto/update-user.dto';

@Injectable()
export class UsersService {
  private readonly logger = new Logger(UsersService.name);

  constructor(private readonly prisma: PrismaService) {}

  // ✅
  async findAll(organizationId: string) {
    try {
      return await this.prisma.client.user.findMany({
        where: { organizationId },
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          status: true,
          image: true,
          createdAt: true,
          permissions: true,
        },
      });
    } catch (error) {
      this.logger.error(`Failed to retrieve users: ${error.message}`);
      throw new InternalServerErrorException('Failed to retrieve users');
    }
  }

  // ✅
  async updateUser(
    organizationId: string,
    id: string,
    updateUserDto: UpdateUserDto,
  ) {
    const { firstName, lastName, permissionIds, status } = updateUserDto;

    try {
      return await this.prisma.client.user.update({
        where: { organizationId, id },
        data: {
          firstName,
          lastName,
          status,
          permissions: permissionIds
            ? {
                deleteMany: {},
                createMany: {
                  data: permissionIds.map((permissionId) => ({ permissionId })),
                },
              }
            : {},
        },
        select: {
          id: true,
          lastName: true,
          firstName: true,
          permissions: true,
          status: true,
        },
      });
    } catch (error) {
      this.logger.error(
        `Failed to update user with ID ${id}: ${error.message}`,
      );

      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2025')
          throw new NotFoundException(`User with ID ${id} not found.`);
        // other prisma errors
      }

      throw new InternalServerErrorException('Failed to update user');
    }
  }

  // ✅
  async findAllPermissions() {
    try {
      return await this.prisma.client.permission.findMany();
    } catch (error) {
      this.logger.error(`Failed to retrieve permissions: ${error.message}`);
      throw new InternalServerErrorException('Failed to retrieve permissions');
    }
  }

  // ✅
  async createUserInvite(user: User, createUserInviteDto: CreateUserInviteDto) {
    try {
      const { emails, permissionIds } = createUserInviteDto;
      const { organizationId, id: invitedByUserId } = user;

      const results: UserInvite[] = [];
      const errors: { email: string; error: string }[] = [];

      for (const email of emails) {
        try {
          const existingUser = await this.prisma.client.user.findUnique({
            where: { email },
          });
          if (existingUser) {
            if (existingUser.organizationId === organizationId)
              errors.push({
                email,
                error: 'User already belongs to this organization',
              });
            else
              errors.push({
                email,
                error:
                  'User already exists but belongs to another organization',
              });
            continue;
          }

          const token = uuidv4();
          const expires = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);
          const permissionsData = permissionIds.map((permissionId) => ({
            permissionId,
          }));

          const response = await this.prisma.client.userInvite.upsert({
            where: { organizationId_email: { organizationId, email } },
            update: {
              token,
              expires,
              status: UserInviteStatus.pending,
              invitedByUserId,
              permissions: {
                createMany: {
                  data: permissionsData,
                },
              },
            },
            create: {
              email,
              organizationId,
              token,
              expires,
              invitedByUserId,
              permissions: {
                createMany: {
                  data: permissionsData,
                },
              },
            },
          });

          console.log({
            url: `${process.env.PUBLIC_URL}/auth/invite?token=${response.token}`,
          });

          results.push(response);
        } catch (error) {
          this.logger.error(
            `Failed to create user invite for ${email}: ${error.message}`,
          );
          errors.push({ email, error: error.message });
        }
      }

      return { results, errors };
    } catch (error) {
      this.logger.error(`Failed to create user invites: ${error.message}`);
      throw new InternalServerErrorException('Failed to create user invites');
    }
  }

  // ✅
  async findAllUserInvites(organizationId: string) {
    try {
      return await this.prisma.client.userInvite.findMany({
        where: { organizationId },
        include: { permissions: true },
      });
    } catch (error) {
      this.logger.error(`Failed to retrieve user invites: ${error.message}`);
      throw new InternalServerErrorException('Failed to retrieve user invites');
    }
  }

  // ✅
  async deleteUserInvite(organizationId: string, id: string) {
    try {
      return await this.prisma.client.userInvite.delete({
        where: { id, organizationId },
      });
    } catch (error) {
      this.logger.error(`Failed to delete user invite: ${error.message}`);

      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2025')
          throw new NotFoundException(`User invite with ID ${id} not found.`);
        // other prisma errors
      }
      throw new InternalServerErrorException('Failed to delete user invite');
    }
  }

  async findOne(id: string) {
    try {
      const user = await this.prisma.client.user.findUnique({
        where: { id },
      });

      if (!user) {
        throw new NotFoundException(`User with ID ${id} not found`);
      }
      return user;
    } catch (error) {
      this.logger.error(`Failed to retrieve user: ${error.message}`);

      if (error instanceof HttpException) {
        throw error; // Re-throw any HttpExceptions unchanged
      }
      throw new InternalServerErrorException('Failed to retrieve user');
    }
  }

  async findByEmail(email: string) {
    try {
      const user = await this.prisma.client.user.findUnique({
        where: { email },
      });

      if (!user) {
        throw new NotFoundException(`User with email ${email} not found`);
      }
      return user;
    } catch (error) {
      this.logger.error(`Failed to retrieve user by email: ${error.message}`);
      if (error instanceof HttpException) {
        throw error; // Re-throw any HttpExceptions unchanged
      }
      throw new InternalServerErrorException(
        'Failed to retrieve user by email',
      );
    }
  }
}
