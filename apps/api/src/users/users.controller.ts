import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
} from '@nestjs/common';
import { User } from '@repo/database';
import { Public } from 'src/common/decorators/public.decorator';
import { CurrentUser } from 'src/common/decorators/user.decorator';
import { CreateUserInviteDto } from './dto/create-user-invite.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { UsersService } from './users.service';

@Controller('users')
export class UsersController {
  constructor(private usersService: UsersService) {}

  // ✅
  @Get()
  findAll(@CurrentUser('organizationId') organizationId: string) {
    return this.usersService.findAll(organizationId);
  }

  // ✅
  @Put(':id')
  updateUser(
    @CurrentUser('organizationId') organizationId: string,
    @Param('id') id: string,
    @Body() updateUserDto: UpdateUserDto,
  ) {
    return this.usersService.updateUser(organizationId, id, updateUserDto);
  }

  // ✅
  @Get('permissions')
  findAllPermissions() {
    return this.usersService.findAllPermissions();
  }

  // ✅
  @Post('invite')
  createUserInvite(
    @CurrentUser() user: User,
    @Body() createUserInviteDto: CreateUserInviteDto,
  ) {
    return this.usersService.createUserInvite(user, createUserInviteDto);
  }

  // ✅
  @Get('invite')
  findAllUserInvites(@CurrentUser('organizationId') organizationId: string) {
    return this.usersService.findAllUserInvites(organizationId);
  }

  // ✅
  @Delete('invite/:id')
  deleteUserInvite(
    @CurrentUser('organizationId') organizationId: string,
    @Param('id') id: string,
  ) {
    return this.usersService.deleteUserInvite(organizationId, id);
  }

  // TODO: this will be handled in the auth controller login route instead. Remove once register/login route is implemented
  @Public()
  @Get('email/:email')
  findByEmail(@Param('email') email: string) {
    return this.usersService.findByEmail(email);
  }

  // TODO: this will be handled in the auth controller login route instead. Remove once register/login route is implemented
  @Public()
  @Get(':id')
  async getUser(@Param('id') id: string) {
    return this.usersService.findOne(id);
  }
}
