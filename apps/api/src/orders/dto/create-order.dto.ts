import { OrderStatus } from '@repo/database';
import { Type } from 'class-transformer';
import {
  ArrayMinSize,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { AddressDto } from 'src/common/dto/address.dto';
import { OrderItemDto } from 'src/common/dto/order-item.dto';
import { ParcelDto } from 'src/common/dto/parcel.dto';

export class FromAddressDto extends AddressDto {
  @IsString()
  @IsNotEmpty()
  id: string;

  @IsString()
  @IsNotEmpty()
  epAddressId: string;
}

export class CreateOrderDto {
  @IsString()
  @IsOptional()
  orderNo?: string;

  @IsString()
  @IsOptional()
  epShipmentId?: string;

  @IsString()
  @IsOptional()
  batchId?: string;

  @IsString()
  @IsOptional()
  storeId?: string;

  @IsString()
  @IsOptional()
  orderDate?: Date;

  @IsEnum(OrderStatus)
  @IsOptional()
  status?: OrderStatus;

  @IsString()
  @IsOptional()
  carrier?: string;

  @IsString()
  @IsOptional()
  service?: string;

  @IsString()
  @IsNotEmpty()
  fromAddressId: string;

  @ValidateNested()
  @Type(() => AddressDto)
  toAddress: AddressDto;

  @ValidateNested()
  @Type(() => OrderItemDto)
  @ArrayMinSize(1, { message: 'At least one item is required' })
  orderItems: OrderItemDto[];

  @IsString()
  @IsOptional()
  notes?: string;

  @ValidateNested()
  @Type(() => ParcelDto)
  parcel: ParcelDto;

  @IsNumber()
  @IsOptional()
  insurance?: number;

  @IsString()
  @IsOptional()
  signature?: string;
}
