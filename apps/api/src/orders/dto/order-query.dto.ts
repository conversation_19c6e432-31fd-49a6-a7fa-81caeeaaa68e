import { OrderStatus } from '@repo/database';
import { Type } from 'class-transformer';
import {
  IsEnum,
  IsObject,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { PaginationQueryDto } from 'src/common/dto/pagination-query.dto';

class BatchFilterDto {
  @IsString()
  @IsOptional()
  name?: string;
}

class OrderFilterDto {
  @IsEnum(OrderStatus)
  @IsOptional()
  status?: OrderStatus;

  @IsString()
  @IsOptional()
  orderNo?: string;

  @ValidateNested()
  @Type(() => BatchFilterDto)
  @IsOptional()
  batch?: BatchFilterDto;

  @IsObject()
  @IsOptional()
  toAddress?: {
    name?: string;
  };

  @IsString()
  @IsOptional()
  storeId?: string;
}

export class OrderQueryDto extends PaginationQueryDto {
  @ValidateNested()
  @Type(() => OrderFilterDto)
  @IsOptional()
  filter?: OrderFilterDto;

  @IsString()
  @IsOptional()
  search?: string;
}
