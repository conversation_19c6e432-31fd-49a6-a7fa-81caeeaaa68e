import {
  BadRequestException,
  HttpException,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import {
  CounterType,
  Order,
  OrderStatus,
  Prisma,
  Product,
  ShipmentStatus,
  User,
  WalletTransactionCategory,
  WalletTransactionType,
} from '@repo/database';
import {
  getCarrierAccountId,
  getCarrierBasicInfoByServiceId,
} from '@repo/shared-data';
import { plainToClass } from 'class-transformer';
import { format } from 'date-fns';
import { Decimal } from 'node_modules/@repo/database/generated/prisma/runtime/library';
import { AddressDto } from 'src/common/dto/address.dto';
import { OrderItemDto } from 'src/common/dto/order-item.dto';
import { ParcelDto } from 'src/common/dto/parcel.dto';
import { DEFAULT_STORE_ID } from 'src/constants/default-store';
import { CreateTransactionProps } from 'src/finance/wallet/types/create-transaction.interface';
import { WalletTransactionService } from 'src/finance/wallet/wallet-transaction.service';
import { EasypostService } from 'src/infrastructure/easypost/easypost.service';
import { PrismaService } from 'src/infrastructure/prisma/prisma.service';
import { RatesService } from 'src/rates/rates.service';
import { ShipmentsService } from 'src/shipments/shipments.service';
import { ObjectTransformer } from 'src/utils/object-transformer.util';
import { filterBuilder } from 'src/utils/queryTools.util';
import { AssignBatchDto } from './dto/assign-batch.dto';
import { CreateOrderDto } from './dto/create-order.dto';
import { OrderQueryDto } from './dto/order-query.dto';
import { PatchOrderDto } from './dto/patch-order.dto';
import { SplitOrderDto } from './dto/split-order.dto';
import { UpdateMultipleStatusDto } from './dto/update-multiple-status.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
import { UpdateStatusDto } from './dto/update-status.dto';

@Injectable()
export class OrdersService {
  private readonly logger = new Logger(OrdersService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly easypostService: EasypostService,
    private readonly shipmentsService: ShipmentsService,
    private readonly ratesService: RatesService,
    private readonly walletTransactionService: WalletTransactionService,
  ) {}

  // ✅
  async create(organizationId: string, createOrderDto: CreateOrderDto) {
    const { orderItems, ...rest } = createOrderDto;
    const result = await this.prisma.client.$transaction(async (tx) => {
      // Check if product exists and create if not
      const mappedOrderItems = await this.mapOrderItemsToProducts(
        tx,
        organizationId,
        orderItems,
      );

      // If orderNo is not provided, generate one
      const orderNo =
        createOrderDto.orderNo?.trim() === '' || !createOrderDto.orderNo
          ? await this.generateOrderNo(tx, organizationId)
          : createOrderDto.orderNo;

      const newOrder = await tx.order.create({
        data: {
          ...rest,
          orderNo,
          organizationId,
          storeId: DEFAULT_STORE_ID,
          toAddress: JSON.parse(JSON.stringify(createOrderDto.toAddress)),
          parcel: JSON.parse(JSON.stringify(createOrderDto.parcel)),
          orderItems: {
            createMany: {
              data: mappedOrderItems.map(({ product, quantity, price }) => ({
                price,
                quantity,
                productId: product.id,
              })),
            },
          },
        },
        include: { orderItems: true },
      });
      return newOrder;
    });
    return result;
  }

  // ✅
  async findAll(organizationId: string, query: OrderQueryDto) {
    try {
      const { limit, offset, filter, search } = query;

      // Quick search filter
      const allowedQuickSearchFields = ['orderNo', 'batch.name'];
      const addressAllowedJsonFields = [
        'name',
        'company',
        'email',
        'street1',
        'street2',
        'city',
        'state',
        'country',
        'zip',
      ];
      // Advance filter
      const fuzzyFields = ['orderNo', 'batch.name'];
      const jsonFields = ['toAddress.name'];

      const modifiedFilter = filterBuilder(filter ?? {}, {
        fuzzyFields,
        jsonFields,
      });

      const where: Prisma.OrderWhereInput = {
        organizationId,
        ...modifiedFilter,
      };

      if (search) {
        const searchConditions: Prisma.OrderWhereInput['OR'] = [];

        allowedQuickSearchFields.forEach((field) => {
          if (field === 'batch.name') {
            searchConditions.push({
              batch: { name: { contains: search, mode: 'insensitive' } },
            });
          } else
            searchConditions.push({
              [field]: { contains: search, mode: 'insensitive' },
            });
        });

        // Search in JSON fields - each field needs its own condition
        addressAllowedJsonFields.forEach((jsonField) => {
          searchConditions.push({
            toAddress: {
              path: [jsonField],
              string_contains: search,
              mode: 'insensitive',
            },
          });
        });
        where.OR = searchConditions;
      }

      const [data, count] = await Promise.all([
        this.prisma.client.order.findMany({
          where,
          orderBy: [{ createdAt: 'desc' }, { id: 'desc' }],
          take: limit,
          skip: offset ? offset : undefined,
          select: {
            id: true,
            orderNo: true,
            epShipmentId: true,
            orderDate: true,
            createdAt: true,
            batch: { select: { name: true } },
            toAddress: true,
            parcel: true,
            carrier: true,
            service: true,
            orderItems: {
              select: {
                id: true,
                price: true,
                quantity: true,
                product: {
                  select: { id: true, name: true, sku: true, imageUrl: true },
                },
              },
            },
            notes: true, // Maybe don't need
            store: { select: { name: true } },
            status: true,
          },
          // include: {
          //   batch: { select: { name: true } },
          //   // orderItems: { include: { product: true } },
          //   orderItems: {
          //     select: {
          //       id: true,
          //       price: true,
          //       quantity: true,
          //       product: {
          //         select: { id: true, name: true, sku: true, imageUrl: true },
          //       },
          //     },
          //   },
          //   store: { select: { name: true } },
          // },
        }),
        this.prisma.client.order.count({ where }),
      ]);

      return {
        data,
        pagination: {
          total: count,
          limit,
          offset: offset ? offset : 0,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to retrieve orders: ${error.message}`);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to retrieve orders');
    }
  }

  // ✅
  async findOne(organizationId: string, id: string) {
    try {
      const order = await this.prisma.client.order.findUnique({
        where: { id, organizationId },
        include: {
          batch: { select: { name: true } },
          orderItems: { include: { product: true } },
          fromAddress: true,
          shipment: true,
        },
      });

      if (!order) {
        throw new NotFoundException(`Order with ID ${id} not found`);
      }

      return order;
    } catch (error) {
      this.logger.error(`Failed to retrieve order: ${error.message}`);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to retrieve order');
    }
  }

  // ✅
  async update(
    organizationId: string,
    id: string,
    updateOrderDto: UpdateOrderDto,
  ) {
    try {
      const { orderItems, ...rest } = updateOrderDto;

      const result = await this.prisma.client.$transaction(async (tx) => {
        // If orderItems are provided, map them to products
        let updatedOrder: Order;
        if (orderItems) {
          const mappedOrderItems = await this.mapOrderItemsToProducts(
            tx,
            organizationId,
            orderItems,
          );
          updatedOrder = await tx.order.update({
            where: { id, organizationId },
            data: {
              ...rest,
              toAddress: JSON.parse(JSON.stringify(rest.toAddress)),
              parcel: JSON.parse(JSON.stringify(rest.parcel)),
              orderItems: {
                deleteMany: {},
                createMany: {
                  data: mappedOrderItems.map(
                    ({ product, quantity, price }) => ({
                      productId: product.id,
                      quantity,
                      price,
                    }),
                  ),
                },
              },
            },
            include: { orderItems: true },
          });
        } else {
          updatedOrder = await tx.order.update({
            where: { id, organizationId },
            data: {
              ...rest,
              toAddress: JSON.parse(JSON.stringify(rest.toAddress)),
              parcel: JSON.parse(JSON.stringify(rest.parcel)),
            },
            include: { orderItems: true },
          });
        }

        return updatedOrder;
      });
      return result;
    } catch (error) {
      this.logger.error(`Failed to update order: ${error.message}`);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to update order');
    }
  }

  // ✅
  async patchOrder(
    organizationId: string,
    id: string,
    patchOrderDto: PatchOrderDto,
  ) {
    try {
      return await this.prisma.client.order.update({
        where: { organizationId, id },
        data: patchOrderDto,
      });
    } catch (error) {
      this.logger.error(`Failed to patch order: ${error.message}`);
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        throw new NotFoundException(`Order with ID ${id} not found`);
      }
      throw new InternalServerErrorException('Failed to patch order');
    }
  }

  // ✅
  async assignBatch(organizationId: string, assignBatchDto: AssignBatchDto) {
    try {
      const { batchId, orderIds } = assignBatchDto;

      return await this.prisma.client.$transaction(async (tx) => {
        const batch = await tx.batch.findUnique({
          where: { organizationId, id: batchId },
        });

        if (!batch)
          throw new NotFoundException(`Batch with ID ${batchId} not found`);

        const existingOrders = await tx.order.findMany({
          where: {
            organizationId,
            id: { in: orderIds },
            status: OrderStatus.open,
          },
          select: { id: true, status: true },
        });

        // Check if all requested orders exist and are open
        if (existingOrders.length !== orderIds.length) {
          const foundIds = existingOrders.map((order) => order.id);
          const missingOrNonOpenIds = orderIds.filter(
            (id) => !foundIds.includes(id),
          );
          throw new NotFoundException(
            `Cannot assign batch to orders (not found or not open): ${missingOrNonOpenIds.join(', ')}`,
          );
        }

        // Update orders with batch assignment
        const updatedOrders = await tx.order.updateMany({
          where: {
            organizationId,
            id: { in: orderIds },
          },
          data: {
            batchId: batch.id,
          },
        });

        return updatedOrders;
      });
    } catch (error) {
      this.logger.error(`Failed to assign batch: ${error.message}`);
      if (error instanceof HttpException) throw error;

      throw new InternalServerErrorException('Failed to assign batch');
    }
  }

  // ✅
  async updateStatus(
    organizationId: string,
    id: string,
    updateStatusDto: UpdateStatusDto,
  ) {
    const { status } = updateStatusDto;

    try {
      this.validateStatusTransition(status);

      return await this.prisma.client.$transaction(async (tx) => {
        const existingOrder = await tx.order.findUniqueOrThrow({
          where: { organizationId, id },
        });

        if (existingOrder.status === OrderStatus.cancelled)
          throw new BadRequestException('Cannot update cancelled order status');

        if (
          status === OrderStatus.cancelled &&
          existingOrder.status !== OrderStatus.open
        )
          throw new BadRequestException('Only open orders can be cancelled');

        // Update the order
        return await tx.order.update({
          where: { id, organizationId },
          data: { status },
        });
      });
    } catch (error) {
      this.logger.error(`Failed to update order status: ${error.message}`);
      if (error instanceof HttpException) throw error;
      // Prisma error
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        switch (error.code) {
          case 'P2025':
            throw new NotFoundException(`Order with ID ${id} not found`);
          default:
            throw new InternalServerErrorException('Database error occurred');
        }
      }
      throw new InternalServerErrorException('Failed to update order status');
    }
  }

  // ✅
  async updateMultipleStatus(
    organizationId: string,
    updateMultipleStatusDto: UpdateMultipleStatusDto,
  ) {
    const { orderIds, status } = updateMultipleStatusDto;

    try {
      this.validateStatusTransition(status);

      return this.prisma.client.$transaction(async (tx) => {
        // Validate all orders exist and meet criteria
        const existingOrders = await tx.order.findMany({
          where: {
            organizationId,
            id: { in: orderIds },
            status: OrderStatus.open,
          },
          select: { id: true, status: true },
        });

        // Check if all orders exist
        if (existingOrders.length !== orderIds.length) {
          const foundIds = existingOrders.map((order) => order.id);
          const missingIds = orderIds.filter((id) => !foundIds.includes(id));
          throw new NotFoundException(
            `Cannot update orders (not found or not open): ${missingIds.join(', ')}`,
          );
        }

        // Update orders
        return await tx.order.updateMany({
          where: { id: { in: orderIds }, organizationId },
          data: { status },
        });
      });
    } catch (error) {
      this.logger.error(`Failed to update orders status: ${error.message}`);

      if (error instanceof HttpException) throw error;

      throw new InternalServerErrorException('Failed to update orders status');
    }
  }

  // ✅
  async splitOrder(
    organizationId: string,
    id: string,
    splitOrderDto: SplitOrderDto,
  ) {
    try {
      const oldOrder = await this.prisma.client.order.findUnique({
        where: { organizationId, id },
      });

      if (!oldOrder)
        throw new NotFoundException(`Invalid order id, could not split order`);

      const { originalOrderItems, newOrders } = splitOrderDto;

      /* 
      Prepare data for createMany child orders
      - Exclude orderItems for now, and create separately with createMany
      */
      const childOrders: Prisma.OrderCreateManyInput[] = newOrders.map(
        (_order, index) => ({
          organizationId,
          orderNo: oldOrder.orderNo + `-${index + 2}`,
          batchId: oldOrder.batchId,
          storeId: oldOrder.storeId,
          orderDate: oldOrder.orderDate,
          status: oldOrder.status,
          carrier: oldOrder.carrier,
          service: oldOrder.service,
          fromAddressId: oldOrder.fromAddressId,
          toAddress: JSON.parse(JSON.stringify(oldOrder.toAddress)),
          // Skip orderItems...
          notes: oldOrder.notes,
          parcel: JSON.parse(JSON.stringify(oldOrder.parcel)),
          insurance: oldOrder.insurance,
          signature: oldOrder.signature,
          parentOrderId: oldOrder.id,
        }),
      );

      /* 
      Update parent order
      - Update parent order with new orderNo, orderItems, and set isSplit to true
      */
      const result = await this.prisma.client.$transaction(async (tx) => {
        const updatedOrder = await tx.order.update({
          where: { organizationId, id },
          data: {
            orderNo: oldOrder.orderNo + '-1',
            isSplit: true,
            orderItems: {
              deleteMany: {},
              createMany: {
                data: originalOrderItems.map(
                  ({ product, quantity, price }) => ({
                    productId: product.id,
                    quantity,
                    price,
                  }),
                ),
              },
            },
          },
          include: { orderItems: true },
        });

        // Create child orders
        const newChildOrders = await tx.order.createManyAndReturn({
          data: childOrders,
        });

        // Prepare data for createMany child orders orderItems
        const childOrdersOrderItems: Prisma.OrderItemCreateManyInput[] =
          newChildOrders.flatMap((order, index) => {
            return newOrders[index].items.map(
              ({ product, quantity, price }) => ({
                orderId: order.id,
                productId: product.id,
                quantity,
                price,
              }),
            );
          });

        // Create child orders orderItems
        const newChildOrdersOrderItems = await tx.orderItem.createManyAndReturn(
          {
            data: childOrdersOrderItems,
          },
        );

        return {
          updatedOrder,
          newChildOrders: newChildOrders.map((order) => ({
            ...order,
            orderItems: newChildOrdersOrderItems.filter(
              (item) => item.orderId === order.id,
            ),
          })),
        };
      });

      return result;
    } catch (error) {
      this.logger.error(`Failed to split order: ${error.message}`);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to split order');
    }
  }

  // ✅
  async oneCallBuy(organizationId: string, id: string) {
    try {
      // 1. Get order and validate order
      const order = await this.prisma.client.order.findUnique({
        where: { id, organizationId },
        include: { fromAddress: { select: { id: true, epAddressId: true } } },
      });

      if (!order) throw new NotFoundException(`Order with ID ${id} not found`);

      if (order.status !== OrderStatus.open)
        throw new BadRequestException(
          `This order has already been processed or cancelled`,
        );

      // 2. Validate required order data
      const service = order.service;
      if (!service) throw new BadRequestException('Service is required');

      const fromAddressEpId = order.fromAddress.epAddressId;
      const toAddress = plainToClass(AddressDto, order.toAddress);
      const parcel = plainToClass(ParcelDto, order.parcel);
      const carrierId = getCarrierBasicInfoByServiceId(service)?.id;
      const carrierAccountId = getCarrierAccountId(carrierId);

      if (!fromAddressEpId || !toAddress || !parcel || !carrierAccountId)
        throw new BadRequestException('Invalid or insufficient order data');

      // 3. Create and buy shipment with EasyPost
      const epShipment = await this.easypostService.createShipment({
        from_address: { id: fromAddressEpId },
        to_address: toAddress,
        parcel,
        carrier_accounts: [carrierAccountId],
        service,
        options: {
          label_date: new Date().toISOString(), // Default add today's date
        },
        insurance: order.insurance?.toString() ?? undefined,
      });

      // 4. Update order status and create shipment
      const { updatedOrder, shipment } = await this.prisma.client.$transaction(
        async (tx) => {
          // Transform rate for pricing
          const transformedRate =
            await this.ratesService.getPricingAndTransformRate(
              organizationId,
              epShipment.selected_rate,
            );

          const shipment = await this.shipmentsService.createShipment(
            tx,
            organizationId,
            {
              orderId: order.id,
              epShipmentId: epShipment.id,
              status: epShipment.status as ShipmentStatus,
              fromAddressId: order.fromAddress.id,
              toAddress: ObjectTransformer.snakeToCamel(epShipment.to_address),
              returnAddress: ObjectTransformer.snakeToCamel(
                epShipment.return_address,
              ),
              parcel: ObjectTransformer.snakeToCamel(epShipment.parcel),
              carrier: epShipment.selected_rate.carrier,
              carrierAccountId: epShipment.selected_rate.carrier_account_id,
              service: epShipment.selected_rate.service,
              rate: transformedRate.rate,
              trackingCode: epShipment.tracking_code,
              shipDate: epShipment.options?.label_date ?? undefined,
              postageLabel: {
                labelUrl: epShipment.postage_label.label_url,
                labelPdfUrl: epShipment.postage_label.label_pdf_url,
                labelZplUrl: epShipment.postage_label.label_zpl_url,
              },
              insurance: epShipment.insurance as unknown as string, // inconsistent type
              fees: ObjectTransformer.snakeToCamel(epShipment.fees),
            },
          );

          const updatedOrder = await tx.order.update({
            where: { id, organizationId },
            data: {
              epShipmentId: epShipment.id,
              status: OrderStatus.processing,
            },
            include: {
              organization: { select: { walletId: true } },
            },
          });

          // 6. Create wallet transaction
          const transactionPayload: CreateTransactionProps = {
            walletId: updatedOrder.organization.walletId,
            type: WalletTransactionType.label_purchase,
            category: WalletTransactionCategory.settled,
            amount: new Decimal(transformedRate.rate),
            referenceType: 'label',
            referenceId: shipment.shipmentNo,
            description: `Purchased ${transformedRate.carrier} label for order ${order.orderNo}`,
          };

          await this.walletTransactionService.createTransaction(
            transactionPayload,
          );

          return { updatedOrder, shipment };
        },
      );

      return { order: updatedOrder, shipment };
    } catch (error) {
      this.logger.error(
        `Could not complete one call buy shipment: ${error.message || error}`,
      );
      if (error instanceof HttpException) throw error;

      throw new InternalServerErrorException(
        'Failed to process shipment purchase',
        error.message,
      );
    }
  }

  // ✅
  async generateRates(user: User, id: string) {
    const { organizationId } = user;

    try {
      const order = await this.prisma.client.order.findUnique({
        where: { id, organizationId },
        include: { fromAddress: { select: { epAddressId: true } } },
      });
      if (!order) throw new NotFoundException(`Order with ID ${id} not found`);

      const fromAddressId = order.fromAddressId;
      const toAddress = plainToClass(AddressDto, order.toAddress);
      const parcel = plainToClass(ParcelDto, order.parcel);

      const { epShipmentId, rates } = await this.ratesService.generateRates(
        user,
        {
          // fromAddressId,
          fromAddress: { id: fromAddressId },
          toAddress,
          parcel,
        },
      );

      const updatedOrder = await this.prisma.client.order.update({
        where: { id, organizationId },
        data: { epShipmentId },
      });

      return updatedOrder;
    } catch (error) {
      this.logger.error(`Failed to generate rates: ${error}`);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to generate rates');
    }
  }

  // ========== INTERNAL FUNCTIONS ==========
  async mapOrderItemsToProducts(
    tx: Prisma.TransactionClient,
    organizationId: string,
    orderItems: OrderItemDto[],
  ): Promise<{ product: Product; quantity: number; price: number }[]> {
    const orderItemsMapping: {
      product: Product;
      quantity: number;
      price: number;
    }[] = [];

    for (const item of orderItems) {
      let product: Product | undefined;

      // Check if product ID is provided
      if (item.product.id) {
        const existingProduct = await tx.product.findUnique({
          where: { id: item.product.id },
        });
        if (existingProduct) product = existingProduct;
      }

      // Check for product by sku
      if (!product) {
        const existingProduct = await tx.product.findFirst({
          where: { organizationId, sku: item.product.sku },
        });
        if (existingProduct) product = existingProduct;
      }

      // Create product if not found
      if (!product) {
        product = await tx.product.create({
          data: {
            organizationId,
            sku: item.product.sku,
            name: item.product.name,
            basePrice: item.price,
          },
        });
      }

      orderItemsMapping.push({
        product,
        quantity: item.quantity,
        price: item.price,
      });
    }
    return orderItemsMapping;
  }

  async generateOrderNo(tx: Prisma.TransactionClient, organizationId: string) {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const dateStr = format(today, 'MMddyyyy');
    const prefix = `ORD-${dateStr}-`;

    const counterRecord = await tx.counter.upsert({
      where: {
        organizationId_type_date: {
          organizationId,
          type: CounterType.order,
          date: today,
        },
      },
      update: { count: { increment: 1 } },
      create: {
        organizationId,
        type: CounterType.order,
        date: today,
        count: 1,
      },
    });

    const sequenceStr = counterRecord.count.toString().padStart(5, '0');
    return prefix + sequenceStr;
  }

  private validateStatusTransition(status: OrderStatus) {
    if (status === OrderStatus.processing || status === OrderStatus.shipped) {
      throw new BadRequestException(
        `Updating order status to ${status} is not allowed`,
      );
    }
  }
}
