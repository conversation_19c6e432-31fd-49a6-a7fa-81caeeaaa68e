import { Module } from '@nestjs/common';
import { ShipmentsModule } from 'src/shipments/shipments.module';
import { OrdersController } from './orders.controller';
import { OrdersService } from './orders.service';
import { EasypostModule } from 'src/infrastructure/easypost/easypost.module';
import { RatesModule } from 'src/rates/rates.module';
import { WalletModule } from 'src/finance/wallet/wallet.module';

@Module({
  imports: [EasypostModule, ShipmentsModule, RatesModule, WalletModule],
  controllers: [OrdersController],
  providers: [OrdersService],
  exports: [OrdersService],
})
export class OrdersModule {}
