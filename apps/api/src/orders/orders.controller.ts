import {
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { User } from '@repo/database';
import { CurrentUser } from 'src/common/decorators/user.decorator';
import { AssignBatchDto } from './dto/assign-batch.dto';
import { CreateOrderDto } from './dto/create-order.dto';
import { OrderQueryDto } from './dto/order-query.dto';
import { SplitOrderDto } from './dto/split-order.dto';
import { UpdateMultipleStatusDto } from './dto/update-multiple-status.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
import { UpdateStatusDto } from './dto/update-status.dto';
import { OrdersService } from './orders.service';
import { PatchOrderDto } from './dto/patch-order.dto';

@Controller('orders')
export class OrdersController {
  constructor(private readonly ordersService: OrdersService) {}

  // ✅
  @Post()
  create(
    @CurrentUser('organizationId') organizationId: string,
    @Body() createOrderDto: CreateOrderDto,
  ) {
    console.log(createOrderDto);
    return this.ordersService.create(organizationId, createOrderDto);
  }

  // ✅
  @Get()
  findAll(
    @Query() query: OrderQueryDto,
    @CurrentUser('organizationId') organizationId: string,
  ) {
    return this.ordersService.findAll(organizationId, query);
  }

  // ✅
  @Patch('assign-batch')
  assignBatch(
    @CurrentUser('organizationId') organizationId: string,
    @Body() assignBatchDto: AssignBatchDto,
  ) {
    return this.ordersService.assignBatch(organizationId, assignBatchDto);
  }

  // ✅
  @Patch('status')
  updateMultipleStatus(
    @CurrentUser('organizationId') organizationId: string,
    @Body() updateMultipleStatusDto: UpdateMultipleStatusDto,
  ) {
    return this.ordersService.updateMultipleStatus(
      organizationId,
      updateMultipleStatusDto,
    );
  }

  // ✅
  @Get(':id')
  findOne(
    @CurrentUser('organizationId') organizationId: string,
    @Param('id') id: string,
  ) {
    return this.ordersService.findOne(organizationId, id);
  }

  // ✅
  @Put(':id')
  update(
    @CurrentUser('organizationId') organizationId: string,
    @Param('id') id: string,
    @Body() updateOrderDto: UpdateOrderDto,
  ) {
    return this.ordersService.update(organizationId, id, updateOrderDto);
  }

  // ✅
  @Patch(':id')
  patchOrder(
    @CurrentUser('organizationId') organizationId: string,
    @Param('id') id: string,
    @Body() patchOrderDto: PatchOrderDto,
  ) {
    return this.ordersService.patchOrder(organizationId, id, patchOrderDto);
  }

  // ✅
  @Post(':id/split')
  splitOrder(
    @CurrentUser('organizationId') organizationId: string,
    @Param('id') id: string,
    @Body() splitOrderDto: SplitOrderDto,
  ) {
    return splitOrderDto;

    return this.ordersService.splitOrder(organizationId, id, splitOrderDto);
  }

  // ✅
  @Patch(':id/status')
  updateStatus(
    @CurrentUser('organizationId') organizationId: string,
    @Param('id') id: string,
    @Body() updateStatusDto: UpdateStatusDto,
  ) {
    return this.ordersService.updateStatus(organizationId, id, updateStatusDto);
  }

  // ✅
  @Post(':id/one-call-buy')
  oneCallBuy(
    @CurrentUser('organizationId') organizationId: string,
    @Param('id') id: string,
  ) {
    return this.ordersService.oneCallBuy(organizationId, id);
  }

  // ✅
  @Get(':id/generate-rates')
  generateRates(@CurrentUser() user: User, @Param('id') id: string) {
    return this.ordersService.generateRates(user, id);
  }

  @Get(':id/fulfillment')
  getFulfillment(
    @CurrentUser('organizationId') organizationId: string,
    @Param('id') id: string,
  ) {
    return { organizationId, id };
  }
}
