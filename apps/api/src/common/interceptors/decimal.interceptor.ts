import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Prisma } from '@repo/database';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

@Injectable()
export class DecimalInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    return next.handle().pipe(
      map((data) => {
        // Apply transformation logic here
        if (data && typeof data === 'object') {
          this.transformDecimals(data);
        }
        return data;
      }),
    );
  }

  private transformDecimals(obj: any) {
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        if (obj[key] instanceof Prisma.Decimal) {
          obj[key] = obj[key].toFixed(2);
        } else if (typeof obj[key] === 'object' && obj[key] !== null) {
          this.transformDecimals(obj[key]); // Recursively transform nested objects
        }
      }
    }
  }
}
