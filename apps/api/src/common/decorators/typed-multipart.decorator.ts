import {
  BadRequestException,
  createParamDecorator,
  ExecutionContext,
  Type,
} from '@nestjs/common';
import { plainToClass } from 'class-transformer';
import { validate } from 'class-validator';
import { FastifyRequest } from 'fastify';
import { FilesToUpload } from '../types/upload-files';

export interface FileValidationOptions {
  minFiles?: number;
  maxFiles?: number;
  allowedMimeTypes?: string[];
  maxFileSize?: number; // in bytes
}

export const TypedMultipart = <T extends object>(
  fileValidationOptions?: FileValidationOptions,
) => {
  return createParamDecorator(
    (
      dtoClass: Type<T>,
      ctx: ExecutionContext,
    ): Promise<{ fields: T; files: FilesToUpload[] }> => {
      const request = ctx.switchToHttp().getRequest<FastifyRequest>();

      const fields: Record<string, any> = {};
      const files: FilesToUpload[] = [];

      return new Promise(async (resolve, reject) => {
        try {
          const parts = request.parts();

          for await (const part of parts) {
            if (part.type === 'field') {
              if (part.value === 'true' || part.value === 'false') {
                fields[part.fieldname] = part.value === 'true';
              } else {
                fields[part.fieldname] = part.value;
              }
            } else if (part.type === 'file') {
              const buffer = await part.toBuffer();

              // File size validation
              if (
                fileValidationOptions?.maxFileSize &&
                buffer.length > fileValidationOptions.maxFileSize
              ) {
                throw new BadRequestException({
                  message: 'File too large',
                  errors: [
                    {
                      field: part.fieldname,
                      constraints: {
                        maxSize: `File size must be less than ${fileValidationOptions.maxFileSize} bytes`,
                      },
                    },
                  ],
                });
              }

              // MIME type validation
              if (
                fileValidationOptions?.allowedMimeTypes &&
                !fileValidationOptions.allowedMimeTypes.includes(part.mimetype)
              ) {
                throw new BadRequestException({
                  message: 'Invalid file type',
                  errors: [
                    {
                      field: part.fieldname,
                      constraints: {
                        fileType: `Only ${fileValidationOptions.allowedMimeTypes.join(', ')} files are allowed`,
                      },
                    },
                  ],
                });
              }

              files.push({
                fieldname: part.fieldname,
                originalname: part.filename,
                encoding: part.encoding,
                mimetype: part.mimetype,
                buffer,
                size: buffer.length,
              });
            }
          }

          // File count validation
          if (
            fileValidationOptions?.minFiles &&
            files.length < fileValidationOptions.minFiles
          ) {
            throw new BadRequestException({
              message: 'Insufficient files',
              errors: [
                {
                  field: 'files',
                  constraints: {
                    minFiles: `At least ${fileValidationOptions.minFiles} file(s) are required`,
                  },
                },
              ],
            });
          }

          const dto = plainToClass(dtoClass, fields);
          const errors = await validate(dto, {
            whitelist: true,
          });
          if (errors.length > 0) {
            throw new BadRequestException({
              message: 'Validation failed',
              errors: errors.map((error) => ({
                field: error.property,
                constraints: error.constraints,
              })),
            });
          }

          resolve({ fields: dto, files });
        } catch (error) {
          reject(error);
        }
      });
    },
  );
};
