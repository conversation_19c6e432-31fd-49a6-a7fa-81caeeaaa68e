import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { FastifyRequest } from 'fastify';
import { FilesToUpload } from '../types/upload-files';

export const File = createParamDecorator(
  async (
    _data: unknown,
    ctx: ExecutionContext,
  ): Promise<FilesToUpload | null> => {
    const request = ctx.switchToHttp().getRequest() as FastifyRequest;
    try {
      const data = await request.file();

      if (!data) return null;

      const buffer = await data.toBuffer();

      return {
        fieldname: data.fieldname,
        originalname: data.filename,
        encoding: data.encoding,
        mimetype: data.mimetype,
        buffer,
        size: buffer.length,
      };
    } catch (error) {
      return null;
    }
  },
);
