import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { FastifyRequest } from 'fastify';
import { FilesToUpload } from '../types/upload-files';

export const Files = createParamDecorator(
  async (_data: unknown, ctx: ExecutionContext): Promise<FilesToUpload[]> => {
    const request = ctx.switchToHttp().getRequest<FastifyRequest>();
    const files: FilesToUpload[] = [];

    try {
      const parts = request.parts();

      for await (const part of parts) {
        // console.log(part);
        if (part.type === 'file') {
          const buffer = await part.toBuffer();

          files.push({
            fieldname: part.fieldname,
            originalname: part.filename,
            encoding: part.encoding,
            mimetype: part.mimetype,
            buffer,
            size: buffer.length,
          });
        }
      }

      return files;
    } catch (error) {
      return [];
    }
  },
);
