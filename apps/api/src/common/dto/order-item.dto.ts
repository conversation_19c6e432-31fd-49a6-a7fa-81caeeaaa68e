import { Type } from 'class-transformer';
import {
  IsNotEmpty,
  <PERSON>N<PERSON>ber,
  IsOptional,
  IsString,
  Min,
  ValidateNested,
} from 'class-validator';

// export class OrderItemDto {
//   @IsString()
//   @IsNotEmpty()
//   sku: string;

//   @IsString()
//   @IsOptional()
//   id?: string;

//   @IsString()
//   @IsOptional()
//   storeId?: string;

//   @IsString()
//   @IsNotEmpty()
//   name: string;

//   @IsNumber()
//   @Min(1)
//   quantity: number;

//   @IsNumber()
//   @Min(0)
//   price: number;
// }

class ProductDto {
  @IsString()
  @IsOptional()
  id?: string;

  @IsString()
  @IsNotEmpty()
  sku: string;

  @IsString()
  @IsNotEmpty()
  name: string;
}

export class OrderItemDto {
  @ValidateNested()
  @Type(() => ProductDto)
  product: ProductDto;

  @IsNumber()
  @Min(1)
  quantity: number;

  @IsNumber()
  @Min(0)
  price: number;
}
