import { Type } from 'class-transformer';
import { IsNumber, IsOptional, IsPositive, IsString } from 'class-validator';

export class ParcelDto {
  @IsString()
  @IsOptional()
  id?: string;

  @IsNumber({ maxDecimalPlaces: 2 })
  @IsOptional()
  // @IsPositive({ message: 'Length must be greater than 0' })
  @Type(() => Number)
  length?: number;

  @IsNumber({ maxDecimalPlaces: 2 })
  @IsOptional()
  // @IsPositive({ message: 'Width must be greater than 0' })
  @Type(() => Number)
  width?: number;

  @IsNumber({ maxDecimalPlaces: 2 })
  @IsOptional()
  // @IsPositive({ message: 'Height must be greater than 0' })
  @Type(() => Number)
  height?: number;

  @IsNumber({ maxDecimalPlaces: 2 })
  @IsPositive({ message: 'Weight must be greater than 0' })
  @Type(() => Number)
  weight: number;

  @IsString()
  @IsOptional()
  predefinedPackage?: string;
}
