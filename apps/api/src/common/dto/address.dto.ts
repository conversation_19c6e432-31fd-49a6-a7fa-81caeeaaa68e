import {
  Is<PERSON><PERSON>,
  <PERSON><PERSON>otEmpt<PERSON>,
  <PERSON><PERSON><PERSON>al,
  IsEmail,
  IsBoolean,
} from 'class-validator';

export class AddressDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsOptional()
  company?: string;

  @IsString()
  @IsNotEmpty()
  street1: string;

  @IsString()
  @IsOptional()
  street2?: string;

  @IsString()
  @IsNotEmpty()
  city: string;

  @IsString()
  @IsNotEmpty()
  state: string;

  @IsString()
  @IsNotEmpty()
  country: string;

  @IsString()
  @IsNotEmpty()
  zip: string;

  @IsString()
  @IsNotEmpty()
  phone: string;

  @IsEmail()
  @IsNotEmpty()
  email: string;

  @IsBoolean()
  @IsOptional()
  residential?: boolean = false;

  @IsBoolean()
  @IsOptional()
  verified?: boolean = false;
}
