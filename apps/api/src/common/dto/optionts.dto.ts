import { IsOptional, IsString } from 'class-validator';

export class OptionsDto {
  @IsString()
  @IsOptional()
  delivery_confirmation?:
    | 'SIGNATURE'
    | 'NO_SIGNATURE'
    | 'ADULT_SIGNATURE'
    | 'INDIRECT_SIGNATURE'
    | 'SERVICE_DEFAULT'
    | 'ADULT_SIGNATURE_RESTRICTED'
    | 'SIGNATURE_RESTRICTED'
    | 'DO_NOT_SAFE_DROP'
    | 'STANDARD_SIGNATURE';

  @IsString()
  @IsOptional()
  label_date?: string;
}
