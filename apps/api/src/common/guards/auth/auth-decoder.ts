export interface AuthJwtPayload {
  sub: string; // User ID
  name?: string;
  email?: string;
  role?: string;
  organizationId?: string;
  exp?: number; // Expiration time
  iat?: number; // Issued at time
  jti?: string; // JWT ID
  [key: string]: any; // Allow other properties
}

/**
 * Decodes and verifies an Auth.js JWT token
 * @param token The encrypted JWT token string
 * @returns The decoded token payload or null if invalid
 */

export async function decodeJwt(token: string): Promise<AuthJwtPayload | null> {
  if (!token || typeof token !== 'string') return null;

  const AUTH_SECRET = process.env.AUTH_SECRET;
  if (!AUTH_SECRET) {
    console.error('Missing environment variable: AUTH_SECRET');
    return null;
  }

  try {
    // Dynamically import jose because of ESM compatibility issues
    const jose = await import('jose');

    // Get encryption key
    const encryptionKey = await getDerivedEncryptionKey();

    // Decrypt the token
    const { plaintext } = await jose.compactDecrypt(token, encryptionKey);

    // Parse the payload
    const decodedPayload = JSON.parse(new TextDecoder().decode(plaintext));

    if (
      !decodedPayload ||
      typeof decodedPayload !== 'object' ||
      !decodedPayload.sub
    ) {
      console.error('Invalid token payload: Missing required fields');
      return null;
    }

    if (decodedPayload.exp && Date.now() >= decodedPayload.exp * 1000) {
      console.error('Token has expired');
      return null;
    }

    return decodedPayload;
  } catch (error) {
    if (error instanceof Error)
      console.error(`JWT decoding error: ${error.name}: ${error.message}`);
    else console.error('Failed to decode token:', error);
    return null;
  }
}

/**
 * Derives the encryption key using Auth.js key derivation parameters
 * @returns A Uint8Array containing the derived key
 */
async function getDerivedEncryptionKey(): Promise<Uint8Array> {
  const { hkdf } = await import('@panva/hkdf');

  const AUTH_SECRET = process.env.AUTH_SECRET;
  if (!AUTH_SECRET) {
    console.error('Missing environment variable: AUTH_SECRET');
    throw new Error('Missing environment variable: AUTH_SECRET');
  }

  const AUTH_COOKIE_NAME =
    process.env.NODE_ENV === 'production'
      ? '__Secure-authjs.session-token' // HTTPS production
      : 'authjs.session-token'; // HTTP local

  return await hkdf(
    'sha256', // Hash algorithm
    AUTH_SECRET, // Secret key material
    AUTH_COOKIE_NAME, // Salt = session cookie name
    `Auth.js Generated Encryption Key (${AUTH_COOKIE_NAME})`, // Info string
    64, // 64 bytes (512 bits) for A256CBC-HS512
  );
}
