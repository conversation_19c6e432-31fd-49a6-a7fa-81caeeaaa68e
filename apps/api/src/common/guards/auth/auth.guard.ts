import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { decodeJwt } from './auth-decoder';

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // Check for public routes
    const isPublic = this.reflector.getAllAndOverride<boolean>('isPublic', [
      context.getHandler(),
      context.getClass(),
    ]);
    if (isPublic) return true;

    const request = context.switchToHttp().getRequest();
    const { authorization } = request.headers;

    if (!authorization || !authorization.startsWith('Bearer '))
      throw new UnauthorizedException({
        error: {
          name: 'USER.UNAUTHORIZED',
          message: 'Please provide a valid access token',
        },
      });

    const token = authorization.split(' ')[1];

    const payload = await decodeJwt(token);

    if (!payload) {
      throw new UnauthorizedException({
        error: {
          name: 'USER.INVALID_TOKEN',
          message: 'Invalid or expired token',
        },
      });
    }

    request.user = {
      id: payload.sub,
      email: payload.email,
      name: payload.name,
      organizationId: payload.organizationId,
    };

    return true;
  }
}
