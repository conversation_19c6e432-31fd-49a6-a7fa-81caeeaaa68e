import { ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import {
  FastifyAdapter,
  NestFastifyApplication,
} from '@nestjs/platform-fastify';
import { AppModule } from './app.module';
import qs from 'qs';
import fastifyMultipart from '@fastify/multipart';
import { DecimalInterceptor } from './common/interceptors/decimal.interceptor';

const corsOptions = {
  origin: [process.env.PUBLIC_URL ?? 'http://localhost:3000'],
  methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
  allowedHeaders: 'Content-Type, Authorization',
  credentials: true,
  maxAge: 86400,
};

async function bootstrap() {
  const FastifyModule = new FastifyAdapter({
    querystringParser: (str) => qs.parse(str),
  });
  FastifyModule.enableCors(corsOptions);

  const app = await NestFactory.create<NestFastifyApplication>(
    AppModule,
    FastifyModule,
    { rawBody: true },
  );

  // Register Fastify Multipart
  await app.register(fastifyMultipart, {
    limits: {
      fieldNameSize: 100,
      fieldSize: 1024 * 1024,
      fields: 25,
      fileSize: 10 * 1024 * 1024, // 10MB
      files: 5,
      headerPairs: 2000,
    },
  });

  app.useGlobalInterceptors(new DecimalInterceptor());

  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
    }),
  );

  const port = process.env.PORT ?? 3001;
  await app.listen(port, '0.0.0.0');
  console.log(`🚀 API server is running on port ${port}`);
}
bootstrap();
