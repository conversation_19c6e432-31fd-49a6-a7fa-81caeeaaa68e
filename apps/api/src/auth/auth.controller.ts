import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
} from '@nestjs/common';
import { Public } from 'src/common/decorators/public.decorator';
import { AuthService } from './auth.service';
import { AcceptInviteDto } from './dto/accept-invite.dto';
import { ForgotPasswordDto } from './dto/forgot-password.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { RegisterDto } from './dto/register.dto';
import { ResendVerificationTokenDto } from './dto/resend-verification-token.dto';

@Public()
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  // ✅
  @Post('register')
  register(@Body() registerDto: RegisterDto) {
    return this.authService.register(registerDto);
  }

  // ✅
  @Post('verification-token/resend')
  resendVerificationToken(
    @Body() resendVerificationTokenDto: ResendVerificationTokenDto,
  ) {
    return this.authService.resendVerificationToken(resendVerificationTokenDto);
  }

  // ✅
  @Post('verification-token/:token')
  @HttpCode(HttpStatus.OK)
  verifyVerificationToken(@Param('token') token: string) {
    return this.authService.verifyVerificationToken(token);
  }

  // ✅
  @Post('accept-invite')
  acceptInvite(@Body() acceptInviteDto: AcceptInviteDto) {
    return this.authService.acceptInvite(acceptInviteDto);
  }

  // ✅
  @Get('user-invite/:token')
  findOneUserInvite(@Param('token') token: string) {
    return this.authService.findOneUserInvite(token);
  }

  // ✅
  @Post('forgot-password')
  @HttpCode(HttpStatus.OK)
  forgotPassword(@Body() forgotPasswordDto: ForgotPasswordDto) {
    return this.authService.forgotPassword(forgotPasswordDto);
  }

  // ✅
  @Post('reset-password')
  @HttpCode(HttpStatus.OK)
  resetPassword(@Body() resetPasswordDto: ResetPasswordDto) {
    return this.authService.resetPassword(resetPasswordDto);
  }

  // ✅
  @Get('password-reset-token/:token')
  verifyPasswordResetToken(@Param('token') token: string) {
    return this.authService.verifyPasswordResetToken(token);
  }
}
