import {
  BadRequestException,
  ConflictException,
  HttpException,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { Prisma, UserInviteStatus } from '@repo/database';
import bcrypt from 'bcryptjs';
import { PrismaService } from 'src/infrastructure/prisma/prisma.service';
import { v4 as uuidv4 } from 'uuid';
import { AcceptInviteDto } from './dto/accept-invite.dto';
import { ForgotPasswordDto } from './dto/forgot-password.dto';
import { RegisterDto } from './dto/register.dto';
import { ResendVerificationTokenDto } from './dto/resend-verification-token.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { DEFAULT_CARRIER_PRICING } from '../constants/default-carrier-pricing';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(private readonly prisma: PrismaService) {}

  // ✅
  async register(registerDto: RegisterDto) {
    try {
      const {
        companyName,
        firstName,
        lastName,
        phone,
        shipmentVolume,
        email,
        password,
      } = registerDto;

      // Check if user already exists
      const existingUser = await this.prisma.client.user.findUnique({
        where: { email },
      });
      if (existingUser) throw new ConflictException('Email already in use');

      const hashedPassword = await bcrypt.hash(password, 10);

      const permissions = await this.prisma.client.permission.findMany();
      await this.prisma.client.user.create({
        data: {
          firstName,
          lastName,
          email,
          password: hashedPassword,
          organization: {
            create: {
              name: companyName,
              phone,
              shipmentVolume,
              OrganizationCarrierPricing: {
                createMany: {
                  data: DEFAULT_CARRIER_PRICING.map((pricing) => ({
                    carrierId: pricing.carrierId,
                    percentageMarkup: pricing.percentageMarkup,
                  })),
                },
              },
              wallet: {
                create: { balance: 0 },
              },
            },
          },
          permissions: {
            createMany: {
              data: permissions.map((permission) => ({
                permissionId: permission.id,
              })),
            },
          },
        },
      });

      const verificationToken = await this.createVerificationToken(email);
      const verificationLink = `${process.env.PUBLIC_URL}/auth/new-verification?token=${verificationToken.token}`;
      // TODO: Send actual email
      console.log({ verificationLink, email: verificationToken.email });

      return { success: 'Confirmation email sent!' };
    } catch (error) {
      this.logger.error(`Unexpected error: ${error.message}`);
      if (error instanceof HttpException) throw error;
      throw new InternalServerErrorException('Failed to register user');
    }
  }

  // ✅
  async resendVerificationToken(
    resendVerificationTokenDto: ResendVerificationTokenDto,
  ) {
    try {
      const { email } = resendVerificationTokenDto;
      const verificationToken =
        await this.prisma.client.verificationToken.findFirst({
          where: { email },
        });

      if (!verificationToken || verificationToken.expires < new Date()) {
        // Clean up old tokens
        await this.prisma.client.verificationToken.deleteMany({
          where: { email },
        });
        // Create new token (assuming user exists)
        const newToken = await this.createVerificationToken(email);
        console.log(
          `${process.env.PUBLIC_URL}/auth/new-verification?token=${newToken.token}`,
        );
      } else {
        console.log(
          `${process.env.PUBLIC_URL}/auth/new-verification?token=${verificationToken.token}`,
        );
      }
      return { success: 'true' };
    } catch (error) {
      this.logger.error(
        `Failed to send verification token email: ${error.message}`,
      );
      if (error instanceof HttpException) throw error;
      throw new InternalServerErrorException(
        'Failed to send verification token email',
      );
    }
  }

  // ✅
  async verifyVerificationToken(token: string) {
    try {
      const verificationToken =
        await this.prisma.client.verificationToken.findUnique({
          where: { token },
        });

      if (!verificationToken) {
        throw new NotFoundException('Verification token not found');
      }

      const hasExpired = new Date(verificationToken.expires) < new Date();
      if (hasExpired) {
        await this.prisma.client.verificationToken.delete({
          where: { id: verificationToken.id },
        });
        throw new BadRequestException('Verification token has expired');
      }

      const existingUser = await this.prisma.client.user.findUnique({
        where: { email: verificationToken.email },
      });

      if (!existingUser) {
        throw new NotFoundException(
          'No user found with this verification token',
        );
      }

      await this.prisma.client.$transaction([
        this.prisma.client.user.update({
          where: { id: existingUser.id },
          data: { emailVerified: new Date() },
        }),
        this.prisma.client.verificationToken.delete({
          where: { id: verificationToken.id },
        }),
      ]);

      this.logger.log(`Email verified for ${verificationToken.email}`);
      return { success: 'Email verified', email: verificationToken.email };
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }

      this.logger.error(`Unexpected error: ${error.message}`);
      throw new InternalServerErrorException('Failed to verify email');
    }
  }

  // ✅
  async acceptInvite(acceptInviteDto: AcceptInviteDto) {
    try {
      return await this.prisma.client.$transaction(async (tx) => {
        const userInvite = await tx.userInvite.findUnique({
          where: { token: acceptInviteDto.token },
          include: {
            permissions: true,
          },
        });

        // Checks if token is valid
        if (
          !userInvite ||
          userInvite.status !== UserInviteStatus.pending ||
          userInvite.expires < new Date()
        ) {
          throw new BadRequestException('Invalid or expired invitation');
        }

        // Checks if user already exists
        const existingUser = await tx.user.findUnique({
          where: { email: userInvite.email },
        });
        if (existingUser) {
          await tx.userInvite.delete({ where: { id: userInvite.id } });
          throw new ConflictException(
            'User account already exists. Please login instead',
          );
        }

        // Create new user
        const { firstName, lastName, password } = acceptInviteDto;
        const user = await tx.user.create({
          data: {
            firstName,
            lastName,
            email: userInvite.email,
            emailVerified: new Date(),
            password: await bcrypt.hash(password, 10),
            organizationId: userInvite.organizationId,
            permissions: {
              createMany: {
                data: userInvite.permissions.map((permission) => ({
                  permissionId: permission.permissionId,
                })),
              },
            },
          },
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
            organizationId: true,
          },
        });

        // Clean up
        await tx.userInvite.delete({ where: { id: userInvite.id } });
        return user;
      });
    } catch (error) {
      this.logger.error(`Unexpected error: ${error.message}`);

      if (error instanceof HttpException) {
        throw error;
      }

      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        this.logger.error(`Database error: ${error.message}`);
        switch (error.code) {
          case 'P2002': // Unique constraint violation
            throw new ConflictException('Email already in use');
          case 'P2025': // Record not found
            throw new NotFoundException('Invitation not found');
          default:
            throw new InternalServerErrorException('Database error occurred');
        }
      }

      throw new InternalServerErrorException('Failed to accept invite');
    }
  }

  // ✅
  async findOneUserInvite(token: string) {
    try {
      const userInvite = await this.prisma.client.userInvite.findUnique({
        where: { token },
      });
      if (!userInvite) throw new NotFoundException('User invite not found');
      return userInvite;
    } catch (error) {
      this.logger.error(`User invite with token ${token} not found`);
      if (error instanceof NotFoundException) throw error;
      throw new InternalServerErrorException('Failed to find user invite');
    }
  }

  // ✅
  async forgotPassword(forgotPasswordDto: ForgotPasswordDto) {
    const { email } = forgotPasswordDto;
    try {
      // Check if user exists
      const existingUser = await this.prisma.client.user.findUnique({
        where: { email },
      });
      // If user exists, create a password reset token and send email otherwise return message without doing anything
      if (existingUser) {
        const token = uuidv4();
        const expires = new Date(Date.now() + 60 * 60 * 1000);

        await this.prisma.client.$transaction([
          this.prisma.client.passwordResetToken.deleteMany({
            where: { email },
          }),
          this.prisma.client.passwordResetToken.create({
            data: { email, token, expires },
          }),
        ]);

        // TODO: Send actual email
        const resetPasswordUrl = `http://localhost:3000/auth/reset-password?token=${token}`;
        console.log({ resetPasswordUrl });
      }

      return { success: true };
    } catch (error) {
      this.logger.error(`Unexpected error: ${error.message}`);
      throw new InternalServerErrorException(
        'Failed to handle forgot password request',
      );
    }
  }

  // ✅
  async resetPassword(resetPasswordDto: ResetPasswordDto) {
    const { token, password } = resetPasswordDto;

    try {
      const passwordResetToken =
        await this.prisma.client.passwordResetToken.findUnique({
          where: { token },
        });

      // Checks if token is valid
      if (!passwordResetToken)
        throw new NotFoundException('Invalid or expired password reset token');

      // Checks if token has expired
      const hasExpired = new Date(passwordResetToken.expires) < new Date();
      if (hasExpired)
        throw new BadRequestException('Password reset token has expired');

      // Checks if user exists
      const user = await this.prisma.client.user.findUnique({
        where: { email: passwordResetToken.email },
      });
      if (!user) throw new NotFoundException('User not found');

      const hashedPassword = await bcrypt.hash(password, 10);

      await this.prisma.client.$transaction([
        this.prisma.client.user.update({
          where: { email: passwordResetToken.email },
          data: { password: hashedPassword },
        }),
        this.prisma.client.passwordResetToken.delete({
          where: { id: passwordResetToken.id },
        }),
      ]);

      return {
        success: true,
      };
    } catch (error) {
      if (error instanceof HttpException) throw error;

      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        this.logger.error(`Database error: ${error.message}`);
        switch (error.code) {
          case 'P2025': // Record not found
            throw new NotFoundException('Password reset token not found');
        }
      }

      this.logger.error(`Unexpected error: ${error.message}`);
      throw new InternalServerErrorException('Failed to reset password');
    }
  }

  // ✅
  async verifyPasswordResetToken(token: string) {
    try {
      const passwordResetToken =
        await this.prisma.client.passwordResetToken.findUnique({
          where: { token },
        });

      // Checks if token is valid
      if (!passwordResetToken)
        throw new NotFoundException('Invalid or expired password reset token');

      // Checks if token has expired
      const hasExpired = new Date(passwordResetToken.expires) < new Date();
      if (hasExpired)
        throw new BadRequestException('Password reset token has expired');

      return { success: true, message: 'Password reset token is valid' };
    } catch (error) {
      if (error instanceof HttpException) throw error;

      this.logger.error(`Unexpected error: ${error.message}`);
      throw new InternalServerErrorException(
        'Failed to verify password reset token',
      );
    }
  }

  // Internal methods
  private async createVerificationToken(email: string) {
    const token = uuidv4();
    const expires = new Date(Date.now() + 60 * 60 * 1000);

    return await this.prisma.client.$transaction(async (tx) => {
      await tx.verificationToken.deleteMany({ where: { email } });
      return await tx.verificationToken.create({
        data: { email, token, expires },
      });
    });
  }
}
