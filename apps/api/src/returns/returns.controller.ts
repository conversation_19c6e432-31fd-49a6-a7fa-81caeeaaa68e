import { Body, Controller, Get, Param, Post, Query } from '@nestjs/common';
import { CurrentUser } from 'src/common/decorators/user.decorator';
import { CreateReturnDto } from './dto/create-return.dto';
import { ReturnsService } from './returns.service';
import { ReturnQueryDto } from './dto/return-query.dto';

@Controller('returns')
export class ReturnsController {
  constructor(private readonly returnsService: ReturnsService) {}

  // ✅
  @Post()
  create(
    @CurrentUser('organizationId') organizationId: string,
    @Body() createReturnDto: CreateReturnDto,
  ) {
    return this.returnsService.create(organizationId, createReturnDto);
  }

  // ✅
  @Get()
  findAll(
    @CurrentUser('organizationId') organizationId: string,
    @Query() query: ReturnQueryDto,
  ) {
    return this.returnsService.findAll(organizationId, query);
  }

  // ✅
  @Get(':id')
  findOne(
    @CurrentUser('organizationId') organizationId: string,
    @Param('id') id: string,
  ) {
    return this.returnsService.findOne(organizationId, id);
  }
}
