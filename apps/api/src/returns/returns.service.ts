import {
  HttpException,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import {
  CounterType,
  Prisma,
  WalletTransactionCategory,
  WalletTransactionType,
} from '@repo/database';
import { format } from 'date-fns';
import { EasypostService } from 'src/infrastructure/easypost/easypost.service';
import { PrismaService } from 'src/infrastructure/prisma/prisma.service';
import { RatesService } from 'src/rates/rates.service';
import { ObjectTransformer } from 'src/utils/object-transformer.util';
import { filterBuilder } from 'src/utils/queryTools.util';
import { CreateReturnDto } from './dto/create-return.dto';
import { ReturnQueryDto } from './dto/return-query.dto';
import { WalletTransactionService } from 'src/finance/wallet/wallet-transaction.service';
import { Decimal } from 'node_modules/@repo/database/generated/prisma/runtime/library';

@Injectable()
export class ReturnsService {
  private readonly logger = new Logger(ReturnsService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly easypostService: EasypostService,
    private readonly ratesService: RatesService,
    private readonly walletTransactionService: WalletTransactionService,
  ) {}

  // ✅
  async create(organizationId: string, createReturnDto: CreateReturnDto) {
    const { epShipmentId, selectedRateId, insuranceAmount } = createReturnDto;

    try {
      // 1. Buy return label from EasyPost
      const epShipment = await this.easypostService.buyShipment(
        epShipmentId,
        selectedRateId,
        insuranceAmount,
      );

      const returnRecord = await this.prisma.client.$transaction(async (tx) => {
        // Generate RMA (if not provided)
        const rma =
          createReturnDto.rma || (await this.generateRma(tx, organizationId));

        // Transform rate markup
        const transformedRate =
          await this.ratesService.getPricingAndTransformRate(
            organizationId,
            epShipment.selected_rate,
          );

        // Create return
        const newReturn = await tx.return.create({
          data: {
            organizationId,
            rma,
            orderId: createReturnDto.orderId,
            epShipmentId: epShipment.id,
            status: createReturnDto.status,
            // Order Items
            fromAddress: ObjectTransformer.snakeToCamel(epShipment.to_address),
            toAddressId: createReturnDto.toAddressId,
            parcel: ObjectTransformer.snakeToCamel(epShipment.parcel),
            carrier: epShipment.selected_rate.carrier,
            carrierAccountId: epShipment.selected_rate.carrier_account_id,
            service: epShipment.selected_rate.service,
            rate: transformedRate.rate,
            trackingCode: epShipment.tracking_code,
            postageLabel: {
              labelUrl: epShipment.postage_label.label_url,
              labelPdfUrl: epShipment.postage_label.label_pdf_url,
              labelZplUrl: epShipment.postage_label.label_zpl_url,
            },
            insurance: epShipment.insurance as unknown as string, // inconsistent type
            orderItems: {
              createMany: {
                data: createReturnDto.orderItems.map((item) => ({
                  productId: item.product.id,
                  quantity: item.quantity,
                  price: item.price,
                  reason: item.reason,
                })),
              },
            },
          },
          include: {
            organization: { select: { walletId: true } },
            order: { select: { orderNo: true } },
          },
        });
        // Create wallet transaction
        await this.walletTransactionService.createTransaction({
          walletId: newReturn.organization.walletId,
          type: WalletTransactionType.label_purchase,
          category: WalletTransactionCategory.settled,
          amount: new Decimal(transformedRate.rate),
          referenceType: 'return_label',
          referenceId: newReturn.rma,
          description: `Purchased return label for order ${newReturn.order.orderNo}`,
        });

        return newReturn;
      });

      return returnRecord;
    } catch (error) {
      this.logger.error(
        `Failed to create return shipment: ${error.message || error}`,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        this.logger.error('Prisma error details:', error);
      }

      throw new InternalServerErrorException(
        'Failed to create return shipment',
        error.message,
      );
    }
  }

  // ✅
  async findAll(organizationId: string, query: ReturnQueryDto) {
    try {
      const { limit, offset, filter, search } = query;

      // Quick search filter
      const allowedQuickSearchFields = ['rma', 'trackingCode', 'order.orderNo'];
      const addressAllowedJsonFields = [
        'name',
        'company',
        'email',
        'street1',
        'city',
        'state',
        'country',
        'zip',
      ];
      // Advance filter
      const fuzzyFields = [
        'rma',
        'trackingCode',
        'order.orderNo',
        'order.storeId',
      ];
      const jsonFields = ['fromAddress.name'];

      const modifiedFilter = filterBuilder(filter ?? {}, {
        fuzzyFields,
        jsonFields,
      });

      const where: Prisma.ReturnWhereInput = {
        organizationId,
        ...modifiedFilter,
      };

      if (search) {
        const searchConditions: Prisma.ReturnWhereInput['OR'] = [];

        allowedQuickSearchFields.forEach((field) => {
          if (field === 'order.orderNo') {
            searchConditions.push({
              order: { orderNo: { contains: search, mode: 'insensitive' } },
            });
          } else {
            searchConditions.push({
              [field]: { contains: search, mode: 'insensitive' },
            });
          }
        });

        // Search in JSON fields - each field needs its own condition'
        // Search in fromAddress not toAddress, since it's a return
        addressAllowedJsonFields.forEach((jsonField) => {
          searchConditions.push({
            fromAddress: {
              path: [jsonField],
              string_contains: search,
              mode: 'insensitive',
            },
          });
        });
        where.OR = searchConditions;
      }

      const [data, count] = await Promise.all([
        this.prisma.client.return.findMany({
          where,
          orderBy: [{ createdAt: 'desc' }, { id: 'desc' }],
          take: limit,
          skip: offset ?? undefined,
          include: {
            order: { select: { orderNo: true } },
            toAddress: true,
            orderItems: { include: { product: true } },
          },
        }),
        this.prisma.client.return.count({ where }),
      ]);
      return {
        data,
        pagination: {
          total: count,
          limit,
          offset: offset ?? 0,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to find returns: ${error}`);
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        console.log(error);
      }
      throw new InternalServerErrorException(error);
    }
  }

  async findOne(organizationId: string, id: string) {
    try {
      const returnShipment = await this.prisma.client.return.findUnique({
        where: { organizationId, id },
        include: {
          order: { select: { orderNo: true } },
          toAddress: true,
          orderItems: { include: { product: true } },
          labelRefunds: true,
        },
      });

      if (!returnShipment) {
        throw new NotFoundException('Return shipment not found');
      }

      return returnShipment;
    } catch (error) {
      this.logger.error(
        `Failed to find return shipment with id: ${id}: ${error}`,
      );
      if (error instanceof HttpException) throw error;
      throw new InternalServerErrorException(error);
    }
  }

  // ============= INTERNAL METHODS =============
  private async generateRma(
    tx: Prisma.TransactionClient,
    organizationId: string,
  ) {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const dateStr = format(today, 'MMddyyyy');
    const prefix = `RMA-${dateStr}-`;

    const counterRecord = await tx.counter.upsert({
      where: {
        organizationId_type_date: {
          organizationId,
          type: CounterType.return,
          date: today,
        },
      },
      update: { count: { increment: 1 } },
      create: {
        organizationId,
        type: CounterType.return,
        date: today,
        count: 1,
      },
    });

    const sequenceStr = counterRecord.count.toString().padStart(5, '0');
    return prefix + sequenceStr;
  }
}
