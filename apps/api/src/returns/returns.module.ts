import { Module } from '@nestjs/common';
import { ReturnsService } from './returns.service';
import { ReturnsController } from './returns.controller';
import { EasypostModule } from 'src/infrastructure/easypost/easypost.module';
import { RatesModule } from 'src/rates/rates.module';
import { WalletModule } from 'src/finance/wallet/wallet.module';

@Module({
  imports: [EasypostModule, RatesModule, WalletModule],
  controllers: [ReturnsController],
  providers: [ReturnsService],
})
export class ReturnsModule {}
