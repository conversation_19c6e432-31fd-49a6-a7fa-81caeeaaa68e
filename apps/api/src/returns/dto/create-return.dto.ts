import { ReturnReason, ShipmentStatus } from '@repo/database';
import { Type } from 'class-transformer';
import {
  ArrayMinSize,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Min,
  ValidateNested,
} from 'class-validator';

class ProductDto {
  @IsString()
  @IsNotEmpty()
  id: string;

  @IsString()
  @IsNotEmpty()
  sku: string;

  @IsString()
  @IsNotEmpty()
  name: string;
}

export class OrderItemDto {
  @ValidateNested()
  @Type(() => ProductDto)
  product: ProductDto;

  @IsNumber()
  @Min(1)
  quantity: number;

  @IsNumber()
  @Min(0)
  price: number;

  @IsEnum(ReturnReason)
  @IsNotEmpty()
  reason: ReturnReason;
}

export class CreateReturnDto {
  @IsString()
  @IsOptional()
  rma?: string;

  @IsString()
  @IsNotEmpty()
  orderId: string;

  @IsString()
  @IsNotEmpty()
  toAddressId: string;

  @IsString()
  @IsNotEmpty()
  epShipmentId: string;

  @IsString()
  @IsNotEmpty()
  selectedRateId: string;

  @IsNumber()
  @IsOptional()
  insuranceAmount?: number;

  @IsEnum(ShipmentStatus)
  @IsOptional()
  status?: ShipmentStatus;

  @ValidateNested()
  @Type(() => OrderItemDto)
  @ArrayMinSize(1, { message: 'At least one item is required' })
  orderItems: OrderItemDto[];
}
