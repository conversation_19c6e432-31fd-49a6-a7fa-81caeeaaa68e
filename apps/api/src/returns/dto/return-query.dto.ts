import { ShipmentStatus } from '@repo/database';
import { Type } from 'class-transformer';
import {
  IsEnum,
  IsObject,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { PaginationQueryDto } from 'src/common/dto/pagination-query.dto';

class ReturnFilterDto {
  @IsEnum(ShipmentStatus)
  @IsOptional()
  status?: ShipmentStatus;

  @IsString()
  @IsOptional()
  rma?: string;

  @IsObject()
  @IsOptional()
  order?: {
    orderNo?: string;
    storeId?: string;
  };

  @IsString()
  @IsOptional()
  trackingCode?: string;

  @IsObject()
  @IsOptional()
  fromAddress?: {
    name?: string;
  };
}

export class ReturnQueryDto extends PaginationQueryDto {
  @ValidateNested()
  @Type(() => ReturnFilterDto)
  @IsOptional()
  filter?: ReturnFilterDto;

  @IsString()
  @IsOptional()
  search?: string;
}
