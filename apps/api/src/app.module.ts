import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { APP_GUARD } from '@nestjs/core';
import { AccountModule } from './account/account.module';
import { AddressesModule } from './addresses/addresses.module';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthModule } from './auth/auth.module';
import { BatchesModule } from './batches/batches.module';
import { ClaimsModule } from './claims/claims.module';
import { AuthGuard } from './common/guards/auth/auth.guard';
import { OrdersModule } from './orders/orders.module';
import { ParcelsModule } from './parcels/parcels.module';
import { RatesModule } from './rates/rates.module';
import { RefundsModule } from './refunds/refunds.module';
import { ReturnsModule } from './returns/returns.module';
import { ShipmentsModule } from './shipments/shipments.module';
import { StoresModule } from './stores/stores.module';
import { UsersModule } from './users/users.module';
import { FinanceModule } from './finance/finance.module';
import { WebhookModule } from './webhook/webhook.module';
import { PrismaModule } from './infrastructure/prisma/prisma.module';

@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true, envFilePath: ['.env'] }),
    PrismaModule,
    UsersModule,
    AddressesModule,
    ParcelsModule,
    BatchesModule,
    OrdersModule,
    AuthModule,
    RatesModule,
    ShipmentsModule,
    ReturnsModule,
    StoresModule,
    ClaimsModule,
    RefundsModule,
    AccountModule,
    FinanceModule,
    WebhookModule,
  ],
  controllers: [AppController],
  providers: [AppService, { provide: APP_GUARD, useClass: AuthGuard }],
})
export class AppModule {}
