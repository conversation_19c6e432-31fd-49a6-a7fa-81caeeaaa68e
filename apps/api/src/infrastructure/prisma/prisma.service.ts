import {
  Injectable,
  Lo<PERSON>,
  OnM<PERSON>uleD<PERSON>roy,
  OnModuleInit,
} from '@nestjs/common';
import { prisma } from '@repo/database';

@Injectable()
export class PrismaService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(PrismaService.name);

  client = prisma;

  async onModuleInit() {
    this.logger.log('Connecting to database...');
    // Connect to the database when the module initializes
    // await prisma.$connect();
  }

  async onModuleDestroy() {
    this.logger.log('Disconnecting from database...');
    // Disconnect from the database when the application shuts down
    // await prisma.$disconnect();
  }
}
