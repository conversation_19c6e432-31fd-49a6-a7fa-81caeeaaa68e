import { Injectable, Logger } from '@nestjs/common';
import { FilesToUpload } from 'src/common/types/upload-files';
import sharp from 'sharp';
import { ImageProcessingOptions } from 'src/common/types/image-processing-options.interface';

@Injectable()
export class ImageProcessingService {
  private readonly logger = new Logger(ImageProcessingService.name);

  async compressImages(
    files: FilesToUpload[],
    options: ImageProcessingOptions = {},
  ): Promise<FilesToUpload[]> {
    if (!files || files.length === 0) {
      return [];
    }

    const {
      maxWidth = 1200,
      maxHeight = 1200,
      quality = 80,
      format = 'original',
      fit = 'inside',
    } = options;

    // Validate quality range
    const validQuality = Math.max(1, Math.min(100, quality));

    const processedFiles = await Promise.all(
      files.map(async (file) => {
        try {
          // Only process image files
          if (!file.mimetype.startsWith('image/')) {
            return file; // Return unchanged for non-images
          }

          let sharpInstance = sharp(file.buffer).resize(maxWidth, maxHeight, {
            fit,
            withoutEnlargement: true,
          });

          // Determine the target format
          let targetFormat = format;
          if (format === 'original') {
            // More precise format detection
            const mimeTypeToFormat = {
              'image/jpeg': 'jpeg',
              'image/jpg': 'jpeg',
              'image/png': 'png',
              'image/webp': 'webp',
            };

            targetFormat = mimeTypeToFormat[file.mimetype] || 'jpeg';
          }

          // Apply format-specific settings
          switch (targetFormat) {
            case 'jpeg':
              sharpInstance = sharpInstance.jpeg({
                quality: validQuality,
                progressive: true,
              });
              break;
            case 'png':
              sharpInstance = sharpInstance.png({
                compressionLevel: Math.min(9, Math.floor(validQuality / 10)),
                progressive: true,
              });
              break;
            case 'webp':
              sharpInstance = sharpInstance.webp({ quality: validQuality });
              break;
          }

          const compressedBuffer = await sharpInstance.toBuffer();

          // Determine mimetype based on target format
          const mimetypeMap = {
            jpeg: 'image/jpeg',
            png: 'image/png',
            webp: 'image/webp',
          };

          return {
            ...file,
            buffer: compressedBuffer,
            size: compressedBuffer.length, // Update size
            mimetype: mimetypeMap[targetFormat], // Set mimetype based on target format
            originalname:
              format === 'original'
                ? file.originalname
                : file.originalname.replace(
                    /\.[^/.]+$/,
                    `.${this.getFileExtension(targetFormat)}`,
                  ),
          };
        } catch (error) {
          this.logger.error(
            `Error processing image ${file.originalname}: ${error.message}`,
          );
          return file;
        }
      }),
    );
    return processedFiles;
  }

  private getFileExtension(format: string): string {
    const extensions = { jpeg: 'jpg', png: 'png', webp: 'webp' };
    return extensions[format] || 'jpg';
  }
}
