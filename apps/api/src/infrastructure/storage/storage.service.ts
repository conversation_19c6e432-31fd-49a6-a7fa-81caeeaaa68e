import {
  DeleteObjectCommand,
  ListObjectsCommand,
  PutObjectCommand,
  S3Client,
} from '@aws-sdk/client-s3';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { FilesToUpload, UploadedFile } from 'src/common/types/upload-files';

@Injectable()
export class StorageService {
  private readonly logger = new Logger(StorageService.name);

  private readonly s3Client: S3Client;
  private readonly r2Bucket: string;
  private readonly r2PublicUrl: string;

  constructor(private configService: ConfigService) {
    const r2Endpoint = this.configService.get('R2_ENDPOINT');
    const r2AccessKey = this.configService.get('R2_ACCESS_KEY');
    const r2SecretKey = this.configService.get('R2_SECRET_KEY');
    const r2Bucket = this.configService.get('R2_BUCKET_NAME');
    const r2PublicUrl = this.configService.get('R2_PUBLIC_URL');

    if (
      !r2Endpoint ||
      !r2AccessKey ||
      !r2SecretKey ||
      !r2Bucket ||
      !r2PublicUrl
    ) {
      throw new Error(
        'R2_ENDPOINT, R2_ACCESS_KEY, R2_SECRET_KEY, and R2_BUCKET_NAME must be set',
      );
    }

    this.r2Bucket = r2Bucket;
    this.r2PublicUrl = r2PublicUrl;

    this.s3Client = new S3Client({
      region: 'auto',
      endpoint: r2Endpoint,
      credentials: {
        accessKeyId: r2AccessKey,
        secretAccessKey: r2SecretKey,
      },
      forcePathStyle: true,
    });
  }

  async listFiles() {
    const command = new ListObjectsCommand({
      Bucket: this.r2Bucket,
    });

    try {
      const data = await this.s3Client.send(command);
      if (data.Contents && data.Contents.length <= 0)
        throw new Error('No files found');

      const mappedData = data?.Contents?.map((item) => ({
        url: `${this.r2PublicUrl}/${item.Key}`,
        fileName: item.Key,
        fileSize: item.Size,
      }));

      if (!mappedData || mappedData.length <= 0) {
        return [];
      }

      return mappedData;
    } catch (error) {
      console.error('Error uploading file to R2:', error);
      throw error;
    }
  }

  async uploadFiles(
    files: FilesToUpload[],
    keyPrefix: string,
  ): Promise<UploadedFile[]> {
    try {
      const uploadPromises = files.map(async (file) => {
        // Generate unique filename
        const timestamp = Date.now().toString().slice(-6);
        const random = Math.random().toString(36).substring(2, 6);
        const fileExtension = file.originalname.split('.').pop() || 'jpg';
        const fileName = `${timestamp}${random}.${fileExtension}`;
        const key = `${keyPrefix}/${fileName}`;

        const command = new PutObjectCommand({
          Bucket: this.r2Bucket,
          Key: key,
          Body: file.buffer,
          ContentType: file.mimetype,
          Metadata: {
            fieldname: file.fieldname,
          },
        });

        await this.s3Client.send(command);

        return {
          url: `${this.r2PublicUrl}/${key}`,
          fileName: file.originalname,
          fileSize: file.size,
          fieldname: file.fieldname,
          mimetype: file.mimetype,
          key,
        };
      });

      const results = await Promise.all(uploadPromises);

      this.logger.log(`Uploaded ${files.length} files`);
      return results;
    } catch (error) {
      this.logger.error(`Failed to upload files: ${error.message}`);
      throw error;
    }
  }

  async deleteFile(key: string) {
    try {
      const command = new DeleteObjectCommand({
        Bucket: this.r2Bucket,
        Key: key,
      });

      await this.s3Client.send(command);
      this.logger.log(`Deleted file: ${key}`);
    } catch (error) {
      this.logger.error(`Failed to delete file ${key}: ${error.message}`);
      // Don't throw - old file might already be deleted
    }
  }

  extractKeyFromUrl(url: string): string | null {
    try {
      const urlObj = new URL(url);
      return urlObj.pathname.substring(1);
    } catch (error) {
      return null;
    }
  }
}
