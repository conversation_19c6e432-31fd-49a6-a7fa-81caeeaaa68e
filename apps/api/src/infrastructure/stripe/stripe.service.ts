import {
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import Stripe from 'stripe';
import CreatePaymentIntentProps from './types/create-payment-intent.interface';

@Injectable()
export class StripeService {
  private readonly client: Stripe;
  private readonly logger = new Logger(StripeService.name);

  constructor() {
    const privateKey = process.env.STRIPE_SECRET_KEY;
    if (!privateKey) {
      throw new Error('STRIPE_SECRET_KEY environment variable is required');
    }
    this.client = new Stripe(privateKey);
  }

  async createCustomer(
    organizationName: string,
    email: string,
  ): Promise<Stripe.Customer> {
    try {
      return await this.client.customers.create({
        name: organizationName,
        email,
      });
    } catch (error) {
      this.logger.error(`Failed to create stripe customer: ${error}`);
      throw new InternalServerErrorException(
        `Error while creating stripe customer: ${error}`,
      );
    }
  }

  async createSetupIntent(customerId: string): Promise<Stripe.SetupIntent> {
    try {
      return await this.client.setupIntents.create({
        customer: customerId,
        automatic_payment_methods: {
          enabled: true,
        },
      });
    } catch (error) {
      this.logger.error(`Failed to create stripe setup intent: ${error}`);
      throw new InternalServerErrorException(
        `Error while creating stripe setup intent: ${error}`,
      );
    }
  }

  verifyWebhookSignature(
    payload: Buffer<ArrayBufferLike>,
    signature: string,
  ): Stripe.Event {
    const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;
    if (!webhookSecret) {
      throw new Error('STRIPE_WEBHOOK_SECRET environment variable is required');
    }

    try {
      return this.client.webhooks.constructEvent(
        payload,
        signature,
        webhookSecret,
      );
    } catch (error) {
      this.logger.error('Webhook signature verification failed', error);
      throw new Error(
        `Webhook signature verification failed: ${error.message}`,
      );
    }
  }

  async getPaymentMethod(
    paymentMethodId: string,
  ): Promise<Stripe.PaymentMethod> {
    try {
      return await this.client.paymentMethods.retrieve(paymentMethodId);
    } catch (error) {
      this.logger.error(
        `Failed to retrieve payment method: ${paymentMethodId}`,
        error,
      );
      throw error;
    }
  }

  async createPaymentIntent(props: CreatePaymentIntentProps) {
    try {
      const paymentIntent = await this.client.paymentIntents.create({
        ...props,
        off_session: true,
        confirm: true,
      });
      return paymentIntent;
    } catch (error) {
      this.logger.error(`Failed to create payment intent`, error.code);

      const paymentIntentRetrieved = await this.client.paymentIntents.retrieve(
        error.raw.payment_intent.id,
      );
      console.log('PI retrieved: ', paymentIntentRetrieved.id);

      throw new Error('Failed to create payment intent');
    }
  }

  // Rollbacks
  async detachPaymentMethod(paymentMethodId: string) {
    await this.client.paymentMethods.detach(paymentMethodId);
  }
}
