import EasyPost, { IRefund } from '@easypost/api';

import {
  HttpException,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { EpClaim } from '@repo/easypost-types';
import { CreateAddressDto } from './dto/create-address.dto';
import { CreateClaimDto } from './dto/create-claim.dto';
import { CreateShipmentDto } from './dto/create-shipment.dto';
import { CreateRefundDto } from './dto/create-refund.dto';
// import { CreateReturnDto } from './dto/create-return.dto';

@Injectable()
export class EasypostService {
  private client: InstanceType<typeof EasyPost>;
  private readonly logger = new Logger(EasypostService.name);

  constructor() {
    const apiKey = process.env.EASY_POST_API_KEY;
    if (!apiKey) {
      throw new Error('EASY_POST_API_KEY environment variable is required');
    }
    this.client = new EasyPost(apiKey);
  }

  // SHIPMENTS
  async createShipment(createShipmentDto: CreateShipmentDto) {
    try {
      return await this.client.Shipment.create(createShipmentDto);
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async getShipment(epShipmentId: string) {
    try {
      return await this.client.Shipment.retrieve(epShipmentId);
    } catch (error) {
      this.logger.error(`Failed to get shipment with Easypost: ${error}`);
      throw error;
    }
  }

  async buyShipment(id: string, rateId: string, insuranceAmount?: number) {
    try {
      const retrievedShipment = await this.client.Shipment.retrieve(id);
      const selectedRate = retrievedShipment.rates.find(
        (rate) => rate.id === rateId,
      );
      if (!selectedRate) {
        throw new NotFoundException(`Selected rate not found`);
      }
      const shipment = await this.client.Shipment.buy(
        retrievedShipment.id,
        selectedRate.id,
        insuranceAmount,
      );
      return shipment;
    } catch (error) {
      this.logger.error(`Failed to buy shipment with Easypost: ${error}`);
      if (error instanceof HttpException) throw error;
      throw new InternalServerErrorException(error);
    }
  }

  async convertShipmentLabel(id: string, format: 'PDF' | 'ZPL') {
    try {
      return await this.client.Shipment.convertLabelFormat(id, format);
    } catch (error) {
      this.logger.error(
        `Failed to convert shipment label with Easypost: ${error}`,
      );
      throw new InternalServerErrorException(error);
    }
  }

  // RETURNS
  // async createReturn(createReturnDto: CreateReturnDto) {
  //   const { parcelId, toAddressId, fromAddressId } = createReturnDto;

  //   try {
  //     return await this.client.Shipment.create({
  //       parcel: { id: parcelId },
  //       to_address: { id: toAddressId },
  //       from_address: { id: fromAddressId },
  //       is_return: true,
  //     });
  //   } catch (error) {
  //     this.logger.error(
  //       `Failed to create return shipment with Easypost: ${error}`,
  //     );
  //     throw new InternalServerErrorException(error);
  //   }
  // }

  // CLAIMS
  async createClaim(createClaimDto: CreateClaimDto) {
    /*  
      Type assertion to access the Claim property.
      Easypost didn't include the Claim property in the EasyPost type definition.
    */

    const claimService = (this.client as any).Claim;

    const {
      trackingCode,
      type,
      amount,
      emailEvidenceAttachments,
      invoiceAttachments,
      supportingDocumentsAttachments,
      description,
      recipientName,
      contactEmail,
    } = createClaimDto;

    try {
      const epClaim = (await claimService.create({
        tracking_code: trackingCode,
        type: type,
        amount: amount,
        email_evidence_attachments: emailEvidenceAttachments,
        invoice_attachments: invoiceAttachments,
        supporting_documents_attachments: supportingDocumentsAttachments,
        description: description,
        recipient_name: recipientName,
        contact_email: contactEmail,
      })) as EpClaim;

      return epClaim; // Easypost doesn't include the Claim property in the EasyPost type definition
    } catch (error) {
      console.log(error);
      this.logger.error(`Failed to create claim with Easypost: ${error.code}`);
      throw error;
    }
  }

  async cancelClaim(epClaimId: string) {
    const claimService = (this.client as any).Claim;

    try {
      return await claimService.cancel(epClaimId);
    } catch (error) {
      this.logger.error(`Failed to cancel claim with Easypost: ${error.code}`);
      throw error;
    }
  }

  async getClaim(epClaimId: string) {
    const claimService = (this.client as any).Claim;

    try {
      return await claimService.retrieve(epClaimId);
    } catch (error) {
      this.logger.error(`Failed to get claim with Easypost: ${error.code}`);
      throw error;
    }
  }

  // REFUNDS
  async createRefund(createRefundDto: CreateRefundDto) {
    const { carrier, trackingCodes } = createRefundDto;
    try {
      const epRefunds = await this.client.Refund.create({
        carrier,
        tracking_codes: trackingCodes,
      });
      return epRefunds as unknown as IRefund[];
    } catch (error) {
      this.logger.error(`Failed to create refund with Easypost: ${error}`);
      throw error;
    }
  }

  // ADDRESSES
  async getAddresses() {
    try {
      const response = await this.client.Address.all();
      const refinedAddresses = response.addresses.map((a) => ({
        id: a.id,
        name: a.name,
        company: a.company,
        street1: a.street1,
        street2: a.street2,
        city: a.city,
        state: a.state,
        zip: a.zip,
        country: a.country,
        phone: a.phone,
        email: a.email,
        residential: a.residential,

        created_at: (a as any).created_at, // Type assertion
        updated_at: (a as any).updated_at, // Type assertion
      }));
      return refinedAddresses;
    } catch (error) {
      this.logger.error(`Failed to get addresses with Easypost: ${error}`);
      throw error;
    }
  }

  async createAndVerifyAddress(createAddressDto: CreateAddressDto) {
    try {
      return await this.client.Address.createAndVerify(createAddressDto);
    } catch (error) {
      this.logger.error(`Failed to create address with Easypost: ${error}`);
      throw error;
    }
  }
}
