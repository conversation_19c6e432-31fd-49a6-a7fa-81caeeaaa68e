import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateIf,
  ValidateNested,
} from 'class-validator';
import { OptionsDto } from 'src/common/dto/optionts.dto';
import { ParcelDto } from 'src/common/dto/parcel.dto';

export class AddressDtoWithId {
  @IsString()
  @IsOptional()
  id?: string;

  @ValidateIf((obj) => !obj.id)
  @IsString()
  @IsNotEmpty()
  name?: string;

  @IsString()
  @IsOptional()
  company?: string;

  @ValidateIf((obj) => !obj.id)
  @IsString()
  @IsNotEmpty()
  street1?: string;

  @IsString()
  @IsOptional()
  street2?: string;

  @ValidateIf((obj) => !obj.id)
  @IsString()
  @IsNotEmpty()
  city?: string;

  @ValidateIf((obj) => !obj.id)
  @IsString()
  @IsNotEmpty()
  state?: string;

  @ValidateIf((obj) => !obj.id)
  @IsString()
  @IsNotEmpty()
  country?: string;

  @ValidateIf((obj) => !obj.id)
  @IsString()
  @IsNotEmpty()
  zip?: string;

  @ValidateIf((obj) => !obj.id)
  @IsString()
  @IsNotEmpty()
  phone?: string;

  @ValidateIf((obj) => !obj.id)
  @IsEmail()
  @IsNotEmpty()
  email?: string;

  @IsBoolean()
  @IsOptional()
  residential?: boolean = false;

  @IsBoolean()
  @IsOptional()
  verified?: boolean = false;
}

export class CreateShipmentDto {
  @IsString()
  @IsOptional()
  reference?: string;

  @ValidateNested()
  @Type(() => AddressDtoWithId)
  @IsNotEmpty()
  to_address: AddressDtoWithId;

  @ValidateNested()
  @Type(() => AddressDtoWithId)
  @IsNotEmpty()
  from_address: AddressDtoWithId; // From address is already created

  @ValidateNested()
  @Type(() => ParcelDto)
  parcel: ParcelDto;

  @ValidateNested()
  @Type(() => OptionsDto)
  @IsOptional()
  options?: OptionsDto;

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  carrier_accounts?: string[];

  @IsString()
  @IsOptional()
  service?: string;

  @IsString()
  @IsOptional()
  insurance?: string;

  //   customs_info?: any;
  //   carrier_accounts?: string[];
}
