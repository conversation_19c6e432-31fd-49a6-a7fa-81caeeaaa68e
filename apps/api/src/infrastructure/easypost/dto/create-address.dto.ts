import { IsString, IsOptional, IsEmail, IsBoolean } from 'class-validator';

export class CreateAddressDto {
  @IsString()
  street1: string;

  @IsString()
  @IsOptional()
  street2?: string;

  @IsString()
  city: string;

  @IsString()
  @IsOptional()
  state?: string;

  @IsString()
  zip: string;

  @IsString()
  country: string;

  @IsString()
  @IsOptional()
  name?: string;

  @IsString()
  @IsOptional()
  company?: string;

  @IsString()
  @IsOptional()
  phone?: string;

  @IsEmail()
  @IsOptional()
  email?: string;

  @IsBoolean()
  @IsOptional()
  residential?: boolean;

  @IsString()
  @IsOptional()
  carrier_facility?: string;

  @IsString()
  @IsOptional()
  federal_tax_id?: string;

  @IsString()
  @IsOptional()
  state_tax_id?: string;

  @IsBoolean()
  @IsOptional()
  verify?: boolean;

  @IsBoolean()
  @IsOptional()
  verify_strict?: boolean;
}
