import { ClaimType } from '@repo/database';
import {
  ArrayMinSize,
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateIf,
} from 'class-validator';

export class CreateClaimDto {
  @IsString()
  @IsNotEmpty()
  trackingCode: string;

  @IsEnum(ClaimType)
  @IsNotEmpty()
  type: ClaimType;

  @IsString()
  @IsNotEmpty()
  amount: string;

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  emailEvidenceAttachments: string[];

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  invoiceAttachments: string[];

  @IsArray()
  @IsString({ each: true })
  @ValidateIf((o) => o.type === ClaimType.theft || o.type === ClaimType.damage)
  @ArrayMinSize(1, {
    message: 'Supporting documents are required for theft or damage claims',
  })
  supportingDocumentsAttachments: string[];

  @IsString()
  @IsOptional()
  description?: string;

  @IsString()
  @IsNotEmpty()
  recipientName: string;

  @IsString()
  @IsNotEmpty()
  contactEmail: string;
}
