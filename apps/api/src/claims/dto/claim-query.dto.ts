import { ClaimStatus, ClaimType } from '@repo/database';
import { Type } from 'class-transformer';
import { IsEnum, IsOptional, IsString, ValidateNested } from 'class-validator';
import { PaginationQueryDto } from 'src/common/dto/pagination-query.dto';

class ClaimFilterDto {
  @IsEnum(ClaimStatus)
  @IsOptional()
  status?: ClaimStatus;

  @IsString()
  @IsOptional()
  trackingCode?: string;

  @IsString()
  @IsOptional()
  type?: ClaimType;

  @IsString()
  @IsOptional()
  reference?: string;
}

export class ClaimQueryDto extends PaginationQueryDto {
  @ValidateNested()
  @Type(() => ClaimFilterDto)
  @IsOptional()
  filter?: ClaimFilterDto;
}
