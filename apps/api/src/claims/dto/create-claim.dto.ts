import { ClaimType } from '@repo/database';
import { Type } from 'class-transformer';
import {
  ArrayMinSize,
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateIf,
  ValidateNested,
} from 'class-validator';

export class ClaimAttachmentDto {
  @IsString()
  @IsNotEmpty()
  data: string;

  @IsString()
  @IsNotEmpty()
  mimeType: string;

  @IsString()
  @IsNotEmpty()
  fileName: string;

  @IsNumber()
  @IsNotEmpty()
  fileSize: number;
}

export class CreateClaimDto {
  // For creating database claim
  @IsString()
  @ValidateIf((o) => !o.returnId || o.returnId.trim() === '')
  @IsNotEmpty({
    message: 'shipmentId is required when returnId is not provided',
  })
  shipmentId?: string;

  @IsString()
  @ValidateIf((o) => !o.shipmentId || o.shipmentId.trim() === '')
  @IsNotEmpty({
    message: 'returnId is required when shipmentId is not provided',
  })
  returnId?: string;

  @IsString()
  @IsOptional()
  reference?: string;

  // For creating Easypost claim
  @IsString()
  @IsNotEmpty()
  trackingCode: string;

  @IsEnum(ClaimType)
  @IsNotEmpty()
  type: ClaimType;

  @IsString()
  @IsNotEmpty()
  amount: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ClaimAttachmentDto)
  @IsOptional()
  emailEvidenceAttachments: ClaimAttachmentDto[];

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ClaimAttachmentDto)
  @IsOptional()
  invoiceAttachments: ClaimAttachmentDto[];

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ClaimAttachmentDto)
  @ValidateIf((o) => o.type === ClaimType.theft || o.type === ClaimType.damage)
  @ArrayMinSize(1, {
    message: 'Supporting documents are required for theft or damage claims',
  })
  supportingDocumentsAttachments: ClaimAttachmentDto[];

  @IsString()
  @IsNotEmpty()
  description: string;
}
