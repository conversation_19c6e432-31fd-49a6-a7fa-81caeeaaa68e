import {
  ConflictException,
  HttpException,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  UnprocessableEntityException,
} from '@nestjs/common';
import {
  ClaimAttachmentType,
  ClaimStatus,
  CounterType,
  Prisma,
  User,
} from '@repo/database';
import { format } from 'date-fns';
import { PrismaService } from 'src/infrastructure/prisma/prisma.service';
import { FilesToUpload, UploadedFile } from 'src/common/types/upload-files';
import { EasypostService } from 'src/infrastructure/easypost/easypost.service';
import { StorageService } from 'src/infrastructure/storage/storage.service';
import { filterBuilder } from 'src/utils/queryTools.util';
import { ClaimQueryDto } from './dto/claim-query.dto';
import { CreateClaimDto } from './dto/create-claim.dto';

const USER_SELECT_FIELDS = {
  id: true,
  firstName: true,
  lastName: true,
  email: true,
  status: true,
  image: true,
  createdAt: true,
};

@Injectable()
export class ClaimsService {
  private readonly logger = new Logger(ClaimsService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly easypostService: EasypostService,
    private readonly storageService: StorageService,
  ) {}

  async create(
    user: User,
    createClaimDto: CreateClaimDto,
    files: FilesToUpload[],
  ) {
    const steps: { type: string; data?: any }[] = [];

    try {
      const easypostAttachments = this.filesToEasypostAttachments(files);

      // Create claim in Easypost
      const epClaim = await this.easypostService.createClaim({
        trackingCode: createClaimDto.trackingCode,
        type: createClaimDto.type,
        amount: createClaimDto.amount,
        description: createClaimDto.description,
        recipientName: 'Dropright., Crop',
        contactEmail: '<EMAIL>',
        ...easypostAttachments,
      });
      this.logger.log(`Claim created in Easypost: ${epClaim.id}`);
      steps.push({ type: 'EASYPOST.CLAIM_CREATED', data: epClaim });

      // Create claim in database without transactions
      const newClaim = await this.prisma.client.$transaction(
        async (tx: Prisma.TransactionClient) => {
          const claimNo = await this.generateClaimNo(tx, user.organizationId);

          const response = await this.prisma.client.claim.create({
            data: {
              organizationId: user.organizationId,
              userId: user.id,
              claimNo,
              ...(createClaimDto.shipmentId && {
                shipmentId: createClaimDto.shipmentId,
              }),
              ...(createClaimDto.returnId && {
                returnId: createClaimDto.returnId,
              }),

              reference: createClaimDto.reference,

              epClaimId: epClaim.id,
              epInsuranceId: epClaim.insurance_id,

              trackingCode: epClaim.tracking_code,
              status: epClaim.status as ClaimStatus, // use the same status as easypost
              type: createClaimDto.type,
              description: createClaimDto.description,

              requestAmount: epClaim.requested_amount,
              insuranceAmount: epClaim.insurance_amount,
              approvedAmount: epClaim.approved_amount,
            },
          });

          steps.push({ type: 'DATABASE.CLAIM_CREATED', data: response });

          return response;
        },
      );

      // Upload attachments
      const keyPrefix = `${user.organizationId}/claims`;
      const uploadedFiles = await this.storageService.uploadFiles(
        files.map((file) => ({
          ...file,
          originalname: `${newClaim.claimNo}-${file.originalname}`,
        })),
        keyPrefix,
      );
      steps.push({
        type: 'CLOUDFLARE.ATTACHMENTS_UPLOADED',
        data: uploadedFiles,
      });

      // Update claim with attachments
      const claimAttachments = this.filesToDatabaseAttachments(uploadedFiles);

      const updatedClaim = await this.prisma.client.claim.update({
        where: { id: newClaim.id },
        data: { attachments: { createMany: { data: claimAttachments } } },
        include: {
          attachments: true,
          user: {
            select: USER_SELECT_FIELDS,
          },
        },
      });

      return updatedClaim;
    } catch (error) {
      this.logger.error(`Failed to create claim: ${error}`);

      await this.compensate(steps.reverse());
      if (error instanceof HttpException) throw error;

      // Easypost error
      if (error.code === 'UNPROCESSABLE_ENTITY') {
        throw new UnprocessableEntityException(error.errors[0]);
      }
      if (error.code === 'CONFLICT') {
        throw new ConflictException(error.errors[0]);
      }

      throw new InternalServerErrorException(
        `Failed to create claim ${error.message}`,
      );
    }
  }

  async findAll(organizationId: string, query: ClaimQueryDto) {
    try {
      const { limit, offset, filter } = query;

      // Advance filter
      const fuzzyFields = ['trackingCode', 'reference'];

      const modifiedFilter = filterBuilder(filter ?? {}, {
        fuzzyFields,
        jsonFields: [],
      });

      const where: Prisma.ClaimWhereInput = {
        organizationId,
        ...modifiedFilter,
      };

      const [data, count] = await Promise.all([
        this.prisma.client.claim.findMany({
          where,
          orderBy: [{ createdAt: 'desc' }],
          take: limit,
          skip: offset ?? undefined,
          include: {
            attachments: true,
            user: {
              select: USER_SELECT_FIELDS,
            },
          },
        }),
        this.prisma.client.claim.count({ where }),
      ]);

      return {
        data,
        pagination: {
          total: count,
          limit,
          offset: offset ?? 0,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to retrieve claims: ${error.message}`);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to retrieve claims');
    }
  }

  async findOne(organizationId: string, id: string) {
    try {
      const claim = await this.prisma.client.claim.findUnique({
        where: { id, organizationId },
        include: {
          attachments: true,
          user: {
            select: USER_SELECT_FIELDS,
          },
          shipment: { select: { shipmentNo: true } },
          return: { select: { rma: true } },
        },
      });

      if (!claim) {
        throw new NotFoundException(`Claim with ID ${id} not found`);
      }

      return claim;
    } catch (error) {
      this.logger.error(`Failed to retrieve claim: ${error.message}`);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to retrieve claim');
    }
  }

  async cancel(organizationId: string, id: string) {
    try {
      const claim = await this.prisma.client.claim.findUnique({
        where: { id, organizationId },
      });

      if (!claim) {
        throw new NotFoundException(`Claim with ID ${id} not found`);
      }
      if (claim.status === ClaimStatus.cancelled) {
        throw new ConflictException('Claim is already cancelled');
      }

      const epResponse = await this.easypostService.cancelClaim(
        claim.epClaimId,
      );
      this.logger.log(`Claim cancelled in Easypost: ${epResponse.id}`);

      const updatedClaim = await this.prisma.client.claim.update({
        where: { id },
        data: { status: ClaimStatus.cancelled },
      });

      return updatedClaim;
    } catch (error) {
      this.logger.error(`Failed to cancel claim: ${error.code}`);

      if (error instanceof HttpException) throw error;

      // Easypost error
      if (error.code === 'UNPROCESSABLE_ENTITY') {
        throw new UnprocessableEntityException(error.errors);
      }

      if (error instanceof HttpException) throw error;
      throw new InternalServerErrorException('Failed to cancel claim');
    }
  }

  async getStatusHistory(organizationId: string, id: string) {
    try {
      const claim = await this.prisma.client.claim.findUnique({
        where: { id, organizationId },
      });

      if (!claim) throw new NotFoundException(`Claim with ID ${id} not found`);

      const epClaim = await this.easypostService.getClaim(claim.epClaimId);

      return epClaim.history;
    } catch (error) {
      this.logger.error(`Failed to retrieve status history: ${error.message}`);
      if (error instanceof HttpException) throw error;
      throw new InternalServerErrorException(
        'Failed to retrieve status history',
      );
    }
  }

  // =================INTERNAL METHODS===============

  private filesToDatabaseAttachments(files: UploadedFile[]) {
    const data: Prisma.ClaimAttachmentCreateManyClaimInput[] = [];

    files.forEach((file) => {
      if (file.fieldname === 'emailEvidenceAttachments')
        data.push({
          type: ClaimAttachmentType.email_evidence,
          fileUrl: file.url,
          mimeType: file.mimetype,
          fileName: file.fileName,
          fileSize: file.fileSize,
          key: file.key,
        });
      if (file.fieldname === 'invoiceAttachments')
        data.push({
          type: ClaimAttachmentType.invoice,
          fileUrl: file.url,
          mimeType: file.mimetype,
          fileName: file.fileName,
          fileSize: file.fileSize,
          key: file.key,
        });
      if (file.fieldname === 'supportingDocumentsAttachments')
        data.push({
          type: ClaimAttachmentType.supporting_documents,
          fileUrl: file.url,
          mimeType: file.mimetype,
          fileName: file.fileName,
          fileSize: file.fileSize,
          key: file.key,
        });
    });

    return data;
  }

  private filesToEasypostAttachments(files: FilesToUpload[]) {
    const attachments: {
      emailEvidenceAttachments: string[];
      invoiceAttachments: string[];
      supportingDocumentsAttachments: string[];
    } = {
      emailEvidenceAttachments: [],
      invoiceAttachments: [],
      supportingDocumentsAttachments: [],
    };

    files.forEach((file) => {
      if (file.fieldname === 'emailEvidenceAttachments')
        attachments.emailEvidenceAttachments.push(
          file.buffer.toString('base64'),
        );
      if (file.fieldname === 'invoiceAttachments')
        attachments.invoiceAttachments.push(file.buffer.toString('base64'));
      if (file.fieldname === 'supportingDocumentsAttachments')
        attachments.supportingDocumentsAttachments.push(
          file.buffer.toString('base64'),
        );
    });

    return attachments;
  }

  private async compensate(steps: { type: string; data?: any }[]) {
    // TODO: Implement compensation

    this.logger.error(
      `Compensating for failed steps ${steps.map((step) => step.type).join(', ')} in this order`,
    );
  }

  private async generateClaimNo(
    tx: Prisma.TransactionClient,
    organizationId: string,
  ) {
    const today = new Date();
    today.setDate(1);
    today.setHours(0, 0, 0, 0);
    const dateStr = format(today, 'MMyyyy');
    const prefix = `CLM-${dateStr}-`;

    const counterRecord = await tx.counter.upsert({
      where: {
        organizationId_type_date: {
          organizationId,
          type: CounterType.claim,
          date: today,
        },
      },
      update: { count: { increment: 1 } },
      create: {
        organizationId,
        type: CounterType.claim,
        date: today,
        count: 1,
      },
    });

    const sequenceStr = counterRecord.count.toString().padStart(5, '0');
    return prefix + sequenceStr;
  }
}
