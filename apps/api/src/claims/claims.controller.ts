import { Controller, Get, Param, Post, Query } from '@nestjs/common';
import { User } from '@repo/database';
import { TypedMultipart } from 'src/common/decorators/typed-multipart.decorator';
import { CurrentUser } from 'src/common/decorators/user.decorator';
import { FilesToUpload } from 'src/common/types/upload-files';
import { ClaimsService } from './claims.service';
import { ClaimQueryDto } from './dto/claim-query.dto';
import { CreateClaimDto } from './dto/create-claim.dto';

@Controller('claims')
export class ClaimsController {
  constructor(private readonly claimsService: ClaimsService) {}

  @Post('create')
  create(
    @CurrentUser() user: User,
    @(TypedMultipart<CreateClaimDto>()(CreateClaimDto))
    multipartData: {
      fields: CreateClaimDto;
      files: FilesToUpload[];
    },
  ) {
    const { fields, files } = multipartData;
    return this.claimsService.create(user, fields, files);
  }

  @Get()
  findAll(
    @CurrentUser('organizationId') organizationId: string,
    @Query() query: ClaimQueryDto,
  ) {
    return this.claimsService.findAll(organizationId, query);
  }

  @Get(':id')
  findOne(
    @CurrentUser('organizationId') organizationId: string,
    @Param('id') id: string,
  ) {
    return this.claimsService.findOne(organizationId, id);
  }

  @Post(':id/cancel')
  cancel(
    @CurrentUser('organizationId') organizationId: string,
    @Param('id') id: string,
  ) {
    return this.claimsService.cancel(organizationId, id);
  }

  @Get(':id/status-history')
  getStatusHistory(
    @CurrentUser('organizationId') organizationId: string,
    @Param('id') id: string,
  ) {
    return this.claimsService.getStatusHistory(organizationId, id);
  }
}
