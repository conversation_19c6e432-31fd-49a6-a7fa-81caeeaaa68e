import { Module } from '@nestjs/common';
import { EasypostModule } from 'src/infrastructure/easypost/easypost.module';
import { StorageModule } from 'src/infrastructure/storage/storage.module';
import { ClaimsController } from './claims.controller';
import { ClaimsService } from './claims.service';

@Module({
  imports: [EasypostModule, StorageModule],
  controllers: [ClaimsController],
  providers: [ClaimsService],
})
export class ClaimsModule {}
