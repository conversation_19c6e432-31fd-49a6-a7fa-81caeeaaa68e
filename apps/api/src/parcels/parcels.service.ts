import {
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from 'src/infrastructure/prisma/prisma.service';
import { CreateParcelDto } from './dto/create-parcel.dto';
import { DeleteParcelsDto } from './dto/delete-parcels.dto';
import { UpdateParcelDto } from './dto/update-parcel.dto';

@Injectable()
export class ParcelsService {
  private readonly logger = new Logger(ParcelsService.name);

  constructor(private readonly prisma: PrismaService) {}

  // ✅
  async create(organizationId: string, createParcelDto: CreateParcelDto) {
    try {
      const response = await this.prisma.client.parcel.create({
        data: {
          ...createParcelDto,
          organizationId,
        },
      });
      return response;
    } catch (error) {
      this.logger.error(`Failed to create parcel: ${error.message}`);
      throw new InternalServerErrorException('Failed to create parcel');
    }
  }

  // ✅
  async findAll(organizationId: string) {
    try {
      const response = await this.prisma.client.parcel.findMany({
        where: { organizationId },
      });
      return response;
    } catch (error) {
      this.logger.error(`Failed to retrieve parcels: ${error.message}`);
      throw new InternalServerErrorException('Failed to retrieve parcels');
    }
  }

  // ✅
  async update(
    organizationId: string,
    id: string,
    updateParcelDto: UpdateParcelDto,
  ) {
    try {
      const response = await this.prisma.client.parcel.update({
        where: { id, organizationId },
        data: updateParcelDto,
      });
      return response;
    } catch (error) {
      this.logger.error(`Failed to update parcel: ${error.message}`);
      if (error.code === 'P2025') {
        throw new NotFoundException(`Parcel with ID ${id} not found`);
      }
      throw new InternalServerErrorException('Failed to update parcel');
    }
  }

  // ✅
  async remove(organizationId: string, id: string) {
    try {
      const response = await this.prisma.client.parcel.delete({
        where: { id, organizationId },
      });
      return response;
    } catch (error) {
      this.logger.error(`Failed to delete parcel: ${error.message}`);
      if (error.code === 'P2025') {
        throw new NotFoundException(`Parcel with ID ${id} not found`);
      }
      throw new InternalServerErrorException('Failed to delete parcel');
    }
  }

  // ✅
  async removeMany(organizationId: string, deleteParcelsDto: DeleteParcelsDto) {
    try {
      const { ids } = deleteParcelsDto;
      const response = await this.prisma.client.parcel.deleteMany({
        where: { id: { in: ids }, organizationId },
      });
      return response;
    } catch (error) {
      this.logger.error(`Failed to delete parcels: ${error.message}`);
      throw new InternalServerErrorException('Failed to delete parcels');
    }
  }
}
