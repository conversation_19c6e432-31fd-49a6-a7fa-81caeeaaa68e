import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Put,
} from '@nestjs/common';
import { ParcelsService } from './parcels.service';
import { CreateParcelDto } from './dto/create-parcel.dto';
import { UpdateParcelDto } from './dto/update-parcel.dto';
import { CurrentUser } from 'src/common/decorators/user.decorator';
import { DeleteParcelsDto } from './dto/delete-parcels.dto';

@Controller('parcels')
export class ParcelsController {
  constructor(private readonly parcelsService: ParcelsService) {}

  // ✅
  @Post()
  create(
    @CurrentUser('organizationId') organizationId: string,
    @Body() createParcelDto: CreateParcelDto,
  ) {
    return this.parcelsService.create(organizationId, createParcelDto);
  }

  // ✅
  @Get()
  findAll(@CurrentUser('organizationId') organizationId: string) {
    return this.parcelsService.findAll(organizationId);
  }

  // ✅
  @Delete()
  removeMany(
    @CurrentUser('organizationId') organizationId: string,
    @Body() deleteParcelsDto: DeleteParcelsDto,
  ) {
    return this.parcelsService.removeMany(organizationId, deleteParcelsDto);
  }

  // ✅
  @Put(':id')
  update(
    @CurrentUser('organizationId') organizationId: string,
    @Param('id') id: string,
    @Body() updateParcelDto: UpdateParcelDto,
  ) {
    return this.parcelsService.update(organizationId, id, updateParcelDto);
  }

  // ✅
  @Delete(':id')
  remove(
    @CurrentUser('organizationId') organizationId: string,
    @Param('id') id: string,
  ) {
    return this.parcelsService.remove(organizationId, id);
  }
}
