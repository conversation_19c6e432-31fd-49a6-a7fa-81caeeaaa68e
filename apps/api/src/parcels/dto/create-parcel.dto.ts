import { Type } from 'class-transformer';
import {
  IsNotEmpty,
  <PERSON><PERSON><PERSON>ber,
  IsOptional,
  IsPositive,
  IsString,
  ValidateIf,
} from 'class-validator';
export class CreateParcelDto {
  @IsString()
  @IsNotEmpty({ message: 'Parcel name is required' })
  name: string;

  @ValidateIf((object) => !object.predefinedPackage)
  @IsNotEmpty({
    message: 'Length is required when predefined package is not specified',
  })
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsPositive({ message: 'Length must be greater than 0' })
  @Type(() => Number)
  length?: number;

  @ValidateIf((object) => !object.predefinedPackage)
  @IsNotEmpty({
    message: 'Width is required when predefined package is not specified',
  })
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsPositive({ message: 'Width must be greater than 0' })
  @Type(() => Number)
  width: number;

  @ValidateIf((object) => !object.predefinedPackage)
  @IsNotEmpty({
    message: 'Height is required when predefined package is not specified',
  })
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsPositive({ message: 'Height must be greater than 0' })
  @Type(() => Number)
  height: number;

  @IsNumber({ maxDecimalPlaces: 2 })
  @IsPositive({ message: 'Weight must be greater than 0' })
  @Type(() => Number)
  weight: number;

  @IsString()
  @IsOptional()
  predefinedPackage?: string;
}
