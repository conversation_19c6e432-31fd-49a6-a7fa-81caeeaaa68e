{"timestamp": "2025-08-19T04:01:30.994Z", "body": {"id": "evt_1RxgjmRHb8e5MUGoCwZGLBlI", "object": "event", "api_version": "2025-06-30.basil", "created": 1755576090, "data": {"object": {"id": "seti_1RxgjdRHb8e5MUGoa0MONBEE", "object": "setup_intent", "application": null, "automatic_payment_methods": {"allow_redirects": "always", "enabled": true}, "cancellation_reason": null, "client_secret": "seti_1RxgjdRHb8e5MUGoa0MONBEE_secret_StThYkM9jn2wur7xZbgCZGfBx1Sx99U", "created": 1755576081, "customer": "cus_StAQLVgXx8tmTc", "description": null, "flow_directions": null, "last_setup_error": null, "latest_attempt": "setatt_1RxgjlRHb8e5MUGobo5ZIAfn", "livemode": false, "mandate": null, "metadata": {}, "next_action": null, "on_behalf_of": null, "payment_method": "pm_1RxgjlRHb8e5MUGoH9iech5m", "payment_method_configuration_details": {"id": "pmc_1Ri8kdRHb8e5MUGohI13v7jH", "parent": null}, "payment_method_options": {"card": {"mandate_options": null, "network": null, "request_three_d_secure": "automatic"}}, "payment_method_types": ["card"], "single_use_mandate": null, "status": "succeeded", "usage": "off_session"}}, "livemode": false, "pending_webhooks": 1, "request": {"id": "req_k8n4t9zXT8LZQ6", "idempotency_key": "93923af6-2866-4010-85ce-4ed48afffa3b"}, "type": "setup_intent.succeeded"}, "headers": {"host": "measured-feasible-racer.ngrok-free.app", "user-agent": "Stripe/1.0 (+https://stripe.com/docs/webhooks)", "content-length": "1554", "accept": "*/*; q=0.5, application/json", "cache-control": "no-cache", "content-type": "application/json; charset=utf-8", "stripe-signature": "t=1755576090,v1=5f2bc7f8513e6f25a6cb074c7fe14cfacf82701e61a4080a85ccc406cee7f7ad,v0=719cb36880525956a8f16c93c3d6fb37b38e079c6383754a66b7e1f79f21d7ca", "x-forwarded-for": "**************", "x-forwarded-host": "measured-feasible-racer.ngrok-free.app", "x-forwarded-proto": "https", "accept-encoding": "gzip"}}