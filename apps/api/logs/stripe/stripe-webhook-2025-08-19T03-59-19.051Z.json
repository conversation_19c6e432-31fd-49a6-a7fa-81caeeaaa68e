{"timestamp": "2025-08-19T03:59:19.053Z", "body": {"id": "evt_1RxgQeRHb8e5MUGosSKYM0Km", "object": "event", "api_version": "2025-06-30.basil", "created": 1755574904, "data": {"object": {"id": "seti_1RxgQeRHb8e5MUGoouaNz5uk", "object": "setup_intent", "application": null, "automatic_payment_methods": null, "cancellation_reason": null, "client_secret": "seti_1RxgQeRHb8e5MUGoouaNz5uk_secret_StTN1kJ9K61KT2uBnyJKwg42IfZZA2f", "created": 1755574904, "customer": null, "description": "(created by Stripe CLI)", "flow_directions": null, "last_setup_error": null, "latest_attempt": "setatt_1RxgQeRHb8e5MUGoHUUw79Tc", "livemode": false, "mandate": null, "metadata": {}, "next_action": null, "on_behalf_of": null, "payment_method": "pm_1RxgQdRHb8e5MUGo6LGo1aNH", "payment_method_configuration_details": null, "payment_method_options": {"card": {"mandate_options": null, "network": null, "request_three_d_secure": "automatic"}}, "payment_method_types": ["card"], "single_use_mandate": null, "status": "succeeded", "usage": "off_session"}}, "livemode": false, "pending_webhooks": 1, "request": {"id": "req_aPHhmyKo1Cz4DK", "idempotency_key": "362e7699-800b-4103-8034-e7530e021694"}, "type": "setup_intent.succeeded"}, "headers": {"host": "measured-feasible-racer.ngrok-free.app", "user-agent": "Stripe/1.0 (+https://stripe.com/docs/webhooks)", "content-length": "1419", "accept": "*/*; q=0.5, application/json", "cache-control": "no-cache", "content-type": "application/json; charset=utf-8", "stripe-signature": "t=1755575958,v1=014776ddf4a3fd36af615bbe0aa473d24a829e00da5da18b63799383ddfbe878,v0=bc319a1e692826d5194882c43ce3687f5eaa8c527c203c1150d90d5941213c6f", "x-forwarded-for": "**************", "x-forwarded-host": "measured-feasible-racer.ngrok-free.app", "x-forwarded-proto": "https", "accept-encoding": "gzip"}}