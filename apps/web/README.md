# Shipping Management App

This project is a shipping management web application built with the following modern tech stack:

## 🚀 Tech Stack

- **State Management**:
  - [Redux Toolkit](https://redux-toolkit.js.org/)
  - [RTK Query](https://redux-toolkit.js.org/rtk-query/overview)
- **Authentication & Authorization**:
  - [NextAuth.js v5 (Auth.js)](https://authjs.dev/) with [Prisma Adapter](https://authjs.dev/getting-started/adapters/prisma)
- **Database**:
  - [PostgreSQL](https://www.postgresql.org/) (via [Neon.tech](https://neon.tech/))
- **ORM**:
  - [Prisma ORM](https://www.prisma.io/)
