{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port 3000", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@auth/prisma-adapter": "^2.8.0", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^4.1.3", "@prisma/client": "6.5.0", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.13", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-tooltip": "^1.1.8", "@reduxjs/toolkit": "^2.7.0", "@repo/database": "workspace:*", "@repo/easypost-types": "workspace:*", "@repo/shared-data": "workspace:*", "@stripe/react-stripe-js": "^3.9.0", "@stripe/stripe-js": "^7.8.0", "@tanstack/react-table": "^8.21.2", "@types/react-signature-canvas": "^1.0.7", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "lucide-react": "^0.482.0", "next": "15.2.3", "next-auth": "5.0.0-beta.25", "next-themes": "^0.4.6", "react": "^19.0.0", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-hook-form": "^7.55.0", "react-redux": "^9.2.0", "react-signature-canvas": "^1.0.7", "sonner": "^2.0.3", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^1.1.2", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@tailwindcss/typography": "^0.5.16", "@types/crypto-js": "^4.2.2", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.3", "tailwindcss": "^4", "typescript": "^5"}}