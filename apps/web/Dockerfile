FROM node:20-alpine AS base

FROM base AS builder
RUN apk update
RUN apk add --no-cache libc6-compat
# Set working directory
WORKDIR /app

RUN npm install -g pnpm
RUN npm install -g turbo@^2
COPY . .

RUN turbo prune web --docker

FROM base AS installer
RUN apk update
RUN apk add --no-cache libc6-compat
WORKDIR /app

RUN npm install -g pnpm

COPY --from=builder /app/out/json/ .
COPY --from=builder /app/apps/web/.env.production ./apps/web/.env.production
RUN pnpm install 

COPY --from=builder /app/out/full/ .

# Explicitly generate prisma client in the database package
# RUN cd packages/database && npx prisma generate

RUN pnpm turbo run build --filter=web

FROM base AS runner
WORKDIR /app

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=installer --chown=nextjs:nodejs /app/apps/web/.env.production ./.env.production
COPY --from=installer --chown=nextjs:nodejs /app/packages ./packages


USER nextjs

COPY --from=installer --chown=nextjs:nodejs /app/apps/web/.next/standalone ./
COPY --from=installer --chown=nextjs:nodejs /app/apps/web/.next/static ./apps/web/.next/static
COPY --from=installer --chown=nextjs:nodejs /app/apps/web/public ./apps/web/public

# CMD node apps/web/server.js
CMD ["node", "./apps/web/server.js"]