import { z } from "zod";

const CreateClaimSchema = z
  .object({
    type: z.string().min(1, { message: "Claim type is required" }),
    amount: z
      .string()
      .min(1, { message: "Amount is required" })
      .refine(
        val => {
          const num = Number(val);
          return !isNaN(num) && num > 0;
        },
        { message: "Amount must be greater than 0" }
      ),
    reference: z.string(),
    description: z.string().min(25),
    invoiceAttachments: z
      .array(z.instanceof(File))
      .min(1, "At least one file is required."),
    emailEvidenceAttachments: z
      .array(z.instanceof(File))
      .min(1, "At least one file is required."),
    supportingDocumentsAttachments: z.array(z.instanceof(File)),
  })
  .superRefine((data, ctx) => {
    const requiredTypes = ["damage", "theft"];
    if (
      requiredTypes.includes(data.type) &&
      (!data.supportingDocumentsAttachments ||
        data.supportingDocumentsAttachments.length === 0)
    ) {
      ctx.addIssue({
        path: ["supportingDocumentsAttachments"],
        code: z.ZodIssueCode.custom,
        message: "At least one file is required for damage or theft claims.",
      });
    }
  });

export const defaultValues: z.infer<typeof CreateClaimSchema> = {
  // trackingCode: "",
  type: "",
  invoiceAttachments: [],
  emailEvidenceAttachments: [],
  // replacementShipped: false,
  // items: [],
  // amount: 0,
  amount: "",
  reference: "",
  description: "",
  supportingDocumentsAttachments: [],
};

export default CreateClaimSchema;
