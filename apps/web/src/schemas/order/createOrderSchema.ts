import AddressSchema, {
  defaultValues as addressDefaultValues,
} from "@/schemas/common/AddressSchema";
import { z } from "zod";
import OrderItemSchema from "./OrderItemsSchema";
import ParcelSchema from "../common/ParcelSchema";

export const createOrderSchema = z.object({
  orderNo: z
    .string()
    .optional()
    .transform(val => (val?.trim() === "" ? null : val)),
  epShipmentId: z.string().optional(),
  orderDate: z.date({ required_error: "Order date is required" }),
  carrier: z.string().nullable(),
  service: z.string().nullable(),
  fromAddressId: z.string().min(1, "From address is required"),
  toAddress: AddressSchema,
  orderItems: z.array(OrderItemSchema).min(1, "At least one item is required"),
  notes: z.string().optional(),
  parcel: ParcelSchema,
  insurance: z.number().optional(),
  signature: z.string().optional(),

  batchId: z.string().nullable().optional(),
  // status: z.string().optional(),
});

export const getDefaultValues = () => {
  return {
    orderNo: "",
    epShipmentId: undefined,
    orderDate: new Date(),
    carrier: null,
    service: null,
    fromAddressId: "",
    toAddress: addressDefaultValues,
    orderItems: [],
    notes: "",
    parcel: {
      weight: 0,
      length: 0,
      width: 0,
      height: 0,
      predefinedPackage: null,
    },
    insurance: 0,
    signature: undefined,
  };
};
