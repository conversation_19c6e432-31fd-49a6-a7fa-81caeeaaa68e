import { z } from "zod";

const OrderItemSchema = z.object({
  product: z.object({
    id: z.string().optional(),
    sku: z.string().min(1, "SKU is required"),
    name: z.string().min(1, "Name is required"),
  }),
  quantity: z.number().min(1, "Quantity is required"),
  price: z.number().min(0, "Price is required"),
});

export const defaultValues = {
  product: {
    id: undefined,
    sku: "",
    name: "",
  },
  quantity: 0,
  price: 0.0,
};

export default OrderItemSchema;
