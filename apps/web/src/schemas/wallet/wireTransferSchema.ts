import { z } from "zod";

export const wireTransferSchema = z.object({
  date: z.date({ message: "Please select a date" }),
  amount: z.coerce.number().min(1),
  bankReference: z.string().optional(),
  bankName: z.string().optional(),
  transferConfirmation: z
    .array(z.instanceof(File))
    .min(1, "At least one file is required"),
});

export const defaultValues: z.infer<typeof wireTransferSchema> = {
  date: new Date(),
  amount: 0,
  bankReference: "",
  bankName: "",
  transferConfirmation: [],
};
