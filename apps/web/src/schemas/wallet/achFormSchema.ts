import { z } from "zod";

export const customerInfoSchema = z.object({
  customerName: z.string().min(1, "Name is required"),
  company: z.string().optional(),
  customerEmail: z.string().email("Invalid email address"),
  customerPhone: z.string().min(1, "Phone number is required"),
  customerAddress: z.string().min(1, "Address is required"),
});

export const bankInfoSchema = z.object({
  bankName: z.string().min(1, "Bank Name is required"),
  bankAccountName: z.string().min(1, "Name on bank is required"),
  bankAccountNo: z.string().min(1, "Bank account number is required"),
  bankRoutingNo: z.string().min(1, "Bank routing number is required"),
  bankBillingAddress: z.string().min(1, "Billing address is required"),
  bankHolderType: z.string().min(1, "Bank holder type is required"),
  bankAccountType: z.string().min(1, "Bank account type is required"),
});

export const fundingSchema = z.object({
  initialFunding: z.coerce.number().min(50, "Minimum is $50"),
  // rechargeThreshold: z.coerce.number().min(50, "Minimum is $50"),
  // rechargeAmount: z.coerce.number().min(50, "Minimum is $50"),

  signatureFile: z.string().min(1, "Please sign your name"),
});

export const achFormSchema = customerInfoSchema
  .merge(bankInfoSchema)
  .merge(fundingSchema);

export const defaultValues: z.infer<typeof achFormSchema> = {
  customerName: "",
  company: "",
  customerEmail: "",
  customerPhone: "",
  customerAddress: "",
  bankName: "",
  bankAccountName: "",
  bankAccountNo: "",
  bankRoutingNo: "",
  bankBillingAddress: "",
  bankAccountType: "",
  bankHolderType: "",
  initialFunding: 0,
  // rechargeThreshold: 0,
  // rechargeAmount: 0,
  signatureFile: "",
};
