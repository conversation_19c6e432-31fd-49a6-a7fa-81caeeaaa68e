import { z } from "zod";

const ReturnItemSchema = z
  .object({
    selected: z.boolean().default(false),
    orderId: z.number(),
    quantity: z.preprocess(
      val => (val !== "" ? Number(val) : undefined),
      z.number().int().positive()
    ),
    reason: z.string().min(1),
    maxQuantity: z.number().int().positive(),
  })
  .superRefine((data, ctx) => {
    if (data.selected && (data.quantity == null || data.quantity <= 0)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Quantity must be greater than 0 when selected",
        path: ["quantity"],
      });
    }

    if (data.quantity > data.maxQuantity) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: `Quantity must not exceed ${data.maxQuantity}`,
        path: ["quantity"],
      });
    }

    if (data.selected && (!data.reason || data.reason.trim() === "")) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Reason is required when item is selected",
        path: ["reason"],
      });
    }
  });

export default ReturnItemSchema;
