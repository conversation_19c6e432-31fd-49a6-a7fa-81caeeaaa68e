import { z } from "zod";
import ReturnItemSchema from "./ReturnItemSchema";

const PendingReturnItemsSchema = z
  .object({
    returnItems: z.array(ReturnItemSchema),
  })
  .superRefine((data, ctx) => {
    const hasAtLeastOneSelected = data.returnItems.some(item => item.selected);

    if (!hasAtLeastOneSelected) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Please select at least one item to return.",
        path: ["returnItems"],
      });
    }
  });

export default PendingReturnItemsSchema;
