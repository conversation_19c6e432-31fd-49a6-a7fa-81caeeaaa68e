import { getAllServiceIds } from "@repo/shared-data";
import { z } from "zod";
import AddressSchema from "../common/AddressSchema";
import ParcelSchema from "../common/ParcelSchema";

const SERVICE_IDS = getAllServiceIds();

const CreateReturnSchema = z.object({
  rma: z
    .string()
    .optional()
    .transform(val => (val?.trim() === "" ? null : val)),
  epShipmentId: z.string().optional().nullable(),
  service: z.preprocess(
    val => (val === "" ? null : val),
    z.enum(SERVICE_IDS as [string, ...string[]]).nullable()
  ),
  toAddressId: z.string().min(1, "To address is required"),
  fromAddress: AddressSchema,
  notes: z.string().optional(),
  parcel: ParcelSchema,
  insurance: z.number().optional(),
  signature: z.string().optional(),
});

export default CreateReturnSchema;
