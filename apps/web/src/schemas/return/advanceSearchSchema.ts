import { z } from "zod";

export const advanceSearchSchema = z.object({
  rma: z.string().optional(),
  order: z.object({
    orderNo: z.string().optional(),
    storeId: z.string().optional(),
  }),
  trackingCode: z.string().optional(),
  // fromAddress: z.object({
  //   name: z.string().optional(),
  // }),
});

export const defaultValues = {
  rma: "",
  order: {
    orderNo: "",
    storeId: "",
  },
  trackingCode: "",
  // fromAddress: {
  //   name: "",
  // },
};
