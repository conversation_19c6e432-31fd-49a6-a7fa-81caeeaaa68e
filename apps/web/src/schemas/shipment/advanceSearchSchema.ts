import { z } from "zod";

export const advanceSearchSchema = z.object({
  shipmentNo: z.string().optional(),
  order: z.object({
    orderNo: z.string().optional(),
    storeId: z.string().optional(),
  }),
  trackingCode: z.string().optional(),
  toAddress: z.object({
    name: z.string().optional(),
  }),
});

export const defaultValues = {
  shipmentNo: "",
  order: {
    orderNo: "",
    storeId: "",
  },
  trackingCode: "",
  toAddress: {
    name: "",
  },
};
