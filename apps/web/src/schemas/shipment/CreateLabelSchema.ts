import { z } from "zod";
import ParcelSchema from "../common/ParcelSchema";
import AddressSchema from "../common/AddressSchema";

const CreateNewLabelSchema = z.object({
  fromAddressId: z.string().min(1, "From address is required"),
  toAddress: AddressSchema,
  carrier: z.string().nullable(),
  service: z.string().nullable(),
  parcel: ParcelSchema,
  epShipmentId: z.string().optional(),
  insurance: z.number().optional(),
  rateId: z.string().optional(),
});

export default CreateNewLabelSchema;
