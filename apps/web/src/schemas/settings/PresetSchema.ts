import { z } from "zod";

const PresetSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1),
  shipFrom: z.string().min(1),
  service: z.string().min(1),
  packageType: z.string().min(1),
  height: z.number().min(0.1),
  length: z.number().min(0.1),
  weight: z.number().min(0.1),
  width: z.number().min(0.1),
});

export const defaultValues: z.infer<typeof PresetSchema> = {
  id: "",
  name: "",
  shipFrom: "",
  service: "",
  packageType: "",
  height: 0,
  length: 0,
  weight: 0,
  width: 0,
};

export default PresetSchema;
