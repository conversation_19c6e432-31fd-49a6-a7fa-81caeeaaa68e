import { z } from "zod";
import PasswordsSchema from "./PasswordsSchema";

const RegisterSchema = z
  .object({
    companyName: z.string().min(1, { message: "Company name is required" }),
    firstName: z.string().min(1, { message: "First name is required" }),
    lastName: z.string().min(1, { message: "Last name is required" }),
    phone: z.string().min(1, { message: "Phone number is required" }),
    email: z.string().email({ message: "Invalid email address" }),
    shipmentVolume: z
      .string()
      .min(1, { message: "Shipment volume is required" }),
    terms: z.boolean().refine(val => val === true, {
      message: "You must agree to the terms and conditions",
    }),
  })
  .merge(PasswordsSchema)
  .refine(data => data.password === data.confirmPassword, {
    path: ["confirmPassword"],
    message: "Passwords do not match",
  });

export default RegisterSchema;
