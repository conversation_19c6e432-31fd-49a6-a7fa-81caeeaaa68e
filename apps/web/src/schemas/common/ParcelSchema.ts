import { z } from "zod";

export const BaseParcelSchema = z.object({
  weight: z.number().min(0.1, "Please specify"),
  length: z.number().optional(),
  width: z.number().optional(),
  height: z.number().optional(),
  predefinedPackage: z
    .string()
    .nullable()
    .transform(val => (val?.trim() === "" ? null : val)),
});

const ParcelSchema = BaseParcelSchema.superRefine((data, ctx) => {
  if (!data.predefinedPackage) {
    if (!data.length || data.length < 0.1) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ["length"],
      });
    }
    if (!data.width || data.width < 0.1) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ["width"],
      });
    }
    if (!data.height || data.height < 0.1) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ["height"],
      });
    }
  }
});

export default ParcelSchema;
