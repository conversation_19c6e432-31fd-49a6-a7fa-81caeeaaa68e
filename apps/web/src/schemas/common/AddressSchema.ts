import { z } from "zod";

const AddressSchema = z.object({
  name: z.string().min(1, "Name is required"),
  company: z.string().optional(),
  street1: z.string().min(1, "Street 1 is required"),
  street2: z.string().optional(),
  city: z.string().min(1, "City is required"),
  state: z.string().min(1, "State is required"),
  country: z.string().min(1, "Country is required"),
  zip: z.string().min(1, "Zip code is required"),
  phone: z.string().min(10, "Phone number is required and at least 10 digits"),
  email: z.string().email("Invalid email address"),
  residential: z.boolean().optional(),
  verified: z.boolean().optional(),
});

export const defaultValues: z.infer<typeof AddressSchema> = {
  name: "",
  company: "",
  street1: "",
  street2: "",
  city: "",
  state: "",
  country: "",
  zip: "",
  phone: "",
  email: "",
  residential: false,
  verified: false,
};

export default AddressSchema;
