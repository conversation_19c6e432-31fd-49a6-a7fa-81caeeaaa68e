import { z } from "zod";

const emailValidate = z.union([
  z.string().transform(val => [val]),
  z.array(z.string().email({ message: "Invalid email address" })).min(1),
]);

const InviteSchema = z.object({
  emails: emailValidate,
  permissionIds: z.array(z.string()).refine(value => value.some(item => item), {
    message: "At least one permission must be selected.",
  }),
});

export const defaultValues: z.infer<typeof InviteSchema> = {
  emails: [],
  permissionIds: [],
};

export default InviteSchema;
