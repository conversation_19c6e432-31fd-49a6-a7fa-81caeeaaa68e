"use server";

import { sendInviteEmail } from "@/lib/mail";
import { UserInvite } from "@repo/database";

export const inviteUsers = async (
  userInvites: UserInvite[],
  errors: { email: string; error: string }[]
) => {
  const messages: { message: string; type: "success" | "error" }[] = [];

  for (const result of userInvites) {
    await sendInviteEmail(result.email, result.token);
    messages.push({
      message: `Invited ${result.email} successfully`,
      type: "success",
    });
  }

  for (const error of errors) {
    messages.push({
      message: `Failed to invite ${error.email}, ${error.error}`,
      type: "error",
    });
  }

  return messages;
};
