"use server";

import ParcelSchema from "@/schemas/common/ParcelSchema";
import { Address } from "@repo/database";
import * as z from "zod";
import { getJwtToken } from "../auth/getToken";
import AddressSchema from "@/schemas/common/AddressSchema";

export const generateRates = async (
  fromAddress: Partial<z.infer<typeof AddressSchema>> & { id?: string },
  toAddress: Partial<z.infer<typeof AddressSchema>> & { id?: string },
  parcel: z.infer<typeof ParcelSchema>,
  carrierAccountIds?: string[]
) => {
  try {
    const url = `${process.env.API_URL}/rates/generate`;
    const jwtToken = await getJwtToken();

    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${jwtToken}`,
      },

      // TODO: accept address object with optional id for both from and to address
      /*
      If address id is provided, we will use it to get the address from the database.
      If not, the whole address object will be send to API and easypost.
      */
      body: JSON.stringify({
        fromAddress,
        toAddress,
        parcel,
        carrierAccountIds,
      }),
    });

    if (!response.ok) {
      throw new Error("Failed to generate rates");
    }

    return { success: true, data: await response.json() };
  } catch (error) {
    console.log(error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to generate rates",
    };
  }
};

export const getRates = async (
  fromAddress: Address,
  toAddress: z.infer<typeof AddressSchema>,
  parcel: z.infer<typeof ParcelSchema>
) => {
  try {
    const url = `${process.env.API_URL}/rates/generate-with-address-object`;
    const jwtToken = await getJwtToken();

    // Destructure the fromAddress object
    const { epAddressId, organizationId, id, ...restFromAddress } = fromAddress;

    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${jwtToken}`,
      },
      body: JSON.stringify({
        fromAddress: restFromAddress,
        toAddress,
        parcel,
      }),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error("Failed to generate rates");
    }

    return { success: true, data };
  } catch (error) {
    console.log(error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to generate rates",
    };
  }
};
