"use server";

import { createOrderSchema } from "@/schemas/order/createOrderSchema";
import * as z from "zod";
import { getJwtToken } from "../auth/getToken";
import { generateRates } from "./generateRates";

export const generateRatesAndUpdateOrder = async (
  orderId: string,
  order: z.infer<typeof createOrderSchema>
) => {
  try {
    const parsedOrder = createOrderSchema.parse(order);
    const { fromAddressId, toAddress, parcel } = parsedOrder;

    const { success, data, error } = await generateRates(
      { id: fromAddressId },
      toAddress,
      parcel
    );

    if (!success) {
      throw new Error(error);
    }
    // TODO: Move this to /data/order.ts later
    const jwtToken = await getJwtToken();
    const response = await fetch(`${process.env.API_URL}/orders/${orderId}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${jwtToken}`,
      },
      body: JSON.stringify({
        ...parsedOrder,
        epShipmentId: data.epShipmentId,
      }),
    });

    if (!response.ok) {
      throw new Error("Failed to update order");
    }
    return { success: true, data: await response.json() };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, error: "Invalid order data" };
    }
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to generate rates and update order",
    };
  }
};
