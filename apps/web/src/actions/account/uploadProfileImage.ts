"use server";

import { getJwtToken } from "../auth/getToken";

export const updateProfileImage = async (payload: FormData) => {
  try {
    const url = `${process.env.API_URL}/account/profile/image`;
    const jwtToken = await getJwtToken();

    const response = await fetch(url, {
      method: "PATCH",
      headers: {
        Authorization: `Bearer ${jwtToken}`,
      },

      body: payload,
    });

    if (!response.ok) {
      throw new Error("Failed to update profile image");
    }

    return { success: true, data: await response.json() };
  } catch (error) {
    if (process.env.NODE_ENV === "development") console.log(error);

    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to update profile image",
    };
  }
};
