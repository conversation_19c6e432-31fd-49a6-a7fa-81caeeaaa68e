"use server";

import { getJwtToken } from "../auth/getToken";

export const deleteProfileImage = async () => {
  try {
    const url = `${process.env.API_URL}/account/profile/image`;
    const jwtToken = await getJwtToken();

    const response = await fetch(url, {
      method: "DELETE",
      headers: {
        Authorization: `Bearer ${jwtToken}`,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to delete profile image");
    }

    return { success: true, data: await response.json() };
  } catch (error) {
    if (process.env.NODE_ENV === "development") console.log(error);

    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to delete profile image",
    };
  }
};
