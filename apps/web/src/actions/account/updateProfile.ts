"use server";

import { getJwtToken } from "../auth/getToken";

type UpdateProfilePayload = {
  firstName: string;
  lastName: string;
};

export const updateProfile = async (payload: UpdateProfilePayload) => {
  try {
    const url = `${process.env.API_URL}/account/profile/info`;
    const jwtToken = await getJwtToken();

    const response = await fetch(url, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${jwtToken}`,
      },

      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      throw new Error("Failed to update profile");
    }

    return { success: true, data: await response.json() };
  } catch (error) {
    if (process.env.NODE_ENV === "development") console.log(error);

    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to update profile",
    };
  }
};
