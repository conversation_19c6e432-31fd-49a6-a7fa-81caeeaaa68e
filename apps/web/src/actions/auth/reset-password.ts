"use server";

import ResetPasswordSchema from "@/schemas/auth/ResetPasswordSchema";
import { z } from "zod";

export const resetPassword = async (
  value: z.infer<typeof ResetPasswordSchema>,
  token: string
) => {
  const parsedData = ResetPasswordSchema.safeParse(value);
  if (!parsedData.success) {
    return { success: false };
  }

  const payload = {
    password: parsedData.data.password,
    token,
  };

  try {
    const url = `${process.env.API_URL}/auth/reset-password`;
    const response = await fetch(url, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      return {
        success: false,
      };
    }

    return { success: true };
  } catch (error) {
    console.error(error);
    return { success: false };
  }
};
