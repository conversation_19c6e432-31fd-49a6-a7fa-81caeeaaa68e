"use server";

const resendVerificationToken = async (email: string) => {
  console.log(email);
  try {
    const response = await fetch(
      `${process.env.API_URL}/auth/verification-token/resend`,
      {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email: "<EMAIL>" }),
      }
    );

    if (!response.ok) throw new Error("Failed to resend verification token");
    return { success: "Verification token resent" };
  } catch (error) {
    if (error instanceof Error) throw new Error(error.message);
    throw new Error("Failed to resend verification token");
  }
};

export default resendVerificationToken;
