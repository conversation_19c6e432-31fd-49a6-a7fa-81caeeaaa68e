"use server";

import { User } from "@repo/database";

type AcceptInvitePayload = {
  firstName: string;
  lastName: string;
  password: string;
  token: string;
};

type AcceptInviteResponse = {
  success: boolean;
  data?: Pick<
    User,
    "id" | "email" | "firstName" | "lastName" | "organizationId"
  >;
  error?: string;
};

export const acceptInvite = async (
  payload: AcceptInvitePayload
): Promise<AcceptInviteResponse> => {
  try {
    const url = `${process.env.API_URL}/auth/accept-invite`;
    const response = await fetch(url, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(payload),
    });

    if (!response.ok) throw new Error("Could not create user from user invite");

    const data = await response.json();
    return { success: true, data };
  } catch (error) {
    if (process.env.NODE_ENV === "development") console.error(error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Something went wrong.",
    };
  }
};
