"use server";

import { getToken } from "next-auth/jwt";
import { cookies } from "next/headers";

export const getJwtToken = async (): Promise<string> => {
  const cookieStore = await cookies();
  const cookiesStr = cookieStore.toString();

  const token = await getToken({
    req: { headers: { cookie: cookiesStr } },
    secret: process.env.AUTH_SECRET,
    raw: true,
    cookieName:
      process.env.NODE_ENV === "production"
        ? "__Secure-authjs.session-token"
        : "authjs.session-token",
  });

  return token;
};
