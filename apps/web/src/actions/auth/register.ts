"use server";

import RegisterSchema from "@/schemas/auth/RegisterSchema";
import * as z from "zod";

export const register = async (values: z.infer<typeof RegisterSchema>) => {
  // Validate fields
  const validatedFields = RegisterSchema.safeParse(values);
  if (!validatedFields.success) return { error: "Invalid fields!" };

  const {
    companyName,
    firstName,
    lastName,
    phone,
    shipmentVolume,
    email,
    password,
  } = validatedFields.data;

  try {
    const response = await fetch(`${process.env.API_URL}/auth/register`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        companyName,
        firstName,
        lastName,
        phone,
        shipmentVolume,
        email,
        password,
      }),
    });

    if (!response.ok) throw new Error("Failed to register");

    return { success: "Successfully registered!" };
  } catch (error) {
    console.log(error);
    return { error: "Unexpected error" };
  }
};
