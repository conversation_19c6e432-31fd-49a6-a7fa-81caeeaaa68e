"use server";

export const verificationToken = async (token: string) => {
  try {
    const url = `${process.env.API_URL}/auth/password-reset-token/${token}`;
    const response = await fetch(url, {
      method: "GET",
      headers: { "Content-Type": "application/json" },
    });

    const data = await response.json();

    console.log(data);

    if (!response.ok) {
      return {
        success: false,
        error: data.message,
      };
    }

    return data;
  } catch (error) {
    console.error(error);
    return { error: "Something went wrong." };
  }
};
