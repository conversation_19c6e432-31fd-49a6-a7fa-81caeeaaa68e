"use server";

export const newVerification = async (token: string) => {
  try {
    const url = `${process.env.API_URL}/auth/verification-token/${token}`;
    const response = await fetch(url, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ token }),
    });

    const data = await response.json();

    if (!response.ok) {
      return { error: data.message };
    }
    return { success: data.success, email: data.email };
  } catch (error) {
    console.error(error);
    return { error: "Something went wrong, could not verify email." };
  }
};
