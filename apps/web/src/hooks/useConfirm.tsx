import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { cn } from "@/lib/utils";
import { TriangleAlertIcon } from "lucide-react";
import { useCallback, useState } from "react";

interface ConfirmOptions {
  title?: string;
  description?: string;
  confirmText?: string;
  cancelText?: string;
  variant?: "destructive" | "default";
  icon?: React.ReactNode;
}

interface ConfirmState extends ConfirmOptions {
  isOpen: boolean;
  resolve?: (value: boolean) => void;
}

export const useConfirm = () => {
  const [state, setState] = useState<ConfirmState>({
    isOpen: false,
  });

  const confirm = useCallback(
    (options: ConfirmOptions = {}): Promise<boolean> => {
      return new Promise(resolve => {
        setState({
          isOpen: true,
          resolve,
          title: "Delete",
          description: "Are you sure you want to delete?",
          confirmText: "Confirm",
          cancelText: "Cancel",
          variant: "default",
          icon: <TriangleAlertIcon />,
          ...options,
        });
      });
    },
    []
  );

  const handleConfirm = () => {
    state.resolve?.(true);
    setState(prev => ({ ...prev, isOpen: false, resolve: undefined }));
  };

  const handleCancel = () => {
    state.resolve?.(false);
    setState(prev => ({ ...prev, isOpen: false, resolve: undefined }));
  };

  const ConfirmModal = () => {
    if (!state.isOpen) return null;

    return (
      <AlertDialog open={state.isOpen} onOpenChange={handleCancel}>
        <AlertDialogContent className="py-8 w-11/12 max-w-md sm:max-w-md">
          <AlertDialogHeader className="flex flex-col items-center">
            <div
              className={cn(
                "my-2 size-10 lg:size-12 [&>svg]:size-5 lg:[&>svg]:size-6 rounded-full grid place-content-center",
                state.variant === "destructive"
                  ? "bg-destructive/20 text-destructive"
                  : "bg-primary/20 text-primary"
              )}
            >
              {state.icon}
            </div>
            <AlertDialogTitle className="text-lg lg:text-xl text-center">
              {state.title}
            </AlertDialogTitle>
            <AlertDialogDescription className="text-sm text-center">
              {state.description}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="mt-2">
            <AlertDialogCancel
              className="w-full shrink-1 text-muted-foreground border-none hover:bg-muted shadow-none"
              onClick={handleCancel}
            >
              {state.cancelText}
            </AlertDialogCancel>
            <AlertDialogAction
              className={cn(
                "w-full shrink-1",
                state.variant === "destructive"
                  ? "text-white bg-destructive hover:bg-destructive/90"
                  : "bg-primary text-primary-foreground hover:bg-primary/90"
              )}
              onClick={handleConfirm}
            >
              {state.confirmText}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    );
  };

  return { confirm, ConfirmModal };
};
