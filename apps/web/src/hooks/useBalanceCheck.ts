import { fetchAccountData } from "@/store/slices/accountSlice";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { RootState } from "@/store/store";

export const useBalanceCheck = () => {
  const dispatch = useAppDispatch();

  const balance = useAppSelector(
    (state: RootState) =>
      state.account.data?.organization.wallet.availableBalance
  );

  const checkSufficientBalance = async (requiredAmount: number) => {
    const result = await dispatch(fetchAccountData());

    let currentBalance: number;

    if (fetchAccountData.fulfilled.match(result)) {
      currentBalance = parseFloat(
        result.payload?.organization.wallet.availableBalance ?? "0"
      );
    } else {
      currentBalance = parseFloat(balance ?? "0");
    }

    return {
      sufficient: currentBalance >= requiredAmount,
      currentBalance,
      shortfall: Math.max(0, requiredAmount - currentBalance),
    };
  };

  return {
    balance,
    checkSufficientBalance,
  };
};
