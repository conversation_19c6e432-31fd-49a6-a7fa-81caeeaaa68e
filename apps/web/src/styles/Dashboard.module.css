@reference "../app/globals.css";

.pagePadding {
  /* @apply px-4 py-5 sm:px-6 sm:py-8 lg:pt-10 xl:px-8 xl:pt-12; */
  @apply px-4 pt-8 pb-6 sm:px-6 xl:px-8;
}

.gridGap {
  @apply gap-5 xl:gap-6;
}

.panelContainer {
  @apply px-4 py-5 border xl:px-5 xl:py-6 rounded-lg;
}

.panelContainerNoBorder {
  @apply px-4 py-5 xl:px-5 xl:py-6 rounded-lg;
}

.panelYSpacing {
  @apply space-y-5 lg:space-y-6;
}

.formGrid {
  @apply grid sm:grid-cols-2 gap-4 items-start;
}

.colSpan2 {
  @apply sm:col-span-2;
}
