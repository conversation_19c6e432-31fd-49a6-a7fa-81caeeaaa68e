import { UserRole } from "@repo/database";
import { type DefaultSession } from "next-auth";

export type ExtendedUser = DefaultSession["user"] & {
  // role: UserRole;
  organizationId: string;
  // isTwoFactorEnabled: boolean;
  // isOAuth: boolean;
};

declare module "next-auth" {
  interface Session {
    user: ExtendedUser;
  }
}

// import { JWT } from "next-auth/jwt";

declare module "next-auth/jwt" {
  interface JWT {
    // role?: UserRole;
    organizationId?: string;
  }
}
