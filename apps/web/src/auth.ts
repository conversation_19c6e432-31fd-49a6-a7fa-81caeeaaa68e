import { getUserById } from "@/data/user";
import NextAuth from "next-auth";
import authConfig from "./auth.config";

export const { handlers, signIn, signOut, auth } = NextAuth({
  pages: {
    signIn: "/auth/login",
    error: "/auth/error",
  },
  callbacks: {
    async signIn({ user, account }) {
      // Future features:
      // Allow OAuth to sign in without verification
      if (account?.provider !== "credentials") return true;

      if (!user.id) return false;
      const existingUser = await getUserById(user.id);

      // Prevent signin without email verification
      if (!existingUser?.emailVerified) return false;

      return true;
    },

    async session({ token, session }) {
      if (token.sub && session.user) {
        session.user.id = token.sub;
      }

      // if (token.role && session.user) {
      //   session.user.role = token.role as UserRole;
      // }

      if (session.user) {
        session.user.name = token.name;
        session.user.email = token.email!;
        session.user.organizationId = token.organizationId as string;
      }

      return session;
    },
    async jwt({ token }) {
      if (!token.sub) return token;
      const existingUser = await getUserById(token.sub);

      if (!existingUser) return token;

      token.name = `${existingUser.firstName} ${existingUser.lastName}`;
      token.email = existingUser.email;
      // token.role = existingUser.role;
      token.organizationId = existingUser.organizationId;

      return token;
    },
  },

  // Don't need for now
  // adapter: PrismaAdapter(prisma as any),

  session: {
    strategy: "jwt",
    maxAge: 60 * 60 * 24 * 7, // 1 week
  },
  ...authConfig,
});
