"use server";

import { User } from "@repo/database";

export const getUserByEmail = async (email: string): Promise<User | null> => {
  try {
    const encodedEmail = encodeURIComponent(email);

    const url = `${process.env.API_URL}/users/email/${encodedEmail}`;

    const response = await fetch(url);
    if (!response.ok) throw new Error("Failed to fetch user by email");

    const user = await response.json();
    return user;
  } catch (error) {
    console.log(error);
    return null;
  }
};

export const getUserById = async (id: string): Promise<User | null> => {
  try {
    const url = `${process.env.API_URL}/users/${id}`;

    const response = await fetch(url);
    if (!response.ok) throw new Error("Failed to fetch user by ID");

    const user = await response.json();
    return user;
  } catch (error) {
    console.error(error);
    return null;
  }
};
