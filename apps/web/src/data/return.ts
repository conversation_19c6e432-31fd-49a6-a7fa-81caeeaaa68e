"use server";

import { getJwtToken } from "@/actions/auth/getToken";
import { FindOneReturn } from "@/types/Return/Return";

const getReturnById = async (id: string): Promise<FindOneReturn | null> => {
  try {
    const url = `${process.env.API_URL}/returns/${id}`;

    const jwtToken = await getJwtToken();
    if (!jwtToken) throw new Error("Invalid credentials");

    const response = await fetch(url, {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${jwtToken}`,
      },
    });

    if (!response.ok) throw new Error("Return not found");

    return await response.json();
  } catch (error) {
    if (process.env.NODE_ENV === "development") console.log(error);
    return null;
  }
};

export { getReturnById };
