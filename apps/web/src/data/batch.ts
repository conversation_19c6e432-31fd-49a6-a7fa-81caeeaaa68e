"use server";

import { getJwtToken } from "@/actions/auth/getToken";
import { BatchWithOrders } from "@/store/types/batch";

export const getBatchById = async (
  id: string
): Promise<BatchWithOrders | null> => {
  try {
    const url = `${process.env.API_URL}/batches/${id}?include=orders`;

    const jwtToken = await getJwtToken();
    if (!jwtToken) throw new Error("Invalid credentials");

    const response = await fetch(url, {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${jwtToken}`,
      },
    });
    if (!response.ok) throw new Error("Batch not found");

    const batch = await response.json();
    return batch;
  } catch (error) {
    console.log(error);
    return null;
  }
};
