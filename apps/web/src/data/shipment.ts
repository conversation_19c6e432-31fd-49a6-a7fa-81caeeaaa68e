"use server";

import { getJwtToken } from "@/actions/auth/getToken";
import { FindOneShipment } from "@/types/Shipment/Shipment";

export const getShipmentById = async (
  id: string
): Promise<FindOneShipment | null> => {
  try {
    const url = `${process.env.API_URL}/shipments/${id}`;

    const jwtToken = await getJwtToken();
    if (!jwtToken) throw new Error("Invalid credentials");

    const response = await fetch(url, {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${jwtToken}`,
      },
    });
    if (!response.ok) throw new Error("Shipment not found");

    return await response.json();
  } catch (error) {
    console.log(error);
    return null;
  }
};
