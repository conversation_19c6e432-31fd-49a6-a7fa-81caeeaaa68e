import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";
import { Switch } from "@/components/ui/switch";
import { cn } from "@/lib/utils";
import { useGetPermissionsQuery } from "@/store/api/userApi";
import styles from "@/styles/Dashboard.module.css";
import { Permission } from "@repo/database";
import { AlertCircle } from "lucide-react";
import { useState } from "react";
import { useFormContext } from "react-hook-form";

const PermissionForm = () => {
  const { data, isLoading } = useGetPermissionsQuery();
  const [isAdmin, setIsAdmin] = useState<boolean>(true);

  const form = useFormContext();

  const permissions = data?.filter(
    (item: Permission) => item.id !== "user_management"
  );

  if (isLoading) {
    return <div>loading..</div>;
  }

  return (
    <div className="space-y-4">
      <h2 className="font-semibold">Permissions Management</h2>
      <FormField
        control={form.control}
        name={"permissionIds"}
        render={({ field }) => {
          const allPermissionIds = data?.map(p => p.id);
          const isAllSelected = allPermissionIds?.every(id =>
            field?.value?.includes(id)
          );

          return (
            <FormItem className="flex gap-x-3">
              <FormControl>
                <Switch
                  checked={isAllSelected}
                  onCheckedChange={checked => {
                    setIsAdmin(!checked);
                    if (checked) {
                      field.onChange(allPermissionIds);
                    } else {
                      field.onChange([]);
                    }
                  }}
                />
              </FormControl>
              <div>
                <FormLabel className="text-foreground text-sm">Admin</FormLabel>
                <FormDescription className="text-xs text-foreground">
                  Full access to all features, can configure account settings
                  and manage users.
                </FormDescription>
              </div>
            </FormItem>
          );
        }}
      />
      {typeof form.formState.errors.permissionIds?.message === "string" &&
        form.formState.errors.permissionIds?.message !== "" && (
          <Alert variant="destructive" className="md:col-span-2">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
              {form.formState.errors.permissionIds?.message}
            </AlertDescription>
          </Alert>
        )}
      <div className="text-foreground text-sm font-semibold">
        Select custom roles (multiple allowed)
      </div>
      <div className={cn(styles.formGrid)}>
        {permissions?.map(item => {
          return (
            <FormField
              key={item.id}
              control={form.control}
              name={`permissionIds`}
              render={({ field }) => {
                const isChecked = field.value?.includes(item.id);
                return (
                  <FormItem className="flex gap-x-3">
                    <FormControl>
                      <Switch
                        disabled={!isAdmin}
                        checked={isChecked}
                        onCheckedChange={checked => {
                          if (checked) {
                            field.onChange([...field.value, item.id]);
                          } else {
                            field.onChange(
                              field.value.filter(
                                (val: string) =>
                                  val !== item.id && val !== "user_management"
                              )
                            );
                          }
                        }}
                      />
                    </FormControl>
                    <div>
                      <FormLabel className="text-foreground text-sm">
                        {item.name}
                      </FormLabel>
                      <FormDescription className="text-xs text-foreground">
                        {item.description}
                      </FormDescription>
                    </div>
                  </FormItem>
                );
              }}
            />
          );
        })}
      </div>
    </div>
  );
};

export default PermissionForm;
