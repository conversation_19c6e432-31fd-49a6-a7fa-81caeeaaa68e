"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Header from "@/components/layout/Header";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import EditUserSchema from "@/schemas/user/EditUserSchema";
import { zodResolver } from "@hookform/resolvers/zod";
import { UserPermission } from "@repo/database";
import { UserRound } from "lucide-react";
import { useState } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { z } from "zod";
import PermissionForm from "./PermissionForm";

type EditUserModalProps = {
  permissions: UserPermission[];
  trigger: React.ReactNode;
};

// TODO: Can update firstName, lastName, permissions, image
const EditUserModal = ({ permissions, trigger }: EditUserModalProps) => {
  const [open, setOpen] = useState<boolean>(false);
  const form = useForm<z.infer<typeof EditUserSchema>>({
    resolver: zodResolver(EditUserSchema),
    defaultValues: {
      permissionIds: permissions.map(permission => permission.permissionId),
    },
  });
  const { handleSubmit, reset } = form;

  const onSubmit = (values: z.infer<typeof EditUserSchema>) => {
    console.log(values);

    reset();
    setOpen(false);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      {trigger}
      <DialogContent className="md:min-w-2xl lg:min-w-3xl xl:gap-y-5">
        <DialogTitle className="hidden" />
        <DialogDescription className="hidden" />
        <DialogHeader>
          <Header icon={UserRound} as="h3">
            Edit User
          </Header>
        </DialogHeader>
        <Separator />
        <h2 className="font-semibold">User Information</h2>
        UserProfileImage here
        <FormProvider {...form}>
          <form
            onSubmit={handleSubmit(onSubmit)}
            className="flex flex-col gap-y-4 xl:gap-y-5"
          >
            <Separator />
            <PermissionForm />
            <Separator />
            <DialogFooter>
              <DialogClose asChild>
                <Button type="button" variant="ghost-destructive">
                  Cancel
                </Button>
              </DialogClose>
              <Button type="submit" onClick={handleSubmit(onSubmit)}>
                Invite Users
              </Button>
            </DialogFooter>
          </form>
        </FormProvider>
      </DialogContent>
    </Dialog>
  );
};

export default EditUserModal;
