"use client";

import { inviteUsers } from "@/actions/user/inviteUsers";
import Header from "@/components/layout/Header";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import { parseAndValidateEmails } from "@/lib/utils/user";
import InviteSchema, { defaultValues } from "@/schemas/user/InviteSchema";
import { useCreateUserInvitesMutation } from "@/store/api/userApi";
import { zodResolver } from "@hookform/resolvers/zod";
import { Loader2, Plus, UserRoundPlus, X } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";
import PermissionForm from "./EditUserModal/PermissionForm";

type InviteModalProp = {
  disabledTrigger: boolean;
  remainingUserSpots: number;
};

const InviteModal = ({
  disabledTrigger = false,
  remainingUserSpots,
}: InviteModalProp) => {
  const [open, setOpen] = useState(false);
  const [emails, setEmails] = useState<string[]>([]);
  const [currentInput, setCurrentInput] = useState("");
  const [error, setError] = useState("");

  const availableUserSpots = remainingUserSpots - emails.length;

  const [createUserInvites, { isLoading }] = useCreateUserInvitesMutation();

  const handleInputChange = (value: string) => {
    setCurrentInput(value);
    setError("");
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && currentInput.trim()) {
      e.preventDefault();
      const { emailList, invalidEmails } = parseAndValidateEmails(currentInput);
      if (invalidEmails.length > 0) {
        setError(`Invalid email(s): ${invalidEmails.join(", ")}`);
        return;
      }

      const allEmails = [...new Set([...emails, ...emailList])];
      setEmails(allEmails);

      form.setValue("emails", allEmails, { shouldValidate: true });
      setCurrentInput("");
      setError("");
    } else if (e.key === "Backspace" && !currentInput && emails.length > 0) {
      const newEmails = emails.slice(0, -1);
      setEmails(newEmails);
      form.setValue("emails", newEmails, { shouldValidate: true });
      setError("");
    }
  };

  const removeEmail = (emailToRemove: string) => {
    const newEmails = emails.filter(email => email !== emailToRemove);
    setEmails(newEmails);
    form.setValue("emails", newEmails, { shouldValidate: true });
  };

  const form = useForm<z.infer<typeof InviteSchema>>({
    resolver: zodResolver(InviteSchema),
    defaultValues,
  });
  const { control, handleSubmit, reset } = form;

  const disabled =
    form.watch("permissionIds").length > 0 && form.watch("emails").length > 0;

  const onSubmit = async (values: z.infer<typeof InviteSchema>) => {
    const { emailList, invalidEmails } = parseAndValidateEmails(currentInput);

    if (invalidEmails.length > 0) {
      setError(`Invalid email(s): ${invalidEmails.join(", ")}`);
      return;
    }

    const allEmails = [...new Set([...emails, ...emailList])];

    // Update form and state with all emails
    setEmails(allEmails);
    form.setValue("emails", allEmails, { shouldValidate: true });
    setCurrentInput("");

    try {
      const response = await createUserInvites({
        ...values,
        emails: allEmails,
      }).unwrap();

      const { results, errors } = response;

      const data = await inviteUsers(results, errors);

      data.forEach(({ message, type }) => {
        if (type === "success") {
          toast.success(message);
        } else {
          toast.error(message);
        }
      });

      setError("");
      reset();
      setEmails([]);
      setCurrentInput("");
      setOpen(false);
    } catch (error) {
      console.log(error);
      toast.error("Failed to invited user");
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button size="lg" disabled={disabledTrigger}>
          <Plus className="size-5" />
          Invite User
        </Button>
      </DialogTrigger>
      <DialogContent className="md:min-w-2xl lg:min-w-3xl xl:gap-y-5">
        <DialogTitle className="hidden" />
        <DialogDescription className="hidden" />
        <DialogHeader>
          <Header icon={UserRoundPlus} as="h3">
            Invite
          </Header>
        </DialogHeader>
        <Separator />
        <Form {...form}>
          <form
            onSubmit={handleSubmit(onSubmit)}
            className="flex flex-col gap-y-4 xl:gap-y-5"
          >
            <FormField
              control={control}
              name="emails"
              render={({ field }) => (
                <FormItem className="relative">
                  <FormLabel className="text-foreground font-semibold text-base">
                    Email{" "}
                    <span className="text-xs font-normal text-muted-foreground">
                      (available spots left: {availableUserSpots})
                    </span>
                  </FormLabel>
                  <FormControl>
                    <div
                      className={cn(
                        availableUserSpots > 0
                          ? "bg-background"
                          : "bg-muted opacity-70",
                        "flex flex-wrap gap-2 p-2 border rounded-md min-h-[100px] "
                      )}
                    >
                      {emails.map((email, index) => (
                        <Badge
                          key={index}
                          variant="secondary"
                          className="flex items-center gap-1 px-2 py-1"
                        >
                          {email}
                          <button
                            type="button"
                            onClick={() => removeEmail(email)}
                            className="ml-1 hover:text-foreground cursor-pointer"
                          >
                            <X className="h-3 w-3" />
                          </button>
                        </Badge>
                      ))}
                      <Textarea
                        {...field}
                        value={currentInput}
                        onChange={e => {
                          handleInputChange(e.target.value);
                          field.onChange(e);
                        }}
                        disabled={availableUserSpots <= 0}
                        onKeyDown={handleKeyDown}
                        className="flex-1 min-h-[24px] border-0 p-0 focus-visible:ring-0 focus-visible:ring-offset-0 resize-none shadow-none dark:bg-background"
                      />
                    </div>
                  </FormControl>
                  <FormMessage>{error}</FormMessage>
                </FormItem>
              )}
            />
            <Separator />
            <PermissionForm />
            <Separator />
            <DialogFooter>
              <DialogClose asChild>
                <Button type="button" variant="ghost-destructive">
                  Cancel
                </Button>
              </DialogClose>
              <Button
                disabled={!disabled || isLoading}
                onClick={handleSubmit(onSubmit)}
              >
                {isLoading && <Loader2 className="animate-spin" />} Invite Users
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default InviteModal;
