"use client";

import SkeletonTableCell from "@/components/loading/SkeletonTableCell";
import DataTable from "@/components/common/table/DataTable";
import {
  useDeleteUserInviteMutation,
  useGetUsersInviteQuery,
  useGetUsersQuery,
  useUpdateUserStatusMutation,
} from "@/store/api/userApi";
import { UserWithPermissions } from "@/types/User/UserWithPermissions";
import { UserStatus } from "@repo/database";
import {
  ColumnDef,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
} from "@tanstack/react-table";
import { useCallback, useEffect, useMemo, useState } from "react";
import { toast } from "sonner";
import { createUserActionsColumns, createUserColumns } from "./useColumns";
import { UserInvitesWithPermissions } from "@/types/User/UserInviteWithPermissions";

type UserWithType =
  | (UserWithPermissions & { type: "user" })
  | (UserInvitesWithPermissions & { type: "invite" });

const TableWrapper = () => {
  const { data: users, isLoading: usersLoading } = useGetUsersQuery();
  const { data: userInvites, isLoading: userInvitesLoading } =
    useGetUsersInviteQuery();
  const [deleteUserInvite] = useDeleteUserInviteMutation();
  const [updateUserStatus] = useUpdateUserStatusMutation();

  const allUsers: UserWithType[] = useMemo(() => {
    if (users && userInvites) {
      const usersWithType = users.map(user => ({
        ...user,
        type: "user" as const,
      }));
      const userInvitesWithType = userInvites.map(user => ({
        ...user,
        type: "invite" as const,
      }));

      return [...usersWithType, ...userInvitesWithType];
    } else return [];
  }, [users, userInvites]);

  const handleCancel = useCallback(
    async (id: string) => {
      try {
        await deleteUserInvite(id).unwrap();
        toast.success(`Cancelled invite`);
      } catch (error) {
        console.log(error);
        toast.error("Failed to cancel invite");
      }
    },
    [deleteUserInvite]
  );

  const handleUpdateStatus = useCallback(
    async (id: string, status: UserStatus) => {
      const payload = {
        userId: id,
        status,
      };
      try {
        await updateUserStatus(payload).unwrap();
        toast.success(`User status updated to ${status}`);
      } catch (error) {
        console.error(error);
        toast.error("Failed to update user status");
      }
    },
    [updateUserStatus]
  );

  const columns = [
    ...createUserColumns(),
    ...createUserActionsColumns({ handleCancel, handleUpdateStatus }),
  ];

  const [mounted, setMounted] = useState(false);
  useEffect(() => {
    setMounted(true);
  }, []);
  if (mounted)
    return (
      <div className="mt-8 lg:mt-10 w-full max-w-full flex flex-col grow-1">
        {usersLoading || userInvitesLoading ? (
          <SkeletonTableCell className="min-h-[calc(100vh-23rem)]" />
        ) : (
          <Table data={allUsers} columns={columns} />
        )}
      </div>
    );
};

type TableProps = {
  data: UserWithType[];
  columns: ColumnDef<UserWithType>[];
};

const Table = ({ data, columns }: TableProps) => {
  const [sorting, setSorting] = useState<SortingState>([]);

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    state: {
      sorting,
    },
  });

  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  return (
    <>
      <div className="grow-1 flex flex-col">
        <DataTable
          table={table}
          showPagination={false}
          rowColor={row =>
            row.original.type === "invite" ? "bg-muted/60" : "bg-transparent"
          }
        />
      </div>
    </>
  );
};

export default TableWrapper;
