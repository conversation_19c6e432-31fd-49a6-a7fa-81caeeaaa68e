import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { DialogTrigger } from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { cn } from "@/lib/utils";
import { getColorStatus, getInitials, getPermissions } from "@/lib/utils/user";
import { UserWithPermissions } from "@/types/User/UserWithPermissions";
import { UserInviteStatus, UserStatus } from "@repo/database";
import { ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";
import {
  EllipsisVertical,
  ListFilter,
  Mail,
  Pencil,
  ThumbsDown,
  ThumbsUp,
} from "lucide-react";
import EditUserModal from "../modals/EditUserModal";
import { UserInvitesWithPermissions } from "@/types/User/UserInviteWithPermissions";

type UserWithType =
  | (UserWithPermissions & { type: "user" })
  | (UserInvitesWithPermissions & { type: "invite" });

export const createUserColumns = (): ColumnDef<UserWithType>[] => [
  {
    accessorKey: "image",
    header: "",
    cell: ({ row }) => {
      const user = row.original;
      if (user.type === "invite")
        return (
          <Avatar>
            <AvatarFallback>
              {getInitials(`${user.email.toUpperCase()}`)}
            </AvatarFallback>
          </Avatar>
        );
      return (
        <Avatar>
          <AvatarImage src={user.image || undefined} />
          <AvatarFallback>
            {getInitials(`${user.firstName} ${user.lastName}`)}
          </AvatarFallback>
        </Avatar>
      );
    },
  },
  {
    accessorKey: "fullName",
    header: "Full Name",
    cell: ({ row }) => {
      const user = row.original;
      return user.type === "user" ? (
        <EditUserModal
          permissions={user.permissions}
          trigger={
            <DialogTrigger className="hover:underline cursor-pointer">
              {user.firstName} {user.lastName}
            </DialogTrigger>
          }
        />
      ) : (
        <div>{user.email}</div>
      );
    },
  },
  {
    accessorKey: "email",
    header: "Email",
  },
  {
    id: "permissions",
    header: "Permissions",
    cell: ({ row }) => {
      const user = row.original;
      if (user.type === "user") {
        return (
          <div className="capitalize">
            {getPermissions(user.permissions.map(p => p.permissionId))}
          </div>
        );
      } else if (user.type === "invite") {
        return (
          <div className="capitalize">
            {getPermissions(user.permissions.map(p => p.permissionId))}
          </div>
        );
      }
    },
  },
  {
    accessorKey: "createdAt",
    header: "Member Since",
    cell: ({ row }) => (
      <div>
        {row.original.type === "user" &&
          format(row.getValue("createdAt"), "MM/dd/yyyy")}
      </div>
    ),
  },
  {
    accessorKey: "status",
    header: () => {
      return (
        <div className="flex items-center gap-2">
          <p className="capitalize">Status</p>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant={"ghost"}
                className="flex items-center justify-center gap-2 size-6 rounded-full bg-muted"
              >
                <ListFilter className="size-3.5" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56">
              <DropdownMenuItem variant="success">
                <ThumbsUp /> Active
              </DropdownMenuItem>
              <DropdownMenuItem variant="warning">
                <Mail /> Invitation Sent
              </DropdownMenuItem>
              <DropdownMenuItem variant="destructive">
                <ThumbsDown /> Deactivated
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      );
    },
    cell: ({ row }) => {
      const textColor = getColorStatus(
        row.original.status as UserInviteStatus | UserStatus
      );
      return (
        <p className={cn(textColor, "capitalize")}>
          {row.original.status.toLocaleLowerCase()}
        </p>
      );
    },
  },
];

type createUserActionsProps = {
  handleCancel: (id: string) => void;
  handleUpdateStatus: (id: string, status: UserStatus) => void;
};

export const createUserActionsColumns = ({
  handleCancel,
  handleUpdateStatus,
}: createUserActionsProps): ColumnDef<UserWithType>[] => [
  {
    id: "actions",
    cell: ({ row }) => {
      const user = row.original;
      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon-xs">
              <span className="sr-only">Open menu</span>
              <EllipsisVertical />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {user.type === "user" && (
              <>
                <DropdownMenuItem
                  onClick={e => {
                    e.preventDefault();
                    e.stopPropagation();
                  }}
                >
                  <EditUserModal
                    permissions={user.permissions}
                    trigger={
                      <DialogTrigger className="flex items-center gap-2 cursor-pointer w-full">
                        <Pencil /> Edit
                      </DialogTrigger>
                    }
                  />
                </DropdownMenuItem>
                <DropdownMenuSeparator />
              </>
            )}

            {user.type === "user" ? (
              <>
                {user.status === UserStatus.active ? (
                  <DropdownMenuItem
                    variant="destructive"
                    onClick={() =>
                      handleUpdateStatus(user.id, UserStatus.deactivated)
                    }
                  >
                    <ThumbsDown /> Deactivated
                  </DropdownMenuItem>
                ) : (
                  <DropdownMenuItem
                    variant="success"
                    onClick={() =>
                      handleUpdateStatus(user.id, UserStatus.active)
                    }
                  >
                    <ThumbsUp /> Active
                  </DropdownMenuItem>
                )}
              </>
            ) : (
              <>
                <DropdownMenuItem
                  variant="destructive"
                  onClick={() => handleCancel(user.id)}
                >
                  Cancel
                </DropdownMenuItem>
              </>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];
