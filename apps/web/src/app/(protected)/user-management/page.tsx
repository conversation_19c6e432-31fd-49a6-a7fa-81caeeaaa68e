"use client";
import { MAX_USERS_PER_ORGANIZATION } from "@/constants/account";
import { cn } from "@/lib/utils";
import { useGetUsersInviteQuery, useGetUsersQuery } from "@/store/api/userApi";
import styles from "@/styles/Dashboard.module.css";
import { UsersRound } from "lucide-react";
import PageHeader from "../../../components/layout/PageHeader";
import InviteModal from "./_components/modals/InviteModal";
import Table from "./_components/table/Table";

export default function UserManagementPage() {
  const { data: users } = useGetUsersQuery();
  const { data: usersInvites } = useGetUsersInviteQuery();

  const userAmount =
    users && usersInvites && users.length + usersInvites.length;

  return (
    <div
      className={cn(
        styles.pagePadding,
        "relative",
        "min-h-[calc(100vh-80px)] flex flex-col"
      )}
    >
      <PageHeader
        header="User & Permissions Management"
        icon={UsersRound}
        description={
          <p>
            You have used{" "}
            {userAmount ?? (
              <span className="inline-block size-3.5 animate-pulse bg-muted rounded-sm" />
            )}{" "}
            out of your {MAX_USERS_PER_ORGANIZATION} allocated user slots for
            this subscription. To free up user slots, you can deactivate users
            by changing their status from{" "}
            <span className="text-foreground font-medium">
              &apos;active&apos;
            </span>{" "}
            to
            <span className="text-foreground font-medium">
              {" "}
              &apos;deactivated&apos;
            </span>{" "}
            in the user management settings
          </p>
        }
      />
      <div className="mt-4 flex gap-3 items-center flex-wrap">
        <InviteModal
          disabledTrigger={Number(userAmount) >= MAX_USERS_PER_ORGANIZATION}
          remainingUserSpots={MAX_USERS_PER_ORGANIZATION - (users?.length ?? 0)}
        />
        {userAmount && (
          <p className="text-muted-foreground/80 text-sm px-2">
            ({userAmount}/{MAX_USERS_PER_ORGANIZATION} user slots)
          </p>
        )}
      </div>
      <Table />
    </div>
  );
}
