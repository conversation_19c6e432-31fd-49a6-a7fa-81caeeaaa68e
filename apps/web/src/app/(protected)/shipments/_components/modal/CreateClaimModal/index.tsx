"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import Header from "@/components/layout/Header";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import { capitalize } from "@/lib/utils/strings";
import CreateClaimSchema, {
  defaultValues,
} from "@/schemas/claim/CreateClaimSchema";
import { useCreateClaimMutation } from "@/store/api/claimApi";
import customStyles from "@/styles/Custom.module.css";
import styles from "@/styles/Dashboard.module.css";
import { zodResolver } from "@hookform/resolvers/zod";
import { ClaimType } from "@repo/database";
import { AlertTriangle, Image as ImageIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { Controller, FormProvider, useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";
import FileUpload from "../../../../../../components/common/FileUpload";

type CreateClaimModalProps = {
  shipmentId: string;
  trackingCode: string;
  insurance: string | number | null;
  trigger: React.ReactNode;
  type: "shipment" | "return" | undefined;
};

const CreateClaimModal = ({
  shipmentId,
  trackingCode,
  insurance,
  trigger,
  type,
}: CreateClaimModalProps) => {
  const [open, setOpen] = useState(false);
  const [invoiceError, setInvoiceError] = useState<string | null>(null);
  const [emailAttachmentsError, setEmailAttachmentsError] = useState<
    string | null
  >(null);
  const [supportingError, setSupportingError] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const [createClaim] = useCreateClaimMutation();

  const claimReasonOptions = Object.keys(ClaimType).map(key => ({
    value: key,
    label: capitalize(key),
  }));

  const form = useForm<z.infer<typeof CreateClaimSchema>>({
    resolver: zodResolver(CreateClaimSchema),
    defaultValues: defaultValues,
  });

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "." || e.key === ",") {
      e.preventDefault();
    }
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    onChange: (value: string) => void
  ) => {
    const value = e.target.value;
    if (value.includes(".")) {
      e.target.value = value.split(".")[0];
    }
    const numericValue = Number(value);
    const isValid =
      value === "" ||
      (Number.isInteger(numericValue) &&
        numericValue >= 1 &&
        numericValue <= Number(insurance));

    if (!isValid) return;

    // Remove leading zeros but keep single "0"
    const trimmedValue = value.replace(/^0+/, "") || "0";
    onChange(trimmedValue);
  };

  const onSubmit = async (values: z.infer<typeof CreateClaimSchema>) => {
    const formData = new FormData();

    values.invoiceAttachments?.forEach(file => {
      formData.append("invoiceAttachments", file);
    });

    values.supportingDocumentsAttachments?.forEach(file => {
      formData.append("supportingDocumentsAttachments", file);
    });

    values.emailEvidenceAttachments?.forEach(file => {
      formData.append("supportingDocumentsAttachments", file);
    });

    if (type === "return") formData.append("returnId", String(shipmentId));
    if (type === "shipment") formData.append("shipmentId", String(shipmentId));

    formData.append("trackingCode", trackingCode);
    formData.append("amount", values.amount.toString());
    formData.append("reference", values.reference);
    formData.append("type", values.type);
    formData.append("description", values.description);

    try {
      const { id } = await createClaim(formData).unwrap();
      toast.success(`Claim successfully`);
      setInvoiceError(null);
      setSupportingError(null);
      setError(null);
      setOpen(false);
      router.push(`/claims/${id}`);
    } catch (error) {
      const err = error as {
        status: number;
        data: {
          message: string;
          error: string;
          statusCode: number;
        };
      };

      if (err.data?.message) {
        setError(err.data.message);
        toast.error(err.data.message);
      } else {
        setError("Unexpected error. Please contact support.");
        toast.error("Unexpected error. Please contact support.");
      }
    }
  };

  const handleCancel = () => {
    setOpen(false);
    setInvoiceError(null);
    setSupportingError(null);
    setError(null);
    form.reset();
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      {trigger}
      <DialogContent className="w-full sm:min-w-xl md:min-w-2xl lg:min-w-3xl xl:gap-y-5">
        <DialogTitle className="sr-only">Import Claim</DialogTitle>
        <DialogDescription className="sr-only">
          Import a claim of orders.
        </DialogDescription>
        <DialogHeader>
          <Header icon={ImageIcon} as="h3">
            File a Claim
          </Header>
        </DialogHeader>
        <Separator />
        <FormProvider {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="space-y-4 xl:space-y-5"
          >
            <div className={cn(styles.formGrid)}>
              <div className="grid gap-2 col-span-2 md:col-span-1">
                <FormLabel>Tracking No.</FormLabel>
                <div className="py-2 px-4">{trackingCode}</div>
              </div>
              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem className="col-span-2 md:col-span-1">
                    <FormLabel>Claim Reason</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger className="w-full">
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {claimReasonOptions?.map((reason, index) => (
                          <SelectItem
                            key={`reason-${index}`}
                            value={reason.value}
                          >
                            {reason.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* <div className="col-span-2">
                <ClaimItemTable />
              </div> */}

              <FormField
                control={form.control}
                name="amount"
                render={({ field }) => (
                  <FormItem className="w-full col-span-2 md:col-span-1">
                    <FormLabel>Amount</FormLabel>
                    <div className="relative">
                      <div className="text-muted-foreground absolute top-1.5 left-3 z-20">
                        $
                      </div>
                      <FormControl>
                        <Input
                          type="number"
                          step="1"
                          className={cn(
                            customStyles.inputNoArrows,
                            "text-right bg-white"
                          )}
                          onKeyDown={handleKeyDown}
                          onChange={e => handleChange(e, field.onChange)}
                          value={Number(field.value)}
                        />
                      </FormControl>
                    </div>
                    <FormMessage className="text-right" />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="reference"
                render={({ field }) => (
                  <FormItem className="w-full col-span-2 md:col-span-1">
                    <FormLabel>Reference (optional)</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage className="text-right" />
                  </FormItem>
                )}
              />

              <div className="col-span-2 grid items-stretch space-y-2">
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea className="resize-none" {...field} />
                      </FormControl>
                      <FormMessage className="text-right" />
                    </FormItem>
                  )}
                />
                <div className="text-xs text-muted-foreground">
                  Minimum 25 characters
                </div>
              </div>

              <div className="col-span-2 grid items-stretch gap-4">
                <div>
                  <h2 className="font-semibold">Customer Communication</h2>
                  <div className="text-xs md:text-sm text-muted-foreground">
                    Email, pdf, or screenshot of customer complaint. Should
                    include customer&apos;s name and email that matches the name
                    and address on the tracking information.
                  </div>
                </div>
                <div className="space-y-2">
                  <Controller
                    name="emailEvidenceAttachments"
                    control={form.control}
                    render={({ field }) => (
                      <FileUpload
                        files={field.value}
                        onChange={field.onChange}
                        accept={[".pdf", ".png", ".jpg", ".jpeg"]}
                        className="relative min-h-40"
                        onError={setEmailAttachmentsError}
                        maxFiles={10}
                      />
                    )}
                  />
                  <div className="text-xs text-muted-foreground">
                    Maximum 10 files
                  </div>
                </div>
                {(emailAttachmentsError ||
                  form.formState.errors.emailEvidenceAttachments?.message) && (
                  <div className="px-4 py-2.5 bg-destructive/10 rounded-md flex items-center gap-2">
                    <AlertTriangle className="text-destructive size-4 shrink-0" />
                    <p className="text-destructive text-sm">
                      {emailAttachmentsError ||
                        form.formState.errors.emailEvidenceAttachments?.message}
                    </p>
                  </div>
                )}
              </div>

              <div className="col-span-2 grid items-stretch gap-4">
                <div>
                  <h2 className="font-semibold">Invoice Attachments</h2>
                  <div className="text-xs md:text-sm text-muted-foreground">
                    Upload receipts, invoices, or proof of purchase showing the
                    value of the items in your shipment. This helps us verify
                    the claimed amount.
                  </div>
                </div>
                <div className="space-y-2">
                  <Controller
                    name="invoiceAttachments"
                    control={form.control}
                    render={({ field }) => (
                      <FileUpload
                        files={field.value}
                        onChange={field.onChange}
                        accept={[".pdf", ".png", ".jpg", ".jpeg"]}
                        className="relative min-h-40"
                        onError={setInvoiceError}
                        maxFiles={10}
                      />
                    )}
                  />
                  <div className="text-xs text-muted-foreground">
                    Maximum 10 files
                  </div>
                </div>
                {(invoiceError ||
                  form.formState.errors.invoiceAttachments?.message) && (
                  <div className="px-4 py-2.5 bg-destructive/10 rounded-md flex items-center gap-2">
                    <AlertTriangle className="text-destructive size-4 shrink-0" />
                    <p className="text-destructive text-sm">
                      {invoiceError ||
                        form.formState.errors.invoiceAttachments?.message}
                    </p>
                  </div>
                )}
              </div>

              <div className="col-span-2 grid items-stretch gap-4">
                <div>
                  <h2 className="font-semibold">Supporting Documents</h2>
                  <div className="text-xs md:text-sm text-muted-foreground">
                    Upload any additional documentation that supports your
                    claim. Required if reason is “theft” or “damage”
                  </div>
                </div>
                <div className="space-y-2">
                  <Controller
                    name="supportingDocumentsAttachments"
                    control={form.control}
                    render={({ field }) => (
                      <FileUpload
                        files={field.value}
                        onChange={field.onChange}
                        accept={[".pdf", ".png", ".jpg", ".jpeg"]}
                        className="relative min-h-40"
                        onError={setSupportingError}
                        maxFiles={10}
                      />
                    )}
                  />
                  <div className="text-xs text-muted-foreground">
                    Maximum 10 files
                  </div>
                </div>
                {(supportingError ||
                  form.formState.errors.supportingDocumentsAttachments
                    ?.message) && (
                  <div className="px-4 py-2.5 bg-destructive/10 rounded-md flex items-center gap-2">
                    <AlertTriangle className="text-destructive size-4 shrink-0" />
                    <p className="text-destructive text-sm">
                      {supportingError ||
                        form.formState.errors.supportingDocumentsAttachments
                          ?.message}
                    </p>
                  </div>
                )}
              </div>
            </div>
            <Separator />
            {error && (
              <div className="px-4 py-2.5 bg-destructive/10 rounded-md flex gap-2">
                <AlertTriangle className="text-destructive size-4 shrink-0" />
                <p className="text-destructive text-sm">{error}</p>
              </div>
            )}
            <DialogFooter>
              <Button
                type="button"
                variant="ghost-destructive"
                onClick={handleCancel}
              >
                Cancel
              </Button>

              <Button type="submit">Submit</Button>
            </DialogFooter>
          </form>
          {/* <AddItemModal /> */}
        </FormProvider>
      </DialogContent>
    </Dialog>
  );
};

export default CreateClaimModal;
