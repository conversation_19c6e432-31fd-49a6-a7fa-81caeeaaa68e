import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import Header from "@/components/layout/Header";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { capitalize } from "@/lib/utils/strings";
import PendingReturnItemsSchema from "@/schemas/return/PendingReturnItemSchema";
import ReturnItemSchema from "@/schemas/return/ReturnItemSchema";
import { zodResolver } from "@hookform/resolvers/zod";
import { OrderItem, Product, ReturnReason } from "@repo/database";
import { AlertCircle, BoxIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { Control, useFieldArray, useForm } from "react-hook-form";
import { z } from "zod";

type CreateReturnModalProp = {
  shipmentId: string;
  orderItems: (OrderItem & { product: Product })[];
  trigger: React.ReactNode;
};

const CreateReturnModal = ({
  shipmentId,
  orderItems,
  trigger,
}: CreateReturnModalProp) => {
  const [open, setOpen] = useState(false);
  const { push } = useRouter();

  const form = useForm({
    resolver: zodResolver(PendingReturnItemsSchema),
    defaultValues: {
      returnItems: orderItems.map(({ id, quantity }) => ({
        selected: false,
        orderId: id,
        quantity,
        reason: ReturnReason.damaged,
        maxQuantity: quantity,
      })),
    },
  });

  const { fields } = useFieldArray({
    control: form.control,
    name: "returnItems",
  });

  const { returnItems } = form.formState.errors;

  const onSubmit = (data: z.infer<typeof PendingReturnItemsSchema>) => {
    const payload = {
      shipmentId,
      orderItems: data.returnItems
        .filter(item => item.selected)
        .map(item => {
          const orderItem = orderItems.find(
            orderItem => orderItem.id === item.orderId
          );

          return {
            product: {
              id: orderItem?.product.id,
              sku: orderItem?.product.sku,
              name: orderItem?.product.name,
            },
            quantity: item.quantity,
            price: orderItem?.price,
            reason: item.reason,
          };
        }),
    };

    sessionStorage.setItem("pendingReturnItems", JSON.stringify(payload));
    push(`/shipments/${shipmentId}/create-return`);
  };

  const handleCancel = () => {
    setOpen(false);
    form.reset();
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      {trigger}
      <DialogContent className="w-[95%] sm:max-w-none md:max-w-3xl lg:max-w-4xl xl:gap-y-5">
        <DialogTitle className="hidden" />
        <DialogDescription className="hidden" />
        <DialogHeader>
          <Header icon={BoxIcon} as="h3">
            Create Return
          </Header>
        </DialogHeader>
        <Separator />
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            <div className="border rounded-md overflow-hidden">
              <Table variant="dark" className="bg-muted rounded-md">
                <TableHeader>
                  <TableRow className="text-xs lg:text-sm">
                    <TableHead className="pl-8">SKU</TableHead>
                    <TableHead className="whitespace-nowrap lg:min-w-32">
                      Name
                    </TableHead>
                    <TableHead className="text-center max-w-20 lg:max-w-28 whitespace-pre-line md:whitespace-nowrap">
                      Shipped Quantity
                    </TableHead>
                    <TableHead className="text-center max-w-20 lg:max-w-28 whitespace-pre-line md:whitespace-nowrap">
                      Return Quantity
                    </TableHead>
                    <TableHead>Reason</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {fields.length > 0 &&
                    fields.map((item, index) => {
                      const orderItem = orderItems.find(
                        orderItem => orderItem.id === item.orderId
                      );
                      if (orderItem)
                        return (
                          <ReturnItemRow
                            key={item.id}
                            item={item}
                            index={index}
                            orderItem={orderItem}
                            control={form.control}
                          />
                        );
                    })}
                </TableBody>
              </Table>
            </div>

            <div>
              {returnItems && (
                <Alert variant="destructive" className="md:col-span-2">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Error</AlertTitle>
                  <AlertDescription>
                    {returnItems.root?.message}
                  </AlertDescription>
                </Alert>
              )}
            </div>
            <div className="flex justify-end items-center space-x-2">
              <Button
                type="button"
                variant="ghost-destructive"
                onClick={() => handleCancel()}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={!form.formState.isValid}>
                Continue
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

type ReturnItemRowProps = {
  item: z.infer<typeof ReturnItemSchema> & { id: string };
  index: number;
  orderItem: OrderItem & { product: Product };
  control: Control<z.infer<typeof PendingReturnItemsSchema>>;
};

const ReturnItemRow = ({
  item,
  index,
  orderItem,
  control,
}: ReturnItemRowProps) => {
  const returnReasonOptions = Object.keys(ReturnReason).map(key => ({
    value: key,
    label: capitalize(key),
  }));

  return (
    <TableRow key={item.id}>
      <TableCell>
        <div className="flex space-x-2 items-center">
          <FormField
            control={control}
            name={`returnItems.${index}.selected`}
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Checkbox
                    onCheckedChange={checked => field.onChange(checked)}
                  />
                </FormControl>
              </FormItem>
            )}
          />
          <div>{orderItem.product.sku}</div>
        </div>
      </TableCell>
      <TableCell>{orderItem.product.name}</TableCell>
      <TableCell className="text-center">{orderItem.quantity}</TableCell>
      <TableCell className="w-28">
        <FormField
          control={control}
          name={`returnItems.${index}.quantity`}
          render={({ field }) => (
            <FormItem className="w-28">
              <FormControl>
                <Input
                  className="text-center w-full"
                  {...field}
                  type="number"
                  onChange={e => {
                    const input = e.target.value;
                    const number = Number(input);
                    const minQty = 1;
                    const maxQty = orderItem.quantity ?? 0;

                    const isValid =
                      input === "" ||
                      (Number.isInteger(number) &&
                        number >= minQty &&
                        number <= maxQty);

                    if (isValid) {
                      field.onChange(input);
                    }
                  }}
                />
              </FormControl>
            </FormItem>
          )}
        />
      </TableCell>
      <TableCell className="min-w-32 md:min-w-36 lg:min-w-40">
        <FormField
          control={control}
          name={`returnItems.${index}.reason`}
          render={({ field }) => (
            <FormItem>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger className="w-full">
                    <SelectValue
                      placeholder="Select reason"
                      defaultValue={returnReasonOptions[1].value}
                    />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {returnReasonOptions?.map((reason, index) => (
                    <SelectItem key={`reason-${index}`} value={reason.value}>
                      {reason.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
      </TableCell>
    </TableRow>
  );
};

export default CreateReturnModal;
