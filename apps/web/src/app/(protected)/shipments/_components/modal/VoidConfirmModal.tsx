import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { cn } from "@/lib/utils";
import { useRefundShipmentMutation } from "@/store/api/shipmentApi";
import { ShipmentStatus } from "@repo/database";
import { AlertTriangle, CircleX, Loader2 } from "lucide-react";
import { useState } from "react";

type VoidConfirmModalProp = {
  open: boolean;
  shipments: {
    id: string;
    shipmentNo: string;
    status: ShipmentStatus;
  }[];
  onOpenChange?: (open: boolean) => void;
};

type refundShipment = {
  id: string;
  shipmentNo: string;
  status: "idle" | "processing" | "success" | "failed";
  error: string | null;
};

const VoidConfirmModal = ({
  open,
  shipments,
  onOpenChange,
}: VoidConfirmModalProp) => {
  const [refundShipment] = useRefundShipmentMutation();

  const [refundShipments, setRefundShipments] = useState<refundShipment[]>(
    shipments.map(shipment => ({
      id: shipment.id,
      shipmentNo: shipment.shipmentNo,
      status: "idle",
      error: null,
    }))
  );

  const updateStatus = (
    id: string,
    status: "idle" | "processing" | "success" | "failed",
    error: string | null
  ) => {
    setRefundShipments(prev =>
      prev.map(refund =>
        refund.id === id ? { ...refund, status, error } : refund
      )
    );
  };

  const handleRefundShipment = async () => {
    setRefundShipments(prev => prev.map(p => ({ ...p, status: "processing" })));

    const promises = refundShipments.map(async refund => {
      try {
        const response = await refundShipment({ id: refund.id }).unwrap();
        updateStatus(refund.id, "success", null);
        return { data: response.shipment };
      } catch (error) {
        console.log(error);

        return updateStatus(refund.id, "failed", "Failed to void label");
      }
    });

    await Promise.allSettled(promises);
  };

  const handleDialogOpenChange = (isOpen: boolean) => {
    onOpenChange?.(isOpen);
  };

  return (
    <AlertDialog open={open} onOpenChange={handleDialogOpenChange}>
      <AlertDialogContent className="py-8 w-11/12 max-w-md sm:max-w-md">
        <AlertDialogHeader className="flex flex-col items-center">
          <div
            className={cn(
              "my-2 size-10 lg:size-12 [&>svg]:size-5 lg:[&>svg]:size-6 rounded-full grid place-content-center bg-destructive/20 text-destructive"
            )}
          >
            <CircleX />
          </div>
          <AlertDialogTitle className="text-lg lg:text-xl text-center">
            Void Refund
          </AlertDialogTitle>
          <AlertDialogDescription className="text-sm text-center">
            Are you sure you want to void refund?
          </AlertDialogDescription>
        </AlertDialogHeader>

        {refundShipments.map(refund => {
          return (
            <VoidCard
              key={refund.id}
              shipmentNo={refund.shipmentNo}
              status={refund.status}
              error={refund.error ?? null}
            />
          );
        })}

        <AlertDialogFooter className="mt-2">
          <AlertDialogCancel className="w-full shrink-1 text-muted-foreground border-none hover:bg-muted shadow-none">
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            className={cn(
              "w-full shrink-1 text-white bg-destructive hover:bg-destructive/90"
            )}
            onClick={e => {
              e.preventDefault();
              handleRefundShipment();
            }}
          >
            Confirm
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

type VoidCardProp = {
  shipmentNo: string;
  status: string;
  error: string | null;
};

const VoidCard = ({ shipmentNo, status, error }: VoidCardProp) => {
  return (
    <>
      <div className="bg-muted py-3 px-4 rounded-md space-y-2">
        <div className="flex justify-between items-center">
          <div className="text-sm font-semibold">{shipmentNo}</div>

          <div>
            {status === "processing" && (
              <Loader2 className="animate-spin text-primary size-5" />
            )}

            {status === "success" && (
              <div className="flex flex-col justify-end items-end">
                <div className="text-xs text-success">Success</div>
              </div>
            )}

            {status === "failed" && (
              <div className="flex items-end">
                <div className="text-xs text-destructive">Failed</div>
              </div>
            )}
          </div>
        </div>

        {error && (
          <div className="px-4 py-2.5 bg-destructive/10 rounded-md flex items-center gap-2">
            <AlertTriangle className="text-destructive size-4 shrink-0" />
            <p className="text-destructive text-sm">{error}</p>
          </div>
        )}
      </div>
    </>
  );
};

export default VoidConfirmModal;
