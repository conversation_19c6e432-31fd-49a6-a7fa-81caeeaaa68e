import {
  Table,
  TableBody,
  // TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import Header from "@/components/layout/Header";
import { FieldError, useFormContext } from "react-hook-form";
// import { CircleP<PERSON>, Trash } from "lucide-react";
// import { Button } from "@/components/ui/button";
// import { useClaimForm } from "../claimForm/ClaimFormContext";

const ClaimItemTable = () => {
  const form = useFormContext();
  const error = form.formState.errors.items as FieldError | undefined;

  // const { claimItems, setAddItemModalOpen, removeClaimItem } = useClaimForm();

  // const handleAddItem = () => {
  //   // setAddItemModalOpen(true);
  // };

  return (
    <>
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Header variant="section" as="h3">
              Items
            </Header>
            {error && (
              <p className="text-destructive text-xs">{error.message}</p>
            )}
          </div>
        </div>
        <Table className="bg-muted rounded-md">
          <TableHeader>
            <TableRow>
              <TableHead className="w-[100px]">SKU</TableHead>
              <TableHead>Name</TableHead>
              <TableHead className="text-right">Quantity</TableHead>
              <TableHead className="text-right">Price (USD)</TableHead>
              <TableHead></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {/* {claimItems.length > 0 ? (
              claimItems.map((item, index) => (
                <TableRow key={item.id}>
                  <TableCell>{item.sku || "-"}</TableCell>
                  <TableCell>{item.name}</TableCell>
                  <TableCell className="text-right">{item.quantity}</TableCell>
                  <TableCell className="text-right">
                    ${item.price.toFixed(2)}
                  </TableCell>
                  <TableCell className="text-right">
                    <Button
                      type="button"
                      size="xs"
                      variant="ghost-destructive"
                      className="bg-background"
                      onClick={() => removeClaimItem(index)}
                    >
                      <Trash /> Delete
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={5}
                  className="text-center text-sm text-muted-foreground py-5"
                >
                  <Button
                    type="button"
                    size="xs"
                    variant="secondary"
                    onClick={handleAddItem}
                  >
                    <CirclePlus />
                    Add item
                  </Button>
                </TableCell>
              </TableRow>
            )} */}
          </TableBody>
        </Table>
        {/* {claimItems.length > 0 && (
          <Button
            type="button"
            size="xs"
            variant="secondary"
            onClick={handleAddItem}
          >
            <CirclePlus /> Add Item
          </Button>
        )} */}
      </div>
    </>
  );
};

export default ClaimItemTable;
