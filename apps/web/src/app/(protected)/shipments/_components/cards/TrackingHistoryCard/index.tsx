"use client";

import DataTable from "@/components/common/table/DataTable";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import { useGetShipmentEasypostQuery } from "@/store/api/shipmentApi";
import styles from "@/styles/Dashboard.module.css";
import { TrackingDetail as EpTrackingDetail } from "@repo/easypost-types";
import {
  ColumnDef,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { createTrackingHistoryColumns } from "./useColumns";

const PAGE_SIZE_OPTIONS = [10, 25, 50];

type TableWrapperProps = {
  epShipmentId: string;
};

const TableWrapper = ({ epShipmentId }: TableWrapperProps) => {
  const { data: epShipment, isLoading } =
    useGetShipmentEasypostQuery(epShipmentId);

  const columns = [...createTrackingHistoryColumns()];

  return (
    <div className={cn(styles.panelContainer, "space-y-4")}>
      <div className="font-semibold text-foreground text-base lg:text-lg">
        Tracking History
      </div>
      {epShipment && !isLoading ? (
        <>
          {epShipment.tracker?.trackingDetails ? (
            <Table
              data={epShipment.tracker.trackingDetails}
              columns={columns}
            />
          ) : (
            <div className="text-sm text-muted-foreground">
              No tracking history found
            </div>
          )}
        </>
      ) : (
        <Skeleton className="h-[340px]" />
      )}
    </div>
  );
};

type TableProps = {
  data: EpTrackingDetail[];
  columns: ColumnDef<EpTrackingDetail>[];
};

const Table = ({ data, columns }: TableProps) => {
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  return (
    <DataTable
      variant="dark"
      table={table}
      pageSizeOptions={PAGE_SIZE_OPTIONS}
      showPagination={false}
      height={"max-h-[340px]"}
    />
  );
};

export default TableWrapper;
