import Status from "@/components/common/Status";
import { TrackingDetail as EpTrackingDetail } from "@repo/easypost-types";
import { ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";

export const createTrackingHistoryColumns =
  (): ColumnDef<EpTrackingDetail>[] => [
    {
      accessorKey: "datetime",
      header: "Date/Time",
      cell: ({ row }) => {
        const { datetime } = row.original;
        return (
          <div className="text-muted-foreground">
            {format(datetime, "dd/MM/yy - HH:mm")}
          </div>
        );
      },
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const { status } = row.original;
        return <Status text={status} />;
      },
    },
    {
      accessorKey: "message",
      header: "Description",
      cell: ({ row }) => {
        const { message } = row.original;

        return (
          <div className="text-muted-foreground">
            {message ? message : " - "}
          </div>
        );
      },
    },
  ];
