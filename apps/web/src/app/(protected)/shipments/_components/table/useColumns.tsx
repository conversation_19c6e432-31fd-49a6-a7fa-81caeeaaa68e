import Status from "@/components/common/Status";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useConfirm } from "@/hooks/useConfirm";
import { getCarrierIcon } from "@/lib/mappings/carriers";
import { useRefundShipmentMutation } from "@/store/api/shipmentApi";
import { GetShipmentsResponse } from "@/store/types/shipment";
import { Shipment } from "@/types/Shipment/Shipment";
import { HomeIcon, PrinterIcon } from "@heroicons/react/24/solid";
import { Order, ShipmentStatus } from "@repo/database";
import { getCarrierBasicInfo, getServiceLevel } from "@repo/shared-data";
import { ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";
import { CircleX, EllipsisVertical, Search } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";
import { toast } from "sonner";
import VoidConfirmModal from "../modal/VoidConfirmModal";

export const createShipmentColumns = (): ColumnDef<
  GetShipmentsResponse["data"][number]
>[] => [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={value => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={value => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "shipmentNo",
    header: "Shipment No.",
    cell: ({ row }) => {
      const data = row.original;
      return (
        <Link
          href={`/shipments/${data.id}`}
          className="hover:text-primary hover:underline underline-offset-2"
        >
          {data?.shipmentNo}
        </Link>
      );
    },
  },
  {
    accessorKey: "orderNo",
    header: "Order No.",
    cell: ({ row }) => {
      const data = row.original;
      return (
        <Link
          href={`/orders/${data.orderId}`}
          className="hover:text-primary hover:underline underline-offset-2"
        >
          {data?.order?.orderNo}
        </Link>
      );
    },
  },
  {
    accessorKey: "trackingCode",
    header: "Tracking Code",
  },
  {
    accessorKey: "service",
    header: () => <div className="min-w-20">Service</div>,
    cell: ({ row }) => {
      const carrier = getCarrierBasicInfo(row.original.carrier ?? "");
      const service = getServiceLevel(carrier?.id, row.original.service ?? "");
      if (!carrier || !service) return null;
      const carrierIcon = getCarrierIcon(carrier.id);

      return (
        <div className="flex flex-col gap-0.5 px-1">
          <div className="flex items-center justify-start gap-1">
            <Image
              src={carrierIcon}
              alt={carrier.fullName}
              className="h-4 w-auto mr-auto max-w-20"
            />
            <span className="text-xs grow-1 font-semibold">
              {carrier.shortName}
            </span>
          </div>
          <span className="text-xs text-muted-foreground">{service.label}</span>
        </div>
      );
    },
  },

  {
    accessorKey: "fromAddress",
    header: "Ship From",
    cell: ({ row }) => {
      const fromAddress = row.original.fromAddress as {
        name?: string;
        street1?: string;
        street2?: string;
        city?: string;
        state?: string;
        zip?: string;
        country?: string;
        phone?: string;
        email?: string;
      };

      const formattedAddress = `
      ${fromAddress?.street1 ?? ""} ${fromAddress?.street2 ?? ""} 
      ${fromAddress?.city ?? ""}, ${fromAddress?.state ?? ""} 
      ${fromAddress?.zip ?? ""} ${fromAddress?.country ?? ""} `;

      return (
        <div className=" max-w-36">
          <div className="truncate capitalize">
            {fromAddress?.name?.toLocaleLowerCase()}
          </div>
          <div className="truncate text-xs text-muted-foreground capitalize">
            {formattedAddress.toLocaleLowerCase()}
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: "customer",
    header: "Customer",
    cell: ({ row }) => {
      const toAddress = row.original.toAddress as {
        name: string;
        state: string;
        country: string;
        residential: boolean;
      };

      const { name, state, country, residential } = toAddress;
      return (
        <div className="leading-4">
          <p className="capitalize">
            {residential && (
              <HomeIcon className="size-3.5 inline-block text-primary mr-1 mb-1" />
            )}
            {name.toLocaleLowerCase()}
          </p>
          <p className="text-xs text-muted-foreground">
            {state}, {country}
          </p>
        </div>
      );
    },
  },
  {
    accessorKey: "shipDate",
    header: "Ship Date",
    cell: ({ row }) => {
      const { createdAt } = row.original;
      return <div>{format(createdAt, "MM/dd/yyyy")}</div>;
    },
  },
  {
    accessorKey: "rate",
    header: () => {
      return <div className="text-center">Rate</div>;
    },
    cell: ({ row }) => {
      return <div className="text-center">$ {row.original.rate}</div>;
    },
  },
  {
    accessorKey: "printed",
    header: "Label",
    cell: ({ row }) => (
      <div className="text-muted-foreground">
        {row.original.labelPrinted ? "Printed" : "Not Printed"}
      </div>
    ),
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const { status } = row.original;
      return <Status text={status} />;
    },
  },
];

type createShipmentActionsProps = {
  handleViewShipment: (id: string) => void;
  // handleRefundShipment: (id: string) => void;
};

export const createShipmentActions = ({
  handleViewShipment,
}: createShipmentActionsProps): ColumnDef<
  Shipment & { order: Pick<Order, "orderNo"> }
>[] => {
  return [
    {
      id: "actions",
      cell: ({ row }) => (
        <ShipmentActionCell
          shipment={row.original}
          handleViewShipment={handleViewShipment}
        />
      ),
    },
  ];
};

const ShipmentActionCell = ({
  shipment,
  handleViewShipment,
}: {
  shipment: Shipment & { order: Pick<Order, "orderNo"> };
  handleViewShipment: (id: string) => void;
}) => {
  const [isOpenVoidConfirm, setIsOpenVoidConfirm] = useState(false);

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="icon-xs">
            <span className="sr-only">Open menu</span>
            <EllipsisVertical />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem>
            <PrinterIcon className="size-4 text-primary" />
            Print Label
          </DropdownMenuItem>
          {shipment.status !== ShipmentStatus.voided && (
            <VoidLabelMenu shipmentId={shipment.id} />
          )}
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => handleViewShipment(shipment.id)}>
            <Search className="size-4 text-primary" />
            Detail
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {isOpenVoidConfirm && (
        <VoidConfirmModal
          open={isOpenVoidConfirm}
          onOpenChange={setIsOpenVoidConfirm}
          shipments={[shipment]}
        />
      )}
    </>
  );
};

const VoidLabelMenu = ({ shipmentId }: { shipmentId: string }) => {
  const { confirm, ConfirmModal } = useConfirm();

  const [refundShipment] = useRefundShipmentMutation();

  const handleVoidLabel = async (e: React.MouseEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();

    const isConfirmed = await confirm({
      title: "Void Label",
      description: "Are you sure you want to void this label?",
      variant: "destructive",
    });

    if (!isConfirmed) return;

    try {
      await refundShipment({ id: shipmentId }).unwrap();
      toast.success("Label voided successfully");
    } catch (error) {
      if (process.env.NODE_ENV === "development") console.log(error);
      toast.error("Unable to request refund. The parcel has been shipped.");
    }
  };

  return (
    <>
      <DropdownMenuItem variant="destructive" onClick={handleVoidLabel}>
        <CircleX className="size-4 text-destructive" />
        Void label
      </DropdownMenuItem>
      <ConfirmModal />
    </>
  );
};
