import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Separator } from "@/components/ui/separator";
import { GetShipmentsResponse } from "@/store/types/shipment";
import { ShipmentStatus } from "@repo/database";
import { Table } from "@tanstack/react-table";
import { ChevronDown, CircleX, EllipsisVertical } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import VoidConfirmModal from "../../modal/VoidConfirmModal";

type SelectionActionsProps = {
  table: Table<GetShipmentsResponse["data"][number]>;
  noOfSelectedRows: number;
};

const SelectionActions = ({
  table,
  noOfSelectedRows,
}: SelectionActionsProps) => {
  const [isOpenVoidConfirm, setIsOpenVoidConfirm] = useState<boolean>(false);
  const selectedShipments = table
    .getFilteredSelectedRowModel()
    .rows.map(row => {
      return {
        id: row.original.id,
        shipmentNo: row.original.shipmentNo,
        status: row.original.status,
      };
    });

  const selectedRows = table.getSelectedRowModel().rows;
  const canVoid = selectedRows.every(
    row => row.original.status !== ShipmentStatus.voided
  );

  return (
    <>
      <span className="text-sm font-semibold">{noOfSelectedRows} selected</span>
      <Separator orientation="vertical" className="mx-2" />
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="muted">
            <EllipsisVertical /> Actions
            <ChevronDown />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-56">
          <DropdownMenuItem
            variant="destructive"
            onSelect={e => {
              e.preventDefault();
              e.stopPropagation();
              if (canVoid) setIsOpenVoidConfirm(true);
              else toast.error("Can't void label");
            }}
          >
            <div className="flex space-x-2 items-center">
              <CircleX className="size-4 text-destructive" />
              <span>Void label</span>
            </div>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {isOpenVoidConfirm && (
        <VoidConfirmModal
          open={isOpenVoidConfirm}
          onOpenChange={open => {
            setIsOpenVoidConfirm(open);
            if (!open) {
              table.setRowSelection({});
            }
          }}
          shipments={selectedShipments}
        />
      )}
    </>
  );
};

export default SelectionActions;
