import Status from "@/components/common/Status";
import OrderItemHoverCard from "@/components/common/table/customCells/OrderItemHoverCard";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { getCarrierIcon } from "@/lib/mappings/carriers";
import { GetReturnsResponse } from "@/store/types/return";
import { HomeIcon } from "@heroicons/react/24/solid";
import { Order, Return } from "@repo/database";
import { getCarrierBasicInfo, getServiceLevel } from "@repo/shared-data";
import { ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";
import { EllipsisVertical, Search } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

export const createReturnColumns = (): ColumnDef<
  GetReturnsResponse["data"][number]
>[] => [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={value => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={value => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "rma",
    header: "RMA",
    cell: ({ row }) => {
      return (
        <Link
          href={`/shipments/returns/${row.original.id}`}
          className="hover:text-primary hover:underline underline-offset-2"
        >
          {row.original.rma}
        </Link>
      );
    },
  },
  {
    accessorKey: "orderNo",
    header: "Order No.",
    cell: ({ row }) => {
      const data = row.original;
      return (
        <Link
          href={`/orders/${data.orderId}`}
          className="hover:text-primary hover:underline underline-offset-2"
        >
          {data?.order?.orderNo}
        </Link>
      );
    },
  },
  {
    accessorKey: "trackingCode",
    header: "Tracking Code",
  },
  {
    id: "sku",
    header: "SKU",
    cell: ({ row }) => {
      const { orderItems } = row.original;
      return orderItems.length <= 1 ? (
        <div>{orderItems[0]?.product?.sku}</div>
      ) : (
        <OrderItemHoverCard orderItems={orderItems} />
      );
    },
  },
  {
    id: "orderItems",
    header: "Item Name",
    cell: ({ row }) => {
      const { orderItems } = row.original;
      return orderItems.length <= 1 ? (
        <div>{orderItems[0].product.name}</div>
      ) : (
        <OrderItemHoverCard orderItems={orderItems} />
      );
    },
  },
  {
    id: "itemQuantity",
    header: () => <div className="text-right">Item Quantity</div>,
    cell: ({ row }) => {
      const { orderItems } = row.original;
      return <div className="text-right">{orderItems.length}</div>;
    },
  },

  {
    accessorKey: "service",
    header: () => <div className="min-w-20">Service</div>,
    cell: ({ row }) => {
      const carrier = getCarrierBasicInfo(row.original.carrier ?? "");
      const service = getServiceLevel(carrier?.id, row.original.service ?? "");
      if (!carrier || !service) return null;
      const carrierIcon = getCarrierIcon(carrier.id);

      return (
        <div className="flex flex-col gap-0.5 px-1">
          <div className="flex items-center justify-start gap-1">
            <Image
              src={carrierIcon}
              alt={carrier.fullName}
              className="h-4 w-auto mr-auto max-w-20"
            />
            <span className="text-xs grow-1 font-semibold">
              {carrier.shortName}
            </span>
          </div>
          <span className="text-xs text-muted-foreground">{service.label}</span>
        </div>
      );
    },
  },
  {
    accessorKey: "fromAddress",
    header: "Ship From",
    cell: ({ row }) => {
      const fromAddress = row.original.fromAddress as {
        name?: string;
        street1?: string;
        street2?: string;
        city?: string;
        state?: string;
        zip?: string;
        country?: string;
        phone?: string;
        email?: string;
        residential?: boolean;
      };

      const { name, state, country, residential, street1, street2, city, zip } =
        fromAddress;

      const formattedAddress = `
      ${street1 ?? ""} ${street2 ?? ""} 
      ${city ?? ""}, ${state ?? ""} 
      ${zip ?? ""} ${country ?? ""} `;

      return (
        <div className="leading-4">
          <p className="capitalize">
            {residential && (
              <HomeIcon className="size-3.5 inline-block text-primary mr-1 mb-1" />
            )}
            {name?.toLocaleLowerCase()}
          </p>
          <p className="truncate text-xs text-muted-foreground capitalize">
            {formattedAddress.toLocaleLowerCase()}
          </p>
        </div>
      );
    },
  },
  {
    accessorKey: "customer",
    header: "Return To",
    cell: ({ row }) => {
      const toAddress = row.original.toAddress as {
        name: string;
        state: string;
        country: string;
        residential: boolean;
      };

      const { name, state, country, residential } = toAddress;
      return (
        <div className="leading-4">
          <p className="capitalize">
            {residential && (
              <HomeIcon className="size-3.5 inline-block text-primary mr-1 mb-1" />
            )}
            {name.toLocaleLowerCase()}
          </p>
          <p className="text-xs text-muted-foreground">
            {state}, {country}
          </p>
        </div>
      );
    },
  },
  {
    accessorKey: "shipDate",
    header: "Ship Date",
    cell: ({ row }) => {
      const { createdAt } = row.original;
      return <div>{format(createdAt, "MM/dd/yyyy")}</div>;
    },
  },
  {
    accessorKey: "rate",
    header: () => {
      return <div className="text-center">Rate</div>;
    },
    cell: ({ row }) => {
      return <div className="text-center">$ {row.original.rate}</div>;
    },
  },
  {
    accessorKey: "printed",
    header: "Label",
    cell: ({ row }) => (
      <div className="text-muted-foreground">
        {row.original.labelPrinted ? "Printed" : "Not Printed"}
      </div>
    ),
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const { status } = row.original;
      return <Status text={status} />;
    },
  },
];

type createReturnActionsProps = {
  handleViewReturn: (id: string) => void;
};

export const createReturnActions = ({
  handleViewReturn,
}: createReturnActionsProps): ColumnDef<
  Return & { order: Pick<Order, "orderNo"> }
>[] => [
  {
    id: "actions",
    cell: ({ row }) => {
      const returnData = row.original;
      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon-xs">
              <span className="sr-only">Open menu</span>
              <EllipsisVertical />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {/* <DropdownMenuItem>
              <PrinterIcon className="size-4 text-primary" />
              Print Label
            </DropdownMenuItem>
            <DropdownMenuItem>
              <CircleX className="size-4 text-destructive" />
              Void label
            </DropdownMenuItem>
            <DropdownMenuSeparator /> */}
            <DropdownMenuItem onClick={() => handleViewReturn(returnData.id)}>
              <Search className="size-4 text-primary" />
              Detail
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];
