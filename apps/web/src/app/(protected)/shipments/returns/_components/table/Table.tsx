"use client";

import SkeletonTable from "@/components/loading/SkeletonTable";
import DataTable from "@/components/common/table/DataTable";
import { createPaginationHandler } from "@/lib/utils/table/pagination";
import { useGetReturnsQuery } from "@/store/api/returnApi";
import { GetReturnsResponse } from "@/store/types/return";
import {
  ColumnDef,
  ColumnFilter,
  ColumnFiltersState,
  getCoreRowModel,
  OnChangeFn,
  PaginationState,
  Updater,
  useReactTable,
  VisibilityState,
} from "@tanstack/react-table";
import { Loader2 } from "lucide-react";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useMemo, useState } from "react";
import { createReturnActions, createReturnColumns } from "./useColumns";
import Toolbar from "./Toolbar";

const PAGE_SIZE_OPTIONS = [100, 250, 500, 1000];
const DEFAULT_PAGE_SIZE = PAGE_SIZE_OPTIONS[0];
const DEFAULT_PAGE_INDEX = 0;

const TableWrapper = () => {
  const [search, setSearch] = useState("");
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: DEFAULT_PAGE_INDEX,
    pageSize: DEFAULT_PAGE_SIZE,
  });
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);

  const queryStr = useMemo(() => {
    const query = new URLSearchParams();
    // Pagination
    query.append("limit", pagination.pageSize.toString());
    query.append(
      "offset",
      (pagination.pageIndex * pagination.pageSize).toString()
    );
    // Filters
    columnFilters.forEach((filter: ColumnFilter) => {
      if (typeof filter.value === "object" && filter.value !== null) {
        Object.entries(filter.value).forEach(([key, value]) => {
          query.append(`filter[${filter.id}][${key}]`, value);
        });
      } else query.append(`filter[${filter.id}]`, `${filter.value}`);
    });
    // Search
    if (search.trim()) query.append("search", search);

    return query.toString();
  }, [pagination, columnFilters, search]);

  const { data: returns, isLoading, isFetching } = useGetReturnsQuery(queryStr);

  const { push } = useRouter();

  const handleViewReturn = useCallback(
    (id: string) => {
      push(`/shipments/returns/${id}`);
    },
    [push]
  );

  const columns = useMemo(
    () => [
      ...createReturnColumns(),
      ...createReturnActions({ handleViewReturn }),
    ],
    [handleViewReturn]
  );

  const [mounted, setMounted] = useState(false);
  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  return (
    <div className="mt-8 lg:mt-10 w-full max-w-full flex flex-col">
      {isLoading || !returns || !columns ? (
        <SkeletonTable tableHeight="h-[calc(100dvh-260px)]" />
      ) : (
        <Table
          isFetching={isFetching}
          data={returns}
          columns={columns as []}
          pagination={pagination}
          setPagination={setPagination}
          columnFilters={columnFilters}
          setColumnFilters={setColumnFilters}
          setSearch={setSearch}
        />
      )}
    </div>
  );
};

type TableProps = {
  isFetching: boolean;
  data: GetReturnsResponse;
  columns: ColumnDef<GetReturnsResponse["data"][number]>[];
  pagination: PaginationState;
  setPagination: React.Dispatch<React.SetStateAction<PaginationState>>;
  columnFilters: ColumnFiltersState;
  setColumnFilters: React.Dispatch<React.SetStateAction<ColumnFiltersState>>;
  setSearch: React.Dispatch<React.SetStateAction<string>>;
};

const Table = ({
  isFetching,
  data,
  columns,
  pagination,
  setPagination,
  columnFilters,
  setColumnFilters,
  setSearch,
}: TableProps) => {
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});

  const onPaginationChange = useCallback(
    (updater: Updater<PaginationState>) =>
      createPaginationHandler(
        data.pagination,
        pagination,
        setPagination
      )(updater),
    [data.pagination, pagination, setPagination]
  );

  const table = useReactTable({
    data: data.data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    // Pagination'
    manualPagination: true,
    onPaginationChange: onPaginationChange as OnChangeFn<PaginationState>,
    rowCount: data.pagination.total,
    // Filtering
    manualFiltering: true,
    onColumnFiltersChange: setColumnFilters as OnChangeFn<ColumnFiltersState>,
    // Column Visibility Settings
    onColumnVisibilityChange: setColumnVisibility,
    // Row Selection
    onRowSelectionChange: setRowSelection,
    state: {
      pagination,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  return (
    <div className="flex flex-col gap-4">
      <Toolbar table={table} setSearch={setSearch} />

      {!isFetching ? (
        <DataTable
          table={table}
          pageSizeOptions={PAGE_SIZE_OPTIONS}
          height="h-[calc(100dvh-307px)]"
        />
      ) : (
        <div className="h-[calc(100dvh-304px)] grid place-items-center border rounded-md">
          <Loader2 className="size-6 animate-spin text-primary" />
        </div>
      )}
    </div>
  );
};

export default TableWrapper;
