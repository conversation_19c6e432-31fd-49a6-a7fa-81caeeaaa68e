import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Separator } from "@/components/ui/separator";
import { GetReturnsResponse } from "@/store/types/return";
import { Table } from "@tanstack/react-table";
import { ChevronDown, CircleX, EllipsisVertical } from "lucide-react";

type SelectionActionsProps = {
  table: Table<GetReturnsResponse["data"][number]>;
  noOfSelectedRows: number;
};

const SelectionActions = ({
  // table,
  noOfSelectedRows,
}: SelectionActionsProps) => {
  return (
    <>
      <span className="text-sm font-semibold">{noOfSelectedRows} selected</span>
      <Separator orientation="vertical" className="mx-2" />
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="muted">
            <EllipsisVertical /> Actions
            <ChevronDown />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-56">
          <DropdownMenuItem variant="destructive">
            <CircleX /> <span className="text-foreground">Void label</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </>
  );
};

export default SelectionActions;
