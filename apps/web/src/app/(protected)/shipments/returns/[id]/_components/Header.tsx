import SummaryCard from "@/components/common/cards/SummaryCard";
import { Button } from "@/components/ui/button";
import { ShipmentStatus } from "@repo/database";
import { ChevronLeft } from "lucide-react";
import Link from "next/link";

type HeaderProps = {
  rma: string;
  status: ShipmentStatus;
  orderNo: string;
};

const Header = ({ rma, status, orderNo }: HeaderProps) => {
  return (
    <div className="flex flex-col md:flex-row gap-2 justify-between items-start">
      {/* Left */}
      <div className="flex items-center gap-1">
        <Link href="/shipments/returns">
          <Button size="icon-sm" variant="ghost">
            <ChevronLeft />
          </Button>
        </Link>
        <p className="font-semibold text-lg">Return - {rma}</p>
      </div>

      <SummaryCard
        data={[
          { label: "status", value: status },
          { label: "RMA", value: rma },
          { label: "Order No", value: orderNo },
        ]}
      />
    </div>
  );
};

export default Header;
