import OrderDetailsCard from "@/components/common/cards/OrderDetailsCard";
import RateDetailsCard from "@/components/common/cards/RateDetailsCard";
import RefundHistoryCard from "@/components/common/cards/RefundHistoryCard";
import ShipmentDetailsCard from "@/components/common/cards/ShipmentDetailsCard";
import { getReturnById } from "@/data/return";
import { cn } from "@/lib/utils";
import styles from "@/styles/Dashboard.module.css";
import { notFound } from "next/navigation";
import TrackingHistoryCard from "../../_components/cards/TrackingHistoryCard";
import Header from "./_components/Header";

const ReturnDetailPage = async ({
  params,
}: {
  params: Promise<{ id: string }>;
}) => {
  const { id } = await params;
  const returnData = await getReturnById(id);
  if (!returnData) return notFound();

  return (
    <div className={cn(styles.pagePadding, "relative")}>
      <Header
        rma={returnData.rma}
        status={returnData.status}
        orderNo={returnData.order.orderNo}
      />

      <main
        className={cn(
          styles.gridGap,
          "mt-4 lg:mt-6 grid grid-cols-1 lg:grid-cols-[3fr_2fr] xl:grid-cols-[2fr_1fr] items-start"
        )}
      >
        {/* Left Panels */}
        <div className={cn(styles.gridGap, "flex flex-col")}>
          <OrderDetailsCard
            isReturn={true}
            rma={returnData.rma}
            orderDate={returnData.createdAt}
            orderNo={returnData.order.orderNo}
            toAddress={returnData.fromAddress}
            orderItems={returnData.orderItems}
            notes={returnData?.notes ?? ""}
          />
          <ShipmentDetailsCard
            fromAddress={returnData.fromAddress}
            toAddress={returnData.toAddress}
            parcel={returnData.parcel}
            shipDate={returnData.shipDate}
            textToAddress="Return To"
          />
          <TrackingHistoryCard epShipmentId={returnData.epShipmentId} />
        </div>

        {/* Right Panel */}
        <div className={cn(styles.gridGap, "flex flex-col")}>
          <RateDetailsCard shipment={returnData} objectType="return" />
          <RefundHistoryCard refunds={returnData.refund} />
        </div>
      </main>
    </div>
  );
};

export default ReturnDetailPage;
