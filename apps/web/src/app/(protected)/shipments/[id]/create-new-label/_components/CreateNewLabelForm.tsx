"use client";

import OrderDetailsCard from "@/components/common/cards/OrderDetailsCard";
import ShippingAndParcelDetailForm from "@/components/common/forms/ShippingAndParcelDetailForm";
import { cn } from "@/lib/utils";
import { shipmentToCreateNewLabelForm } from "@/lib/utils/shipment";
import CreateNewLabelSchema from "@/schemas/shipment/CreateLabelSchema";
import styles from "@/styles/Dashboard.module.css";
import { FindOneShipment } from "@/types/Shipment/Shipment";
import { zodResolver } from "@hookform/resolvers/zod";
import { FormProvider, useForm } from "react-hook-form";
import { z } from "zod";
import CreateLabelActionsCard from "./CreateLabelActionsCard";

type CreateNewLabelFormProps = {
  shipment: FindOneShipment;
};

const CreateNewLabelForm = ({ shipment }: CreateNewLabelFormProps) => {
  const form = useForm<z.infer<typeof CreateNewLabelSchema>>({
    resolver: zodResolver(CreateNewLabelSchema),
    defaultValues: shipmentToCreateNewLabelForm(shipment),
  });

  return (
    <main>
      <FormProvider {...form}>
        <form
          className={cn(
            styles.gridGap,
            "mt-4 lg:mt-6 grid grid-cols-1 lg:grid-cols-[3fr_2fr] xl:grid-cols-[2fr_1fr] items-start"
          )}
        >
          {/* Left Panels */}
          <div className={cn(styles.gridGap, "flex flex-col")}>
            <ShippingAndParcelDetailForm />
            <OrderDetailsCard
              toAddress={shipment.toAddress}
              orderDate={shipment.createdAt}
              orderNo={shipment.order.orderNo}
              orderItems={shipment.order.orderItems}
              notes={shipment?.order?.notes ?? ""}
            />
          </div>

          {/* Right Panels */}
          <div className={cn(styles.gridGap, "flex flex-col")}>
            <CreateLabelActionsCard
              originalShipmentId={shipment.id}
              originalShipmentOrderId={shipment.orderId}
            />
          </div>
        </form>
      </FormProvider>
    </main>
  );
};

export default CreateNewLabelForm;
