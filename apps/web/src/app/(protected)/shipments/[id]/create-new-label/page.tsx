import { getShipmentById } from "@/data/shipment";
import { cn } from "@/lib/utils";
import styles from "@/styles/Dashboard.module.css";
import CreateNewLabelForm from "./_components/CreateNewLabelForm";
import PageHeader from "@/components/layout/PageHeader";
import { ScrollText } from "lucide-react";
import { ShipmentStatus } from "@repo/database";

type CreateNewLabelPageProps = {
  params: Promise<{ id: string }>;
};

const CreateNewLabelPage = async ({ params }: CreateNewLabelPageProps) => {
  const { id } = await params;

  const shipment = await getShipmentById(id);
  if (!shipment || shipment.status !== ShipmentStatus.voided)
    return <div>Shipment not found</div>;

  return (
    <div className={cn(styles.pagePadding, "relative")}>
      <PageHeader header="Create New Label" icon={ScrollText} />
      <CreateNewLabelForm shipment={shipment} />
    </div>
  );
};

export default CreateNewLabelPage;
