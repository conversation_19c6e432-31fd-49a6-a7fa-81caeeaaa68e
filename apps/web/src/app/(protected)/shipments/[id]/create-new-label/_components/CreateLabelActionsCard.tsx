import { generateRates } from "@/actions/rates/generateRates";
import ShippingOptionsForm from "@/components/common/forms/ShippingOptionsForm";
import BuyLabelModal from "@/components/rates/BuyLabelModal";
import RateList from "@/components/rates/RateList";
import Header from "@/components/layout/Header";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { addressToAddressForm } from "@/lib/utils/address";
import { calculateInsuranceCost } from "@/lib/utils/insurance";
import { canGetRates } from "@/lib/utils/rates";
import { useBuyNewLabelMutation } from "@/store/api/shipmentApi";
import styles from "@/styles/Dashboard.module.css";
import { Rate } from "@repo/easypost-types";
import { CircleDollarSign, RefreshCcw, ScrollText } from "lucide-react";
import { useRouter } from "next/navigation";
import { useMemo, useState } from "react";
import { useFormContext, useWatch } from "react-hook-form";
import { toast } from "sonner";

type CreateLabelActionsCardProps = {
  originalShipmentId: string;
  originalShipmentOrderId: string;
};

const CreateLabelActionsCard = ({
  originalShipmentId,
  originalShipmentOrderId,
}: CreateLabelActionsCardProps) => {
  const [selectedRate, setSelectedRate] = useState<Rate | null>(null);
  const [openBuyLabelModal, setOpenBuyLabelModal] = useState(false); // Handle modal open here
  const [generatingRates, setGeneratingRates] = useState(false); // Loading state for get rates
  const [lastRateFetchData, setLastRateFetchData] = useState<string | null>(
    null
  ); // Store latest data that was used to fetch rates to compare

  const { setValue } = useFormContext();
  const [epShipmentId, fromAddressId, toAddress, parcel, insurance] = useWatch({
    name: ["epShipmentId", "fromAddressId", "toAddress", "parcel", "insurance"],
  });

  const { push } = useRouter();

  const [buyNewLabel, { isLoading }] = useBuyNewLabelMutation();

  const rateDataValid = useMemo(() => {
    return canGetRates({
      fromAddress: { id: fromAddressId },
      toAddress,
      parcel,
    });
  }, [fromAddressId, toAddress, parcel]);

  const dataHasChanged = useMemo(() => {
    if (!lastRateFetchData || !epShipmentId) return false;
    const currentData = {
      fromAddressId,
      toAddress,
      parcel,
    };
    return JSON.stringify(currentData) !== lastRateFetchData;
  }, [fromAddressId, toAddress, parcel, lastRateFetchData, epShipmentId]);

  const getRatesBtnDisabled = epShipmentId
    ? !dataHasChanged || !rateDataValid
    : !rateDataValid;

  const handleGetRates = async () => {
    if (!rateDataValid) {
      toast.error("Data is invalid. Please complete your shipping options.");
      return;
    }

    setGeneratingRates(true);

    try {
      const response = await generateRates(
        { id: fromAddressId },
        addressToAddressForm(toAddress),
        parcel
      );

      if (!response.success || !response.data) throw new Error(response.error);

      setValue("epShipmentId", response.data.epShipmentId);
      setLastRateFetchData(
        JSON.stringify({ fromAddressId, toAddress, parcel })
      ); // Store latest data that was used to fetch rates
    } catch (error) {
      if (process.env.NODE_ENV === "development") console.log(error);
      toast.error("Could not generate rates. Please try again");
    } finally {
      setGeneratingRates(false);
    }
  };

  const buyLabelAndCreateShipment = async () => {
    if (!selectedRate) throw new Error("Please select a rate you what to buy");

    const payload = {
      epShipmentId,
      selectedRateId: selectedRate.id,
      orderId: originalShipmentOrderId,
      fromAddressId,
      insuranceAmount: insurance,
    };

    try {
      const response = await buyNewLabel({
        id: originalShipmentId,
        payload,
      }).unwrap();

      push(`/shipments/${response.id}`);
      return response;
    } catch (error) {
      if (process.env.NODE_ENV === "development") console.log(error);
      toast.error(
        error instanceof Error ? error.message : "Could not buy label"
      );
      return null;
    }
  };

  return (
    <div className={cn(styles.panelContainer, styles.panelYSpacing)}>
      <div className="space-y-4">
        <div className="shrink-0 flex justify-between">
          <Header icon={CircleDollarSign}>Rates</Header>
          <Button
            className="ml-auto"
            size="sm"
            variant="link"
            type="button"
            disabled={getRatesBtnDisabled}
            onClick={handleGetRates}
          >
            <RefreshCcw className={cn(generatingRates && "animate-spin")} />
            {epShipmentId ? "Refresh" : "Get rates"}
          </Button>
        </div>

        {epShipmentId && (
          <RateList
            epShipmentId={epShipmentId}
            onSelect={rate => setSelectedRate(rate)}
            selectedRate={selectedRate}
          />
        )}

        <ShippingOptionsForm />

        <div className="flex justify-between gap-4">
          <Button
            type="button"
            className="flex-1"
            disabled={!selectedRate}
            onClick={() => setOpenBuyLabelModal(true)}
          >
            <ScrollText /> Buy Label
          </Button>
        </div>
        <BuyLabelModal
          open={openBuyLabelModal}
          selectedRate={selectedRate}
          insuranceFee={calculateInsuranceCost(insurance)}
          setOpen={setOpenBuyLabelModal}
          onSubmit={buyLabelAndCreateShipment}
          isSubmitting={isLoading}
        />
      </div>
    </div>
  );
};

export default CreateLabelActionsCard;
