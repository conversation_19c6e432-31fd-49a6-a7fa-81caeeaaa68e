"use client";

import OrderDetailsCard from "@/components/common/cards/OrderDetailsCard";
import RateDetailsCard from "@/components/common/cards/RateDetailsCard";
import RefundHistoryCard from "@/components/common/cards/RefundHistoryCard";
import ShipmentDetailsCard from "@/components/common/cards/ShipmentDetailsCard";
import { cn } from "@/lib/utils";
import { useGetShipmentQuery } from "@/store/api/shipmentApi";
import styles from "@/styles/Dashboard.module.css";
import { notFound, useParams } from "next/navigation";
import TrackingHistoryCard from "../_components/cards/TrackingHistoryCard";
import Header from "./_components/Header";
import Loading from "./loading";

const ViewShipmentPage = () => {
  const params = useParams();
  const id = params.id as string;

  const { data: shipment, isLoading: shipmentLoading } =
    useGetShipmentQuery(id);

  if (shipmentLoading) return <Loading />;

  if (!shipment) notFound();

  return (
    <div className={cn(styles.pagePadding, "relative")}>
      <Header shipment={shipment} />
      <main
        className={cn(
          styles.gridGap,
          "mt-4 lg:mt-6 grid grid-cols-1 lg:grid-cols-[3fr_2fr] xl:grid-cols-[2fr_1fr] items-start"
        )}
      >
        {/* Left Panels */}
        <div className={cn(styles.gridGap, "flex flex-col")}>
          <ShipmentDetailsCard
            fromAddress={shipment.fromAddress}
            toAddress={shipment.toAddress}
            parcel={shipment.parcel}
            shipDate={shipment.shipDate}
          />
          <OrderDetailsCard
            toAddress={shipment.toAddress}
            orderDate={shipment.createdAt}
            orderNo={shipment.order.orderNo}
            orderItems={shipment.order.orderItems}
            notes={shipment?.order?.notes ?? ""}
          />

          <TrackingHistoryCard epShipmentId={shipment.epShipmentId} />
        </div>

        {/* Right Panel */}
        <div className={cn(styles.gridGap, "flex flex-col")}>
          <RateDetailsCard objectType="shipment" shipment={shipment} />
          <RefundHistoryCard refunds={shipment.labelRefunds} />
        </div>
      </main>
    </div>
  );
};

export default ViewShipmentPage;
