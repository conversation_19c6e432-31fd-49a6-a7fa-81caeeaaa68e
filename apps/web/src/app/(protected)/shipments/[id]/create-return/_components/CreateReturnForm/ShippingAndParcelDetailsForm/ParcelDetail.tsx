import WeightInputs from "@/components/parcel/WeightInputs";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { UNIT_SYSTEMS } from "@/config/unitSystem";
import { cn } from "@/lib/utils";
import customStyles from "@/styles/Custom.module.css";
import styles from "@/styles/Dashboard.module.css";
import { useCallback } from "react";
import { useFormContext, useWatch } from "react-hook-form";
// import { useGetParcelsQuery } from "@/store/api/parcelApi";

const ParcelDetail = () => {
  const form = useFormContext();
  const parcel = useWatch({ name: "parcel" });
  const disableLWH = useWatch({ name: "parcel.predefinedPackage" });

  // const { data: parcels, isLoading: isLoadingParcels } = useGetParcelsQuery();

  const handleNumberChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    onChange: (value: number) => void
  ) => {
    const value = e.target.value;
    // Only allow one decimal point and one digit after it
    if (
      /^\d*\.?\d{0,1}$/.test(value) &&
      (value.match(/\./g) || []).length <= 1
    ) {
      onChange(Number(value));
    }
  };

  const handleWeightChange = useCallback(
    (value: number) => {
      form.setValue("parcel.weight", value);
    },
    [form]
  );

  return (
    <div className={cn(styles.panelYSpacing)}>
      <div className="space-y-4">
        <div className="grid sm:grid-cols-3 gap-2">
          <FormField
            control={form.control}
            name="parcel.length"
            disabled={parcel.predefinedPackage}
            render={({ field }) => (
              <FormItem className="relative">
                <FormLabel>Length</FormLabel>
                <div className="relative">
                  <FormControl>
                    <Input
                      type="number"
                      step="0.1"
                      min={0}
                      className={cn(
                        customStyles.inputNoArrows,
                        "text-right pr-8"
                      )}
                      {...field}
                      disabled={disableLWH}
                      onChange={e => handleNumberChange(e, field.onChange)}
                    />
                  </FormControl>

                  <span
                    className={cn(
                      "text-muted-foreground text-xs absolute right-3.5 top-1/2 -translate-y-1/2",
                      parcel.predefinedPackage && "opacity-40"
                    )}
                  >
                    {UNIT_SYSTEMS.imperial.dimension.shortLabel}
                  </span>
                </div>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="parcel.width"
            disabled={parcel.predefinedPackage}
            render={({ field }) => (
              <FormItem className="relative">
                <FormLabel>Width</FormLabel>
                <div className="relative">
                  <FormControl>
                    <Input
                      type="number"
                      step="0.1"
                      min={0}
                      className={cn(
                        customStyles.inputNoArrows,
                        "text-right pr-8"
                      )}
                      {...field}
                      disabled={disableLWH}
                      onChange={e => handleNumberChange(e, field.onChange)}
                    />
                  </FormControl>
                  <span
                    className={cn(
                      "text-muted-foreground text-xs absolute right-3.5 top-1/2 -translate-y-1/2",
                      parcel.predefinedPackage && "opacity-40"
                    )}
                  >
                    {UNIT_SYSTEMS.imperial.dimension.shortLabel}
                  </span>
                </div>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="parcel.height"
            disabled={parcel.predefinedPackage}
            render={({ field }) => (
              <FormItem className="relative">
                <FormLabel>Height</FormLabel>
                <div className="relative">
                  <FormControl>
                    <Input
                      type="number"
                      step="0.1"
                      min={0}
                      className={cn(
                        customStyles.inputNoArrows,
                        "text-right pr-8"
                      )}
                      {...field}
                      disabled={disableLWH}
                      onChange={e => handleNumberChange(e, field.onChange)}
                    />
                  </FormControl>
                  <span
                    className={cn(
                      "text-muted-foreground text-xs absolute right-3.5 top-1/2 -translate-y-1/2",
                      parcel.predefinedPackage && "opacity-40"
                    )}
                  >
                    {UNIT_SYSTEMS.imperial.dimension.shortLabel}
                  </span>
                </div>
              </FormItem>
            )}
          />
        </div>

        <WeightInputs
          weight={parcel.weight}
          onWeightChange={handleWeightChange}
        />
      </div>
    </div>
  );
};

export default ParcelDetail;
