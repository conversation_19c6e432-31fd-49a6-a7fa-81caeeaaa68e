"use client";

import AddressFormModal from "@/components/common/forms/AddressFormModal";
import BuyLabelModal from "@/components/rates/BuyLabelModal";
import { calculateInsuranceCost } from "@/lib/utils/insurance";
import { shipmentToReturnForm } from "@/lib/utils/return";
import CreateReturnSchema from "@/schemas/return/CreateReturnSchema";
import { useCreateReturnMutation } from "@/store/api/returnApi";
import { OrderItemWithReason } from "@/types/Return/OrderItemWithReason";
import { PendingReturnItems } from "@/types/Return/PendingReturnItems";
import { Return } from "@/types/Return/Return";
import { FindOneShipment } from "@/types/Shipment/Shipment";
import { zodResolver } from "@hookform/resolvers/zod";
import { Rate } from "@repo/easypost-types";
import { useRouter } from "next/navigation";
import {
  createContext,
  Dispatch,
  SetStateAction,
  useContext,
  useEffect,
  useState,
} from "react";
import { useForm, UseFormReturn } from "react-hook-form";
import { z } from "zod";

type ReturnFormContextType = {
  shipmentId: string;
  orderId: string;
  form: UseFormReturn<z.infer<typeof CreateReturnSchema>>;

  // Buy Label Modal
  selectedRate: Rate | null;
  setSelectedRate: Dispatch<SetStateAction<Rate | null>>;

  // Order Items
  orderItems: OrderItemWithReason[] | null;

  // Modals state
  setModal: (modalName: string, value: boolean) => void;
  getModalState: (modalName: string) => boolean;
};

const ReturnFormContext = createContext<ReturnFormContextType | null>(null);

interface ReturnFormProviderProps {
  shipment: FindOneShipment;
  children: React.ReactNode;
}

const ReturnFormProvider: React.FC<ReturnFormProviderProps> = ({
  shipment,
  children,
}) => {
  const form = useForm<z.infer<typeof CreateReturnSchema>>({
    resolver: zodResolver(CreateReturnSchema),
    defaultValues: shipmentToReturnForm(shipment),
  });

  const { push } = useRouter();

  // ========= MODAL STATES =========
  const [modals, setModals] = useState({
    customerAddress: false,
    buyLabel: false,
  });
  const setModal = (modalName: string, value: boolean) => {
    setModals(prev => ({ ...prev, [modalName]: value }));
  };
  const getModalState = (modalName: string) =>
    modals[modalName as keyof typeof modals];

  // =========== BUY LABEL ============
  const [selectedRate, setSelectedRate] = useState<Rate | null>(null);
  const [createReturn, { isLoading: creatingReturn }] =
    useCreateReturnMutation();
  const insuranceFee = calculateInsuranceCost(form.getValues().insurance ?? 0);

  const buyLabelAndCreateReturn = async (): Promise<Return | null> => {
    const { rma, toAddressId, epShipmentId } = form.getValues();

    if (!epShipmentId || !selectedRate || !storedSession?.orderItems) {
      console.log("missing required fields");
      return null;
    }

    const payload = {
      rma: rma?.trim() ? rma : undefined,
      orderId: shipment.orderId,
      toAddressId,
      epShipmentId,
      selectedRateId: selectedRate.id,
      orderItems: storedSession.orderItems,
    };
    const newReturn = await createReturn(payload).unwrap();
    sessionStorage.removeItem("pendingReturnItems");
    push(`/shipments/returns/${newReturn.id}`);
    return newReturn;
  };

  // =========== STORED SESSION => RETURN ITEMS ============
  const [storedSession, setStoredSession] = useState<PendingReturnItems | null>(
    null
  );

  useEffect(() => {
    const handleSessionStorage = () => {
      const value = sessionStorage.getItem("pendingReturnItems");
      const parsedValue = JSON.parse(value as string) as PendingReturnItems;

      if (parsedValue?.shipmentId !== shipment.id) {
        if (process.env.NODE_ENV === "development")
          console.log("Shipment ID doesn't match, deleting session storage");
        sessionStorage.removeItem("pendingReturnItems");
        push(`/shipments/${shipment.id}`);
      } else {
        setStoredSession(parsedValue);
      }
    };
    handleSessionStorage();
  }, [shipment.id, push]);

  return (
    <ReturnFormContext.Provider
      value={{
        form,
        shipmentId: shipment.id,
        orderId: shipment.orderId,

        // Buy Label Modal
        selectedRate,
        setSelectedRate,

        // Stored session
        orderItems: storedSession?.orderItems ?? null,

        // Modals state
        setModal,
        getModalState,
      }}
    >
      {/* Pending return items is required to create a return and access this page */}
      {storedSession && children}

      {/* ========= MODALS ========= */}
      <AddressFormModal
        isOpen={modals.customerAddress}
        setOpen={value => setModal("customerAddress", value)}
        values={form.getValues("fromAddress")}
        onSubmit={values => {
          form.setValue("fromAddress", values);
          setModal("customerAddress", false);
        }}
      />
      <BuyLabelModal
        open={getModalState("buyLabel")}
        selectedRate={selectedRate}
        insuranceFee={insuranceFee}
        setOpen={value => setModal("buyLabel", value)}
        onSubmit={buyLabelAndCreateReturn}
        isSubmitting={creatingReturn}
      />
    </ReturnFormContext.Provider>
  );
};

const useReturnForm = () => {
  const context = useContext(ReturnFormContext);
  if (!context)
    throw new Error("useReturnForm must be used within a ReturnFormProvider");
  return context;
};

export { ReturnFormProvider, useReturnForm };
