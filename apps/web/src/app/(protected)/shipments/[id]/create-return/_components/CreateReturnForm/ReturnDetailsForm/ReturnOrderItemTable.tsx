"use client";

import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import Header from "@/components/layout/Header";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Package } from "lucide-react";
import { FieldError, useFormContext } from "react-hook-form";
import { useReturnForm } from "../ReturnFormContext";
import { capitalize } from "@/lib/utils/strings";

const OrderItemTable = () => {
  const form = useFormContext();
  const error = form.formState.errors.items as FieldError | undefined;

  const { orderItems } = useReturnForm();

  return (
    <>
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Header icon={Package} variant="section" as="h3">
              Items
            </Header>
            {error && (
              <p className="text-destructive text-xs">{error.message}</p>
            )}
          </div>
        </div>
        <div className="border rounded-md overflow-hidden">
          <Table variant="dark">
            <TableHeader>
              <TableRow>
                <TableHead className="w-[100px]">SKU</TableHead>
                <TableHead>Name</TableHead>
                <TableHead className="text-right">Price</TableHead>
                <TableHead className="text-center">Return Quantity</TableHead>
                <TableHead>Reason</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {orderItems &&
                orderItems.length > 0 &&
                orderItems.map((item, index) => (
                  <TableRow key={index}>
                    <TableCell>{item.product.sku || "-"}</TableCell>
                    <TableCell>{item.product.name}</TableCell>
                    <TableCell className="text-right">${item.price}</TableCell>
                    <TableCell className="text-center">
                      {item.quantity}
                    </TableCell>
                    <TableCell>{capitalize(item.reason)}</TableCell>
                  </TableRow>
                ))}
            </TableBody>
          </Table>
        </div>

        <FormField
          control={form.control}
          name="notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                Notes{" "}
                <span className="text-xs text-muted-foreground">
                  (optional)
                </span>
              </FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </>
  );
};

export default OrderItemTable;
