import { generateRates } from "@/actions/rates/generateRates";
import ShippingOptionsForm from "@/components/common/forms/ShippingOptionsForm";
import RateList from "@/components/rates/RateList";
import Header from "@/components/layout/Header";
import { Button } from "@/components/ui/button";
import { useConfirm } from "@/hooks/useConfirm";
import { cn } from "@/lib/utils";
import { canGetRates } from "@/lib/utils/rates";
import styles from "@/styles/Dashboard.module.css";
import { CircleDollarSign, RefreshCcw, Scroll } from "lucide-react";
import { useRouter } from "next/navigation";
import { useMemo, useState } from "react";
import { useWatch } from "react-hook-form";
import { toast } from "sonner";
import { useReturnForm } from "./ReturnFormContext";

const CreateReturnActionsCard = () => {
  const [generatingRates, setGeneratingRates] = useState(false); // Loading state for get rates
  const [lastRateFetchData, setLastRateFetchData] = useState<string | null>(
    null
  ); // Store latest data that was used to fetch rates to compare

  const { confirm, ConfirmModal } = useConfirm();
  const { push } = useRouter();

  const {
    form: { control, setValue },
    shipmentId,
    selectedRate,
    setSelectedRate,
    setModal,
  } = useReturnForm();

  const [epShipmentId, fromAddress, toAddressId, parcel] = useWatch({
    name: ["epShipmentId", "fromAddress", "toAddressId", "parcel"],
    control,
  });

  const rateDataValid = useMemo(() => {
    return canGetRates({
      fromAddress,
      toAddress: { id: toAddressId },
      parcel,
    });
  }, [fromAddress, toAddressId, parcel]);

  const dataHasChanged = useMemo(() => {
    if (!lastRateFetchData || !epShipmentId) return false;
    const currentData = {
      fromAddress,
      toAddressId,
      parcel,
    };
    return JSON.stringify(currentData) !== lastRateFetchData;
  }, [fromAddress, toAddressId, parcel, lastRateFetchData, epShipmentId]);

  const getRatesBtnDisabled = epShipmentId
    ? !dataHasChanged || !rateDataValid
    : !rateDataValid;

  const handleGetRates = async () => {
    if (!rateDataValid) {
      toast.error("Data is invalid. Please complete your shipping options.");
      return;
    }

    setGeneratingRates(true);

    try {
      const response = await generateRates(
        fromAddress,
        { id: toAddressId },
        parcel
      );

      if (!response.success || !response.data) throw new Error(response.error);

      setValue("epShipmentId", response.data.epShipmentId);
      setLastRateFetchData(
        JSON.stringify({ fromAddress, toAddressId, parcel })
      );
    } catch (error) {
      if (process.env.NODE_ENV === "development") console.log(error);
      toast.error("Could not generate rates, Please try again");
    } finally {
      setGeneratingRates(false);
    }
  };

  const handleCancelReturn = async () => {
    try {
      const confirmed = await confirm({
        title: "Cancel",
        description:
          "This return is not yet created. Are you sure you want to cancel it?",
        confirmText: "Cancel Return",
        variant: "destructive",
      });

      if (!confirmed) return;
      sessionStorage.removeItem("pendingReturnItems");
      push(`/shipments/${shipmentId}`);
    } catch (error) {
      console.error("Error deleting return order:", error);
      toast.error("Failed to cancel the return order.");
    }
  };

  return (
    <div className={cn(styles.panelContainer, styles.panelYSpacing)}>
      <div className="space-y-4">
        <div className="shrink-0 flex justify-between">
          <Header icon={CircleDollarSign}>Shop Rates</Header>
          <Button
            className="ml-auto"
            size="sm"
            variant="link"
            type="button"
            disabled={getRatesBtnDisabled}
            onClick={handleGetRates}
          >
            <RefreshCcw className={cn(generatingRates && "animate-spin")} />
            {epShipmentId ? "Refresh Rates" : "Get Rates"}
          </Button>
        </div>

        {epShipmentId && (
          <RateList
            epShipmentId={epShipmentId}
            onSelect={rate => setSelectedRate(rate)}
            selectedRate={selectedRate}
          />
        )}
      </div>

      <ShippingOptionsForm />

      <div className="flex justify-between gap-4">
        <Button
          type="button"
          variant="accent"
          className="flex-1"
          onClick={handleCancelReturn}
        >
          Cancel Return
        </Button>
        <Button
          type="button"
          className="flex-1"
          disabled={!selectedRate}
          onClick={() => setModal("buyLabel", true)}
        >
          <Scroll />
          Buy Label
        </Button>
      </div>
      <ConfirmModal />
    </div>
  );
};

export default CreateReturnActionsCard;
