import {
  FormControl,
  FormField,
  Form<PERSON>tem,
  FormLabel,
} from "@/components/ui/form";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import { Input } from "@/components/ui/input";
import { CircleHelp } from "lucide-react";
import { useFormContext } from "react-hook-form";

const RmaInput = () => {
  const form = useFormContext();

  return (
    <div className="min-w-76 xl:min-w-88">
      <FormField
        control={form.control}
        name="rma"
        render={({ field }) => (
          <FormItem className="grid grid-cols-[auto_1fr]">
            <FormLabel className="whitespace-nowrap gap-1">
              RMA
              <HoverCard>
                <HoverCardTrigger>
                  <CircleHelp className="shrink-0 size-3.5 text-muted-foreground/50" />
                </HoverCardTrigger>
                <HoverCardContent className="text-sm">
                  If left blank, RMA. will be auto-generated
                </HoverCardContent>
              </HoverCard>
            </FormLabel>
            <FormControl>
              <Input {...field} placeholder="Optional" className="text-sm" />
            </FormControl>
          </FormItem>
        )}
      />
    </div>
    // </div>
  );
};

export default RmaInput;
