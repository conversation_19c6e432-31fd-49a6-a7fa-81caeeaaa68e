import PageHeader from "@/components/layout/PageHeader";
import { getShipmentById } from "@/data/shipment";
import { cn } from "@/lib/utils";
import styles from "@/styles/Dashboard.module.css";
import { ArrowUturnDownIcon } from "@heroicons/react/24/outline";
import ReturnForm from "./_components/CreateReturnForm";
import { ReturnFormProvider } from "./_components/CreateReturnForm/ReturnFormContext";
import { notFound } from "next/navigation";

type CreateReturnPageProps = {
  params: Promise<{ id: string }>;
};

const CreateReturnPage = async ({ params }: CreateReturnPageProps) => {
  const { id } = await params;

  const shipment = await getShipmentById(id);
  if (!shipment) return notFound();

  return (
    <div className={cn(styles.pagePadding, "relative")}>
      <PageHeader header="Create Return" icon={ArrowUturnDownIcon} />
      <ReturnFormProvider shipment={shipment}>
        <ReturnForm />
      </ReturnFormProvider>
    </div>
  );
};

export default CreateReturnPage;
