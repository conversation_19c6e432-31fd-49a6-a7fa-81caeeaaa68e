"use client";

import { cn } from "@/lib/utils";
import styles from "@/styles/Dashboard.module.css";
import { FormProvider } from "react-hook-form";
import ReturnDetailsForm from "./ReturnDetailsForm";
import { useReturnForm } from "./ReturnFormContext";
import ShippingAndParcelDetailsForm from "./ShippingAndParcelDetailsForm";
import CreateReturnActionsCard from "./CreateReturnActionsCard";

const ReturnForm = () => {
  const { form } = useReturnForm();

  return (
    <FormProvider {...form}>
      <form
        className={cn(
          styles.gridGap,
          "mt-4 lg:mt-6 grid lg:grid-cols-[3fr_2fr] xl:grid-cols-[2fr_1fr] items-start"
        )}
      >
        {/* Left Panels */}
        <div className={cn(styles.gridGap, "flex flex-col overflow-x-hidden")}>
          <ReturnDetailsForm />
          <ShippingAndParcelDetailsForm />
        </div>

        {/* Right Panel */}
        <CreateReturnActionsCard />
      </form>
    </FormProvider>
  );
};

export default ReturnForm;
