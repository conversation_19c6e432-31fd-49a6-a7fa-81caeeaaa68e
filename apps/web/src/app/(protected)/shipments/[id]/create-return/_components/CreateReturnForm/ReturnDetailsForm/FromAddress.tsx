import { getJwtToken } from "@/actions/auth/getToken";
import AddressInfo from "@/components/common/AddressInfo";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  BuildingOffice2Icon,
  ShieldCheckIcon,
} from "@heroicons/react/24/solid";
import { Loader2 } from "lucide-react";
import { useState } from "react";
import { FieldError, useFormContext, useWatch } from "react-hook-form";
import { toast } from "sonner";
import { useReturnForm } from "../ReturnFormContext";
import AddressSchema from "@/schemas/common/AddressSchema";

const FromAddress = () => {
  const [verifying, setVerifying] = useState(false);

  const fromAddress = useWatch({ name: "fromAddress" });
  const { setModal } = useReturnForm();
  const form = useFormContext();
  const error = form.formState.errors.fromAddress as FieldError | undefined;

  const { success: fromAddressIsFilled } = AddressSchema.safeParse(fromAddress);

  const verifyAddress = async () => {
    try {
      const jwtToken = await getJwtToken();
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/addresses/verify`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${jwtToken}`,
          },
          body: JSON.stringify(fromAddress),
        }
      );

      const data = await response.json();

      if (!data.verified) throw new Error("Address is not verified");

      form.setValue("fromAddress.verified", true);
    } catch (error) {
      console.log(error);
      toast.error("Failed to verify address");
    } finally {
      setVerifying(false);
    }
  };

  return (
    <div className="flex flex-col gap-1">
      <label className="text-sm text-muted-foreground font-medium">
        Ship From
      </label>
      <AddressInfo
        name={fromAddress.name}
        company={fromAddress.company}
        street1={fromAddress.street1}
        street2={fromAddress.street2}
        city={fromAddress.city}
        state={fromAddress.state}
        zip={fromAddress.zip}
        country={fromAddress.country}
        phone={fromAddress.phone}
        email={fromAddress.email}
        residential={fromAddress.residential}
      />
      <div className="pt-2 space-y-2">
        <div className="flex flex-wrap items-center gap-2">
          <Button
            size="xs"
            variant="secondary"
            type="button"
            onClick={() => setModal("customerAddress", true)}
          >
            <BuildingOffice2Icon /> Change Address
          </Button>
          {fromAddressIsFilled && !fromAddress.verified && (
            <Button
              type="button"
              size="xs"
              variant="ghost"
              onClick={verifyAddress}
            >
              {verifying ? (
                <Loader2 className="animate-spin" />
              ) : (
                <ShieldCheckIcon />
              )}
              Verify Address
            </Button>
          )}
          {fromAddress.verified && (
            <Badge variant="accent">
              <ShieldCheckIcon />
              Verified
            </Badge>
          )}
        </div>

        {error && (
          <p className="text-destructive text-xs">
            Please specify customer address
          </p>
        )}
      </div>
    </div>
  );
};

export default FromAddress;
