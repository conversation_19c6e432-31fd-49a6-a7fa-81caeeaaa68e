import { cn } from "@/lib/utils";
import styles from "@/styles/Dashboard.module.css";
import { ReceiptText } from "lucide-react";
import Header from "@/components/layout/Header";
import FromAddress from "./FromAddress";
import ReturnOrderItemTable from "./ReturnOrderItemTable";
import RmaInput from "./RmaInput";

const ReturnDetailsForm = () => {
  return (
    <>
      <div className={cn(styles.panelContainer, styles.panelYSpacing)}>
        {/* Return Details */}
        <div className="flex flex-col md:flex-row md:justify-between lg:flex-col xl:flex-row gap-4">
          <div className="space-y-4">
            <Header icon={ReceiptText}>Return Details</Header>
            <FromAddress />
          </div>
          <RmaInput />
        </div>
        {/* Items */}
        <ReturnOrderItemTable />
      </div>
    </>
  );
};

export default ReturnDetailsForm;
