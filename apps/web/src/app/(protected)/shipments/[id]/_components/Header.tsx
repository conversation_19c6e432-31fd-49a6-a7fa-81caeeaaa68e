import SummaryCard from "@/components/common/cards/SummaryCard";
import { Button } from "@/components/ui/button";
import { GetShipmentResponse } from "@/store/types/shipment";
import { ChevronLeft } from "lucide-react";
import Link from "next/link";

type HeaderProps = {
  shipment: GetShipmentResponse;
};

const Header = ({ shipment }: HeaderProps) => {
  return (
    <div className="flex flex-col md:flex-row gap-2 justify-between items-start">
      {/* Left */}
      <div className="flex items-center gap-1">
        <Link href="/shipments">
          <Button size="icon-sm" variant="ghost">
            <ChevronLeft />
          </Button>
        </Link>
        <p className="font-semibold text-lg">
          Shipment - {shipment.shipmentNo}
        </p>
      </div>
      {/* Right  */}
      <SummaryCard
        data={[
          { label: "status", value: shipment.status },
          { label: "Shipment No", value: shipment.shipmentNo },
          { label: "Order No", value: shipment.order.orderNo },
        ]}
      />
    </div>
  );
};

export default Header;
