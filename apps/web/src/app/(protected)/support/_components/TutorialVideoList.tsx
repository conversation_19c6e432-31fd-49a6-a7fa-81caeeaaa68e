import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { cn } from "@/lib/utils";
import PageHeader from "../../../../components/layout/PageHeader";

const MOCK_VIDEOS = [
  {
    title: "How to create an order",
    description: "Sed ut perspiciatis unde omnis iste natus ",
  },
  {
    title: "How to create a label",
    description: "Sed ut perspiciatis unde omnis iste natus ",
  },
  {
    title: "How to issue a return",
    description: "Sed ut perspiciatis unde omnis iste natus ",
  },
  {
    title: "How to delete a batch",
    description: "Sed ut perspiciatis unde omnis iste natus ",
  },
  {
    title: "How to create an order",
    description: "Sed ut perspiciatis unde omnis iste natus ",
  },
];

const TutorialVideoList = () => {
  return (
    <div>
      <Carousel>
        <div className="flex justify-between items-center mb-6">
          <PageHeader header={"Tutorials Videos"} />
          <div className="space-x-2.5">
            <CarouselPrevious className="!static !translate-y-0 border-none bg-accent text-white hover:bg-accent/80" />
            <CarouselNext className="!static !translate-y-0 border-none bg-accent text-white hover:bg-accent/80" />
          </div>
        </div>
        <CarouselContent className="">
          {MOCK_VIDEOS.map((item, index) => (
            <CarouselItem
              key={index}
              className="sm:basis-[calc(100%/1.3)] md:basis-[calc(100%/2.3)] lg:basis-[calc(100%/3.5)]"
            >
              <div className="bg-accent aspect-video rounded-lg" />
              <div className={cn("mt-4", "flex flex-col")}>
                <div className="font-semibold text-foreground">
                  {item.title}
                </div>
                <div className="text-sm text-muted-foreground">
                  {item.description}
                </div>
              </div>
            </CarouselItem>
          ))}
        </CarouselContent>
      </Carousel>
    </div>
  );
};

export default TutorialVideoList;
