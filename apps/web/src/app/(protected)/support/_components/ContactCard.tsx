import PageHeader from "../../../../components/layout/PageHeader";
import { PhoneIcon } from "@heroicons/react/24/solid";
import { Clock3, MailIcon } from "lucide-react";

const ContactCard = () => {
  return (
    <div>
      <PageHeader header={"Contact Us"} />
      <div className="mt-5 bg-secondary rounded-lg px-5 py-6 gap-y-6 gap-x-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-1">
        <div>
          <div className="font-medium text-foreground">Tech Support</div>
          <div className="space-y-2 mt-3">
            <div className="flex items-center gap-3">
              <PhoneIcon className="size-4 text-primary shrink-0" />
              <span className="text-sm text-ring">+1125 5468 8985</span>
            </div>
            <div className="flex items-center gap-3">
              <MailIcon className="size-4 text-primary shrink-0" />
              <span className="text-sm text-ring truncate">
                <EMAIL>
              </span>
            </div>
          </div>
        </div>

        <div>
          <div className="font-medium text-foreground">Billing Support</div>
          <div className="space-y-2 mt-3">
            <div className="flex items-center gap-3">
              <PhoneIcon className="size-4 text-primary shrink-0" />
              <span className="text-sm text-ring">+1125 5468 8986</span>
            </div>
            <div className="flex items-center gap-3">
              <MailIcon className="size-4 text-primary shrink-0" />
              <span className="text-sm text-ring truncate">
                <EMAIL>
              </span>
            </div>
          </div>
        </div>

        <div>
          <div className="font-medium text-foreground">Office Hours</div>
          <div className="space-y-2 mt-3">
            <div className="flex gap-3">
              <Clock3 className="size-4 text-primary shrink-0 mt-0.5" />
              <span className="text-sm text-ring">
                Mon - Fri, 9 AM - 5 PM PST
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContactCard;
