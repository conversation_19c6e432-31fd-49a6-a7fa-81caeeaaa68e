import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import PageHeader from "../../../../components/layout/PageHeader";
import { cn } from "@/lib/utils";

const faqData = [
  {
    question: "How to use batch order?",
    answer:
      "You can create batch orders by uploading your xlsx file or manually create a batch and move orders into that batch. Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi ",
  },
  {
    question: "What is the maximum amount of users I can invite?",
    answer:
      "You can create batch orders by uploading your xlsx file or manually create a batch and move orders into that batch. Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi ",
  },
  {
    question: "Can I void a label after the label as been scanned?",
    answer:
      "You can create batch orders by uploading your xlsx file or manually create a batch and move orders into that batch. Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi ",
  },
  {
    question: "What happens when my wallet balance runs out?",
    answer:
      "You can create batch orders by uploading your xlsx file or manually create a batch and move orders into that batch. Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi ",
  },
  {
    question: "How can I streamline my order management?",
    answer:
      "You can create batch orders by uploading your xlsx file or manually create a batch and move orders into that batch. Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi ",
  },
];

const FaqSection = () => {
  return (
    <div>
      <PageHeader header={"Frequently asked questions"} />
      <Accordion type="single" collapsible className="w-full mt-5">
        {faqData.map((item, index) => (
          <AccordionItem
            key={index}
            value={`item-${index + 1}`}
            className={cn(faqData.length === index + 1 && "border-none")}
          >
            <AccordionTrigger
              className={cn(
                "bg-muted px-4 text-foreground text-base",
                index === 0 && "rounded-t-lg",
                faqData.length === index + 1 && "rounded-b-lg"
              )}
            >
              {item.question}
            </AccordionTrigger>
            <AccordionContent className="p-4 text-ring text-sm">
              {item.answer}
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    </div>
  );
};

export default FaqSection;
