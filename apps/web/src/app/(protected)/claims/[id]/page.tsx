"use client";

import { cn } from "@/lib/utils";
import { useGetClaimQuery } from "@/store/api/claimApi";
import styles from "@/styles/Dashboard.module.css";
import { notFound, useParams } from "next/navigation";
import ClaimDetailsCard from "./_components/ClaimDetailsCard";
import ClaimMetadata from "./_components/ClaimMetadataCard";
import Header from "./_components/Header";
import Loading from "./loading";

const ClaimDetailPage = () => {
  const params = useParams();
  const id = params.id as string;

  const { data: claim, isLoading: claimLoading } = useGetClaimQuery(id);

  if (claimLoading) return <Loading />;

  if (!claim) return notFound();

  return (
    <div className={cn(styles.pagePadding, "relative")}>
      <Header claim={claim} />
      <main
        className={cn(
          styles.gridGap,
          "mt-4 lg:mt-6 grid grid-cols-1 lg:grid-cols-[3fr_2fr] xl:grid-cols-[2fr_1fr] items-start"
        )}
      >
        {/* Left Panels */}
        <div className={cn(styles.gridGap, "flex flex-col overflow-hidden")}>
          <ClaimDetailsCard claim={claim} />
        </div>

        {/* Right Panel */}
        <ClaimMetadata
          user={claim.user}
          shipmentNo={claim.shipmentId ? claim?.shipment.shipmentNo : null}
          rma={claim.returnId ? claim?.return.rma : null}
          reference={claim.reference}
          claimId={claim.id}
          status={claim.status}
        />
      </main>
    </div>
  );
};

export default ClaimDetailPage;
