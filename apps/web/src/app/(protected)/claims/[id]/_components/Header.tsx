import { Button } from "@/components/ui/button";
import { FindOneClaim } from "@/types/Claim/Claim";
import { ChevronLeft } from "lucide-react";
import Link from "next/link";

type HeaderProps = {
  claim: FindOneClaim;
};

const Header = ({ claim }: HeaderProps) => {
  return (
    <div className="gap-2">
      <div className="flex items-center gap-1">
        <Link href="/claims">
          <Button size="icon-sm" variant="ghost">
            <ChevronLeft />
          </Button>
        </Link>
        <p className="font-semibold text-lg">Claim - {claim.claimNo}</p>
      </div>
    </div>
  );
};

export default Header;
