import Status from "@/components/common/Status";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { useConfirm } from "@/hooks/useConfirm";
import { cn } from "@/lib/utils";
import { getInitials } from "@/lib/utils/user";
import { useCancelClaimMutation } from "@/store/api/claimApi";
import styles from "@/styles/Dashboard.module.css";
import { User } from "@/types/User/User";
import { ClaimStatus } from "@repo/database";
import { format } from "date-fns";
import { useCallback } from "react";
import { toast } from "sonner";

type ClaimMetadataCardProps = {
  user: User;
  shipmentNo: string | null;
  rma: string | null;
  reference: string | null;
  claimId: string;
  status: ClaimStatus;
};

const ClaimMetadataCard = ({
  user,
  shipmentNo,
  rma,
  reference,
  claimId,
  status,
}: ClaimMetadataCardProps) => {
  const [cancelClaim] = useCancelClaimMutation();
  const { confirm, ConfirmModal } = useConfirm();

  const handleCancel = useCallback(
    async (id: string) => {
      try {
        const confirmed = await confirm({
          title: "Cancel",
          description: "Are you sure you want to cancel claim?",
          confirmText: "Confirm",
          variant: "destructive",
        });

        if (!confirmed) return;

        await cancelClaim(id).unwrap();
        toast.success("Claim cancelled successfully");
      } catch (error) {
        console.error("Failed to cancel claim:", error);
        toast.error("Failed to cancel claim");
      }
    },
    [cancelClaim, confirm]
  );

  return (
    <div className={cn(styles.panelContainer, styles.panelYSpacing)}>
      <div className="space-y-4">
        <div className="flex space-x-4">
          <Avatar className="size-9">
            <AvatarImage src={user.image || undefined} />
            <AvatarFallback>
              {getInitials(`${user.firstName} ${user.lastName}`)}
            </AvatarFallback>
          </Avatar>
          <div>
            <div className="text-sm text-accent">
              Claimed by{" "}
              <span className="font-medium">
                {user.firstName} {user.lastName}
              </span>
            </div>
            <div className="text-xs text-muted-foreground">
              {format(user.createdAt, "MM/d/yyyy - hh:mm")}
            </div>
          </div>
        </div>
        <div>
          <div className="text-xs font-semibold text-muted-foreground">
            {!!shipmentNo ? "Shipment No" : "RMA"}
          </div>
          <div className="text-accent">{!!shipmentNo ? shipmentNo : rma}</div>
        </div>
        <div>
          <div className="text-xs font-semibold text-muted-foreground">
            Reference
          </div>
          <div className="text-accent">{reference ? reference : "-"}</div>
        </div>
        <div>
          <div className="text-xs font-semibold text-muted-foreground">
            Status
          </div>
          <div className="text-accent">
            <Status text={status} />
          </div>
        </div>
      </div>

      {status === ClaimStatus.submitted && (
        <div className="flex justify-end">
          <Button
            onClick={() => handleCancel(claimId)}
            variant="destructive"
            size="lg"
          >
            Cancel Claim
          </Button>
        </div>
      )}

      {/* Modal */}
      <ConfirmModal />
    </div>
  );
};

export default ClaimMetadataCard;
