import { cn } from "@/lib/utils";
import { ClaimAttachmentType } from "@repo/database";
import { File, LucideIcon, Receipt } from "lucide-react";
import Link from "next/link";

type AttachmentFileProps = {
  fileName: string | null;
  type: ClaimAttachmentType;
  className?: string;
  fileUrl: string;
};

const typeIconMap: Record<
  ClaimAttachmentType,
  { icon: LucideIcon; colorClass: string }
> = {
  // Claim Attachment Types
  invoice: { icon: Receipt, colorClass: "bg-secondary text-accent" },
  supporting_documents: { icon: File, colorClass: "bg-accent text-white" },
  email_evidence: { icon: File, colorClass: "bg-accent text-white" },
};

const AttachmentFile = ({
  fileName,
  type,
  className,
  fileUrl,
}: AttachmentFileProps) => {
  const { icon: IconComponent, colorClass } = typeIconMap[type];

  return (
    <Link
      href={fileUrl}
      target="_blank"
      className="flex space-x-3 hover:bg-muted transition-colors duration-200 p-2 rounded-md"
    >
      <div
        className={cn(
          className,
          colorClass,
          "p-2.5 rounded-full flex justify-center items-center size-10"
        )}
      >
        <IconComponent className="size-5" />
      </div>
      <div className="space-y-0.5">
        <div className="text-sm font-medium">{fileName ?? "No file name"}</div>
        <div className="text-xs font-medium text-accent px-2 py-0.5 bg-muted rounded-lg w-fit capitalize">
          {type.replaceAll("_", " ")}
        </div>
      </div>
    </Link>
  );
};

export default AttachmentFile;
