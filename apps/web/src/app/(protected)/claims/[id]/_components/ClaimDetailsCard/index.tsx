import ClaimIcon from "@/components/icons/ClaimIcon";
import Header from "@/components/layout/Header";
import { ScrollA<PERSON>, ScrollBar } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";
import { truncateFileName } from "@/lib/utils/fileUtils";
import { capitalize } from "@/lib/utils/strings";
import styles from "@/styles/Dashboard.module.css";
import { FindOneClaim } from "@/types/Claim/Claim";
import { Clock } from "lucide-react";
import AttachmentFile from "./AttachmentFile";
import StatusHistory from "./StatusHistory";

type ClaimDetailsCardProps = {
  claim: FindOneClaim;
};

const ClaimDetailsCard = ({ claim }: ClaimDetailsCardProps) => {
  return (
    <div className={cn(styles.panelContainer, styles.panelYSpacing)}>
      <div className="flex flex-col md:flex-row md:justify-between gap-4">
        <div className="space-y-4 w-full">
          <Header icon={ClaimIcon}>Claim Detail</Header>

          <div className="grid grid-cols-2 gap-4 mb-3">
            <div className="text-muted-foreground text-sm">
              <div className="font-medium">Tracking No.</div>
              <div>{claim.trackingCode}</div>
            </div>

            <div className="text-muted-foreground text-sm">
              <div className="font-medium">Type</div>
              <div>{capitalize(claim.type)}</div>
            </div>
          </div>

          <div className="text-muted-foreground text-sm">
            <div className="font-medium">Description</div>
            <div>{claim.description}</div>
          </div>

          <div className="grid md:grid-cols-3 grid-cols-1 gap-3 mb-9">
            <StatCard label="Insurance Amount" amount={claim.insuranceAmount} />
            <StatCard label="Requested Amount" amount={claim.requestAmount} />
            <StatCard label="Approved Amount" amount={claim.approvedAmount} />
          </div>

          <div className="mb-9">
            <div className="text-accent font-semibold text-sm">Attachments</div>
            <ScrollArea className="py-2 md:py-4">
              <div className="flex gap-2 max-w-full overflow-x-auto">
                {claim.attachments.map(attachment => {
                  const fullName = attachment.fileName ?? "";
                  const [namePart, ext] = fullName.split(".");
                  const displayName = truncateFileName(namePart);

                  return (
                    <AttachmentFile
                      key={attachment.id}
                      fileName={`${displayName}.${ext}`}
                      type={attachment.type}
                      fileUrl={attachment.fileUrl}
                    />
                  );
                })}
              </div>
              <ScrollBar orientation="horizontal" />
            </ScrollArea>
          </div>

          <div className="space-y-4">
            <Header icon={Clock}>Status History</Header>
            <StatusHistory claimId={claim.id} />
          </div>
        </div>
      </div>
    </div>
  );
};

const StatCard = ({
  label,
  amount,
}: {
  label: string;
  amount: string | null;
}) => {
  return (
    <div className="bg-muted rounded-lg py-3 px-5">
      <div className="text-sm text-muted-foreground">{label}</div>
      <div className="font-semibold text-accent">
        {amount ? `$ ${amount}` : "-"}
      </div>
    </div>
  );
};

export default ClaimDetailsCard;
