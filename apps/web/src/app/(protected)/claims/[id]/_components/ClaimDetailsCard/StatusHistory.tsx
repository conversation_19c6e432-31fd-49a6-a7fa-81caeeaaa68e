import { useGetStatusHistoryQuery } from "@/store/api/claimApi";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import { format } from "date-fns";

type StatusHistoryProps = {
  claimId: string;
};

const StatusHistory = ({ claimId }: StatusHistoryProps) => {
  const { data: statusHistory } = useGetStatusHistoryQuery(claimId);

  return (
    <div className="rounded-md overflow-hidden">
      {statusHistory ? (
        <Table variant="dark">
          <TableHeader>
            <TableRow>
              <TableHead>Date/Time</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Detail</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {statusHistory?.map((item, index) => {
              return (
                <TableRow key={index}>
                  <TableCell>
                    {format(item.timestamp, "MM/d/yyyy - hh:mm")} GMT
                  </TableCell>
                  <TableCell className="capitalize">
                    {item.status.split("_").join("")}
                  </TableCell>
                  <TableCell>{item.status_detail}</TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      ) : (
        <Skeleton className="w-full h-[120px]" />
      )}
    </div>
  );
};

export default StatusHistory;
