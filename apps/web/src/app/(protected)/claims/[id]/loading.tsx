"use client";

import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import styles from "@/styles/Dashboard.module.css";

export default function Loading() {
  return (
    <div className={cn(styles.pagePadding, "relative")}>
      <div className="flex flex-col md:flex-row gap-2 justify-between items-start">
        <Skeleton className="w-[292px] h-8" />
      </div>
      <div
        className={cn(
          styles.gridGap,
          "mt-4 lg:mt-6 grid lg:grid-cols-[2fr_1fr] grid-cols-1 items-start"
        )}
      >
        {/* skeleton card left */}
        <div className={cn(styles.gridGap, "flex flex-col")}>
          <Skeleton className="w-full h-[387px] md:h-[570px]" />
        </div>

        {/* skeleton card right */}
        <Skeleton className="w-full h-[262px]" />
      </div>
    </div>
  );
}
