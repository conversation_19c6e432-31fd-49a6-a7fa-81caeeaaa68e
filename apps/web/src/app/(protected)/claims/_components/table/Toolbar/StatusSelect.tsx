import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { claimStatusColor } from "@/lib/mappings/statusColor";
import { capitalize } from "@/lib/utils/strings";
import { <PERSON>laim, ClaimStatus } from "@repo/database";
import { Table } from "@tanstack/react-table";

type StatusFilterValue = ClaimStatus | "ALL";

const statusOptions = [
  { value: "ALL", label: "All" },
  ...Object.values(ClaimStatus).map(value => ({
    value,
    label: capitalize(value),
  })),
];

type StatusSelectProps = {
  // table: Table<GetClaimsResponse["data"][number]>;
  table: Table<Claim>;
};

const StatusSelect = ({ table }: StatusSelectProps) => {
  const statusFilterValue =
    (table.getColumn("status")?.getFilterValue() as StatusFilterValue) ?? "ALL";

  const handleStatusSelect = (value: StatusFilterValue) =>
    table
      .getColumn("status")
      ?.setFilterValue(value === "ALL" ? undefined : value);

  return (
    <Select value={statusFilterValue} onValueChange={handleStatusSelect}>
      <SelectTrigger className="w-[140px]">
        <SelectValue />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          <SelectLabel>Status</SelectLabel>
          {statusOptions.map(status => (
            <SelectItem
              key={status.value}
              value={status.value}
              className={
                claimStatusColor[
                  status.value as keyof typeof claimStatusColor
                ] ?? ""
              }
            >
              {status.label}
            </SelectItem>
          ))}
        </SelectGroup>
      </SelectContent>
    </Select>
  );
};

export default StatusSelect;
