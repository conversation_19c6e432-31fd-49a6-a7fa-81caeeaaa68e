import QuickSearch from "@/components/common/table/toolbar/QuickSearch";
import ViewOptions from "@/components/common/table/toolbar/ViewOptions";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Separator } from "@/components/ui/separator";
import { Table } from "@tanstack/react-table";
import { ChevronDown, EllipsisVertical, Trash2 } from "lucide-react";
import { toast } from "sonner";
import AdvancedSearch from "./AdvancedSearch";
import StatusSelect from "./StatusSelect";
import { Claim } from "@repo/database";

type ToolbarProps = {
  // table: Table<GetClaimsResponse["data"][number]>;
  table: Table<Claim>;
  setSearch: React.Dispatch<React.SetStateAction<string>>;
  // filterColumn?: string;
  // filterPlaceholder?: string;
};

const Toolbar = ({ table, setSearch }: ToolbarProps) => {
  const noOfSelectedRows = table.getFilteredSelectedRowModel().rows.length;

  const handleCancel = async () => {
    const selectedRows = table.getFilteredSelectedRowModel().rows;
    const orderIds = selectedRows.map(row => row.original.id);
    if (orderIds.length === 0) return;

    try {
      toast.success(`Cancelled Claim`);
    } catch (error) {
      console.log(error);
      toast.error("Failed to cancel orders");
    }
  };

  return (
    <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
      {/* LEFT */}
      <div className="flex items-center gap-2 h-8">
        {noOfSelectedRows > 0 ? (
          <>
            <span className="text-sm font-semibold">
              {noOfSelectedRows} selected
            </span>
            <Separator orientation="vertical" className="mx-2" />
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="muted">
                  <EllipsisVertical /> Actions
                  <ChevronDown />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56">
                <DropdownMenuGroup>
                  <DropdownMenuItem
                    variant="destructive"
                    onClick={handleCancel}
                  >
                    <Trash2 />
                    Cancel Claim
                  </DropdownMenuItem>
                </DropdownMenuGroup>
              </DropdownMenuContent>
            </DropdownMenu>
          </>
        ) : (
          <QuickSearch setSearch={setSearch} />
        )}
      </div>
      {/* RIGHT */}

      <div className="flex items-center gap-2 h-8">
        <AdvancedSearch table={table} />
        <ViewOptions table={table} />
        <StatusSelect table={table} />
      </div>
    </div>
  );
};

export default Toolbar;
