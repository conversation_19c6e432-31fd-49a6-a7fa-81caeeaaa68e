import Status from "@/components/common/Status";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { GetClaimsResponse } from "@/store/types/claim";
import { Claim } from "@repo/database";
import { ColumnDef } from "@tanstack/react-table";
import { EllipsisVertical, Eye, X } from "lucide-react";
import Link from "next/link";

export const createClaimsColumns = (): ColumnDef<
  GetClaimsResponse["data"][number]
>[] => [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={value => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={value => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "claimNo",
    header: "Claim No.",
    cell: ({ row }) => {
      const { id, claimNo } = row.original;
      return (
        <Link
          href={`/claims/${id}`}
          className="hover:underline hover:text-primary"
        >
          {claimNo}
        </Link>
      );
    },
  },
  {
    accessorKey: "reference",
    header: "Ref",
    cell: ({ row }) => {
      const { reference } = row.original;
      return <div className="text-center">{reference ? reference : "-"}</div>;
    },
  },
  {
    accessorKey: "trackingCode",
    header: "Tracking No",
  },
  {
    accessorKey: "description",
    header: "Description",
  },
  {
    accessorKey: "requestAmount",
    header: () => {
      return <div className="text-right">Request Amount</div>;
    },
    cell: ({ row }) => {
      const { requestAmount } = row.original;
      return (
        <div className="text-right">
          {requestAmount ? `$ ${requestAmount}` : "-"}
        </div>
      );
    },
  },
  {
    accessorKey: "insuranceAmount",
    header: () => {
      return <div className="text-right">Insurance Amount</div>;
    },
    cell: ({ row }) => {
      const { insuranceAmount } = row.original;
      return (
        <div className="text-right">
          {insuranceAmount ? `$ ${insuranceAmount}` : "-"}
        </div>
      );
    },
  },
  {
    accessorKey: "approvedAmount",
    header: () => {
      return <div className="text-right">Approved Amount</div>;
    },
    cell: ({ row }) => {
      const { approvedAmount } = row.original;
      return (
        <div className="text-right">
          {approvedAmount ? `$ ${approvedAmount}` : "-"}
        </div>
      );
    },
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const { status } = row.original;
      return <Status text={status} />;
    },
  },
  {
    accessorKey: "claimed",
    header: "Claimed by",
    cell: ({ row }) => {
      const { user } = row.original;
      return <div>{user.firstName}</div>;
    },
  },
];

type createClaimsActionsProps = {
  handleCancel: (id: string) => void;
};

export const createClaimsActions = ({
  handleCancel,
}: createClaimsActionsProps): ColumnDef<Claim>[] => [
  {
    id: "actions",
    cell: ({ row }) => {
      const claim = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon-xs">
              <span className="sr-only">Open menu</span>
              <EllipsisVertical />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem asChild>
              <Link href={`/claims/${claim.id}`}>
                <Eye />
                View
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem
              variant="destructive"
              onClick={() => handleCancel(claim.id)}
            >
              <X />
              Cancel
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];
