import Header from "@/components/layout/Header";
import PopoverFooter from "@/components/layout/PopoverFooter";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverClose,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { capitalize } from "@/lib/utils/strings";
import AdvanceSearchSchema, {
  defaultValues,
} from "@/schemas/claim/AdvancedSearchSchema";
import styles from "@/styles/Dashboard.module.css";
import { zodResolver } from "@hookform/resolvers/zod";
import { Claim, ClaimType } from "@repo/database";
import { Table } from "@tanstack/react-table";
import { Settings2, X } from "lucide-react";
import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

function removeEmptyStrings(values: Record<string, string | object>) {
  const result: Record<string, unknown> = {};

  Object.entries(values).forEach(([key, value]) => {
    if (typeof value === "object" && value !== null) {
      // Filter nested object
      const filteredNested = Object.entries(value).reduce(
        (acc, [k, v]) => {
          if (v !== "" && v !== undefined && v !== null) {
            acc[k] = v;
          }
          return acc;
        },
        {} as Record<string, unknown>
      );

      if (Object.keys(filteredNested).length > 0) {
        result[key] = filteredNested;
      }
    } else if (value !== "" && value !== undefined && value !== null) {
      result[key] = value;
    }
  });
  return result;
}

interface AdvancedSearchProps {
  // table: Table<GetClaimsResponse["data"][number]>;
  table: Table<Claim>;
}

const AdvancedSearch = ({ table }: AdvancedSearchProps) => {
  const [open, setOpen] = useState(false);

  const columnFilters = table.getState().columnFilters;
  const currentFilterLength = table
    .getState()
    .columnFilters.filter(filter => filter.id !== "status").length;
  const fieldNames = Object.keys(AdvanceSearchSchema.shape);

  const typeOptions = [
    ...Object.values(ClaimType).map(value => ({
      value,
      label: capitalize(value),
    })),
  ];

  const form = useForm({
    resolver: zodResolver(AdvanceSearchSchema),
    defaultValues,
  });

  const clearAllFilters = () => {
    const clearedFilters = columnFilters.filter(
      ({ id }) => !fieldNames.includes(id)
    );

    table.setColumnFilters(clearedFilters);
    form.reset();
    setOpen(false);
  };

  const onSubmit = (values: z.infer<typeof AdvanceSearchSchema>) => {
    const filteredValues = removeEmptyStrings(values);

    const cleanedFilters = columnFilters.filter(
      ({ id }) => !fieldNames.includes(id)
    );
    const filterArray = Object.entries(filteredValues).map(([id, value]) => ({
      id,
      value,
    }));
    table.setColumnFilters([...cleanedFilters, ...filterArray]);
    setOpen(false);
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <div className="relative">
        <PopoverTrigger asChild>
          <Button
            variant={currentFilterLength > 0 ? "accent" : "secondary"}
            size="icon-sm"
          >
            <Settings2 />
          </Button>
        </PopoverTrigger>
        {currentFilterLength > 0 && (
          <div className="absolute right-0 translate-x-1/3 top-0 -translate-y-1/3 size-4.5 rounded-full bg-destructive grid place-content-center">
            <div className="text-[10px] font-semibold text-white">
              {currentFilterLength}
            </div>
          </div>
        )}
      </div>
      <PopoverContent
        className="w-full lg:min-w-lg xl:min-w-xl space-y-4"
        align="end"
      >
        <PopoverClose asChild>
          <Button
            variant="ghost"
            size="icon"
            className="absolute right-2 top-2"
          >
            <X strokeWidth={1.5} className="size-6!" />
          </Button>
        </PopoverClose>

        <Header icon={Settings2} as="h4" variant="section">
          Advanced Search
        </Header>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className={styles.formGrid}>
              <FormField
                control={form.control}
                name="trackingCode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tracking Code</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="reference"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Reference</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Type</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger className="w-full">
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {typeOptions?.map(item => (
                          <SelectItem key={item.value} value={item.value}>
                            {capitalize(item.label)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <PopoverFooter
              submitLabel="Search"
              onSubmitClick={form.handleSubmit(onSubmit)}
              destructiveLabel="Clear All"
              onDestructiveClick={clearAllFilters}
            />
          </form>
        </Form>
      </PopoverContent>
    </Popover>
  );
};

const MemoizedAdvancedSearch = React.memo(AdvancedSearch);

MemoizedAdvancedSearch.displayName = "AdvancedSearch";

export default MemoizedAdvancedSearch;
