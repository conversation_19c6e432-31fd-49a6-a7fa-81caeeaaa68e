import { cn } from "@/lib/utils";
import styles from "@/styles/Dashboard.module.css";
import { ClipboardDocumentListIcon } from "@heroicons/react/24/outline";
import PageHeader from "../../../../components/layout/PageHeader";
import OrderForm from "../_components/orderForm";
import { OrderFormProvider } from "../_components/orderForm/OrderFormContext";

const CreateOrderPage = () => {
  return (
    <div className={cn(styles.pagePadding, "relative")}>
      <PageHeader header="Create Order" icon={ClipboardDocumentListIcon} />
      <OrderFormProvider>
        <OrderForm />
      </OrderFormProvider>
    </div>
  );
};

export default CreateOrderPage;
