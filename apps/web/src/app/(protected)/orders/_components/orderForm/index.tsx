"use client";

import ShippingAndParcelDetailForm from "@/components/common/forms/ShippingAndParcelDetailForm";
import { cn } from "@/lib/utils";
import { createOrderSchema } from "@/schemas/order/createOrderSchema";
import {
  useCreateOrderMutation,
  useUpdateOrderMutation,
} from "@/store/api/orderApi";
import styles from "@/styles/Dashboard.module.css";
import { useRouter } from "next/navigation";
import { FormProvider } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";
import CreateOrderActionsCard from "./CreateOrderActionsCard";
import OrderDetailsForm from "./OrderDetailsForm";
import { useOrderForm } from "./OrderFormContext";
import UpdateOrderActionsCard from "./UpdateOrderActionsCard";

const OrderForm = () => {
  const { id, form } = useOrderForm();

  const router = useRouter();

  const [createOrder, { isLoading: creatingOrder }] = useCreateOrderMutation();
  const [updateOrder, { isLoading: updatingOrder }] = useUpdateOrderMutation();

  const onSubmit = async (values: z.infer<typeof createOrderSchema>) => {
    try {
      if (id) await updateOrder({ id, data: values }).unwrap();
      else {
        const response = await createOrder(values).unwrap();
        toast.success(`Order created successfully`);
        return router.push(`/orders/${response.id}`);
      }
      toast.success(`Order ${id ? "updated" : "created"} successfully`);
      router.push(`/orders/${id}`);
    } catch (error) {
      console.log(error);
      if (error && typeof error === "object" && "status" in error) {
        if (error.status === 400) toast.error("Invalid request");
        else {
          toast.error("Failed to save order");
        }
      } else {
        console.error(error);
        toast.error("Unexpected error. Please contact support.");
      }
    }
  };

  return (
    <>
      <FormProvider {...form}>
        <form
          className={cn(
            styles.gridGap,
            "mt-4 lg:mt-6 grid lg:grid-cols-[3fr_2fr] xl:grid-cols-[2fr_1fr] items-start"
          )}
          onSubmit={form.handleSubmit(onSubmit)}
        >
          {/* Left Panels */}
          <div
            className={cn(styles.gridGap, "flex flex-col overflow-x-hidden")}
          >
            <OrderDetailsForm />
            <ShippingAndParcelDetailForm />
          </div>

          {/* Right Panel */}
          {!id ? (
            <CreateOrderActionsCard isSubmitting={creatingOrder} />
          ) : (
            <UpdateOrderActionsCard isSubmitting={updatingOrder} />
          )}
        </form>
      </FormProvider>
    </>
  );
};

export default OrderForm;
