import { But<PERSON> } from "@/components/ui/button";
import Header from "@/components/layout/Header";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { CirclePlus, Package, Pencil, Trash } from "lucide-react";
import { FieldError, useFormContext } from "react-hook-form";
import { useOrderForm } from "../OrderFormContext";
import SplitOrderItems from "../SplitOrderItems";

const OrderItemTable = () => {
  const form = useFormContext();
  const error = form.formState.errors.orderItems as FieldError | undefined;

  const {
    id,
    orderItems,
    removeOrderItem,
    setModal,
    setSelectedOrderItemIndex,
  } = useOrderForm();

  const handleAddItem = () => {
    setModal("orderItem", true);
  };

  const handleEditItem = (index: number) => {
    setSelectedOrderItemIndex(index);
    setModal("orderItem", true);
  };

  return (
    <>
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Header icon={Package} variant="section" as="h3">
              Items
            </Header>
            {error && (
              <p className="text-destructive text-xs">{error.message}</p>
            )}
          </div>
          {id && <SplitOrderItems orderItems={orderItems} />}
        </div>
        <div className="border rounded-md overflow-hidden">
          <Table variant="dark">
            <TableHeader>
              <TableRow>
                <TableHead className="w-[100px]">SKU</TableHead>
                <TableHead>Name</TableHead>
                <TableHead className="text-right">Quantity</TableHead>
                <TableHead className="text-right">Price (USD)</TableHead>
                <TableHead></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {orderItems.length > 0 ? (
                orderItems.map((item, index) => (
                  <TableRow key={item.id}>
                    <TableCell>{item.product.sku || "-"}</TableCell>
                    <TableCell>{item.product.name}</TableCell>
                    <TableCell className="text-right">
                      {item.quantity}
                    </TableCell>
                    <TableCell className="text-right">
                      ${item.price.toFixed(2)}
                    </TableCell>
                    <TableCell className="text-right flex items-center justify-end gap-1.5">
                      <Button
                        type="button"
                        size="xs"
                        variant="ghost"
                        className="bg-background"
                        onClick={() => handleEditItem(index)}
                      >
                        <Pencil />
                        Edit
                      </Button>
                      <Button
                        type="button"
                        size="xs"
                        variant="ghost-destructive"
                        className="bg-background"
                        onClick={() => removeOrderItem(index)}
                      >
                        <Trash /> Delete
                      </Button>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={5}
                    className="text-center text-sm text-muted-foreground py-5"
                  >
                    <Button
                      type="button"
                      size="xs"
                      variant="secondary"
                      onClick={handleAddItem}
                    >
                      <CirclePlus />
                      Add item
                    </Button>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
        {orderItems.length > 0 && (
          <Button
            type="button"
            size="xs"
            variant="secondary"
            onClick={handleAddItem}
          >
            <CirclePlus /> Add Item
          </Button>
        )}
        <FormField
          control={form.control}
          name="notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                Notes{" "}
                <span className="text-xs text-muted-foreground">
                  (optional)
                </span>
              </FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </>
  );
};

export default OrderItemTable;
