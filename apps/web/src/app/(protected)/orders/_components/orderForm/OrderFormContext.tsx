"use client";

import AddressFormModal from "@/components/common/forms/AddressFormModal";
import AddOrderItemFormModal from "@/components/product/AddOrderItemFormModal";
import BuyLabelModal from "@/components/rates/BuyLabelModal";
import { calculateInsuranceCost } from "@/lib/utils/insurance";
import { orderToOrderForm } from "@/lib/utils/order";
import {
  createOrderSchema,
  getDefaultValues,
} from "@/schemas/order/createOrderSchema";
import { useBuyLabelMutation } from "@/store/api/orderApi";
import { Order } from "@/types/Order/Order";
import { Shipment } from "@/types/Shipment/Shipment";
import { zodResolver } from "@hookform/resolvers/zod";
import { Rate } from "@repo/easypost-types";
import {
  createContext,
  Dispatch,
  SetStateAction,
  useContext,
  useState,
} from "react";
import {
  FieldArrayWithId,
  useFieldArray,
  useForm,
  UseFormReturn,
} from "react-hook-form";
import { z } from "zod";

type OrderFormContextType = {
  form: UseFormReturn<z.infer<typeof createOrderSchema>>;
  id?: string; // For update order

  // Order Items Modal
  orderItems: FieldArrayWithId<
    z.infer<typeof createOrderSchema>,
    "orderItems",
    "id"
  >[];
  removeOrderItem: (index: number) => void;
  setSelectedOrderItemIndex: (index: number) => void;

  // Buy Label
  selectedRate: Rate | null;
  setSelectedRate: Dispatch<SetStateAction<Rate | null>>;

  // Split Shipment
  isSplit: boolean;
  parentOrderId?: string;

  // Modals state
  setModal: (modalName: string, value: boolean) => void;
  getModalState: (modalName: string) => boolean;
};

const OrderFormContext = createContext<OrderFormContextType | null>(null);

const OrderFormProvider = ({
  order,
  children,
}: {
  order?: Order | undefined;
  children: React.ReactNode;
}) => {
  // ========= REACT HOOK FORM SETUP ==========
  const form = useForm<z.infer<typeof createOrderSchema>>({
    resolver: zodResolver(createOrderSchema),
    defaultValues: order ? orderToOrderForm(order) : getDefaultValues(),
  });

  // ========= ORDER ITEMS MANAGEMENT =========
  const {
    fields: orderItems,
    append,
    update: updateOrderItem,
    remove: removeOrderItem,
  } = useFieldArray({
    control: form.control,
    name: "orderItems",
  });

  const [selectedOrderItemIndex, setSelectedOrderItemIndex] = useState<
    number | null
  >(null);

  // ========= MODAL STATES =========
  const [modals, setModals] = useState({
    customerAddress: false,
    orderItem: false,
    splitOrder: false,
    buyLabel: false,
  });
  const setModal = (modalName: string, value: boolean) => {
    setModals(prev => ({ ...prev, [modalName]: value }));
  };
  const getModalState = (modalName: string) =>
    modals[modalName as keyof typeof modals];

  // =========== BUY LABEL ============
  const [selectedRate, setSelectedRate] = useState<Rate | null>(null);
  const [buyLabel, { isLoading: isBuyingLabel }] = useBuyLabelMutation();
  const insuranceFee = calculateInsuranceCost(form.getValues().insurance ?? 0);

  const buyLabelAndCreateShipment = async (): Promise<Shipment | null> => {
    const { epShipmentId, insurance } = form.getValues();

    if (!epShipmentId || !order?.id || !selectedRate) {
      console.log("missing required fields");
      return null;
    }

    const payload = {
      orderId: order.id,
      rateId: selectedRate.id,
      epShipmentId,
      insuranceAmount: insurance,
    };
    const result = await buyLabel(payload).unwrap();

    return result.shipment;
  };

  return (
    <OrderFormContext.Provider
      value={{
        form,
        id: order?.id,

        // Order Items
        orderItems,
        removeOrderItem,
        setSelectedOrderItemIndex,

        // Buy Label
        selectedRate,
        setSelectedRate,

        // Split Order Items
        isSplit: order?.isSplit ?? false,
        parentOrderId: order?.parentOrderId ?? undefined,

        // Modals state
        setModal,
        getModalState,
      }}
    >
      {children}

      {/* ========= MODALS ========= */}
      <BuyLabelModal
        open={getModalState("buyLabel")}
        selectedRate={selectedRate}
        insuranceFee={insuranceFee}
        setOpen={value => setModal("buyLabel", value)}
        onSubmit={buyLabelAndCreateShipment}
        isSubmitting={isBuyingLabel}
      />
      <AddressFormModal
        isOpen={modals.customerAddress}
        setOpen={value => setModal("customerAddress", value)}
        values={form.getValues("toAddress")}
        onSubmit={values => {
          form.setValue("toAddress", values);
          setModal("customerAddress", false);
        }}
      />
      <AddOrderItemFormModal
        isOpen={modals.orderItem}
        setOpen={value => setModal("orderItem", value)}
        values={
          selectedOrderItemIndex !== null
            ? orderItems[selectedOrderItemIndex]
            : undefined
        }
        onSubmit={values => {
          if (selectedOrderItemIndex !== null) {
            updateOrderItem(selectedOrderItemIndex, values);
            setSelectedOrderItemIndex(null);
          } else append(values);
        }}
        onClose={() => setSelectedOrderItemIndex(null)}
      />
    </OrderFormContext.Provider>
  );
};

const useOrderForm = () => {
  const context = useContext(OrderFormContext);
  if (!context)
    throw new Error("useOrderForm must be used within a OrderFormProvider");
  return context;
};

export { OrderFormProvider, useOrderForm };
