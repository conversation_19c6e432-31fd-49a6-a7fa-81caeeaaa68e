import AddressInfo from "@/components/common/AddressInfo";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import AddressSchema from "@/schemas/common/AddressSchema";
import {
  BuildingOffice2Icon,
  ShieldCheckIcon,
} from "@heroicons/react/24/solid";
import { Loader2, PlusCircle } from "lucide-react";
import { useState } from "react";
import { FieldError, useFormContext, useWatch } from "react-hook-form";
import { useOrderForm } from "../OrderFormContext";

const CustomerAddress = () => {
  const [verifyingAddress, setVerifyingAddress] = useState(false);

  const toAddress = useWatch({ name: "toAddress" });
  const { setModal } = useOrderForm();
  const form = useFormContext();
  const error = form.formState.errors.toAddress as FieldError | undefined;

  const { success: toAddressIsFilled } = AddressSchema.safeParse(toAddress);

  const verifyAddress = () => {
    // TODO verify address
    setVerifyingAddress(true);
    form.setValue("toAddress.verified", true);
    setVerifyingAddress(false);
  };

  return (
    <div className="flex flex-col gap-1">
      <label className="text-sm text-muted-foreground font-medium">
        Ship To
      </label>
      {toAddressIsFilled && (
        <AddressInfo
          name={toAddress.name}
          company={toAddress.company}
          street1={toAddress.street1}
          street2={toAddress.street2}
          city={toAddress.city}
          state={toAddress.state}
          zip={toAddress.zip}
          country={toAddress.country}
          phone={toAddress.phone}
          email={toAddress.email}
          residential={toAddress.residential}
        />
      )}
      <div className="pt-2 space-y-2">
        <div className="flex flex-wrap items-center gap-2">
          <Button
            size="xs"
            variant="secondary"
            type="button"
            onClick={() => setModal("customerAddress", true)}
          >
            {toAddressIsFilled ? (
              <>
                <BuildingOffice2Icon /> Change Address{" "}
              </>
            ) : (
              <>
                <PlusCircle /> Add customer address
              </>
            )}
          </Button>
          {toAddressIsFilled && !toAddress.verified && (
            <Button
              type="button"
              size="xs"
              variant="ghost"
              onClick={verifyAddress}
            >
              {verifyingAddress ? (
                <Loader2 className="animate-spin" />
              ) : (
                <ShieldCheckIcon />
              )}
              Verify Address
            </Button>
          )}
          {toAddress.verified && (
            <Badge variant="accent">
              <ShieldCheckIcon />
              Verified
            </Badge>
          )}
        </div>

        {error && !toAddressIsFilled && (
          <p className="text-destructive text-xs">
            Please specify customer address
          </p>
        )}
      </div>
    </div>
  );
};

export default CustomerAddress;
