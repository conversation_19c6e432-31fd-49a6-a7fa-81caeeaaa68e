import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { format } from "date-fns";
import { CircleHelp } from "lucide-react";
import { useFormContext, useWatch } from "react-hook-form";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";

const OrderIdentifiers = () => {
  const form = useFormContext();
  const orderDate = useWatch({ name: "orderDate" });

  return (
    <div className="text-sm flex-col space-y-2 min-w-76 xl:min-w-88">
      <div className="grid grid-cols-[90px_1fr] gap-1">
        <span className="font-medium text-muted-foreground">Order Date</span>
        <span className="px-2">
          {format(orderDate ?? new Date(), "MMMM d, yyyy")}
        </span>
      </div>
      <FormField
        control={form.control}
        name="orderNo"
        render={({ field }) => (
          <FormItem className="grid grid-cols-[90px_1fr] gap-1">
            <FormLabel className="whitespace-nowrap gap-1">
              Order No.
              <HoverCard>
                <HoverCardTrigger>
                  <CircleHelp className="shrink-0 size-3.5 text-muted-foreground/50" />
                </HoverCardTrigger>
                <HoverCardContent className="text-sm">
                  If left blank, order no. will be auto-generated
                </HoverCardContent>
              </HoverCard>
            </FormLabel>
            <FormControl>
              <Input {...field} placeholder="Optional" className="text-sm" />
            </FormControl>
          </FormItem>
        )}
      />
    </div>
  );
};

export default OrderIdentifiers;
