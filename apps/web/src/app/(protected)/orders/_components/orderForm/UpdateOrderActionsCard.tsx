import { generateRatesAndUpdateOrder } from "@/actions/rates/generateRatesAndUpdateOrder";
import ShippingOptionsForm from "@/components/common/forms/ShippingOptionsForm";
import RateList from "@/components/rates/RateList";
import Header from "@/components/layout/Header";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import AddressSchema from "@/schemas/common/AddressSchema";
import ParcelSchema from "@/schemas/common/ParcelSchema";
import styles from "@/styles/Dashboard.module.css";
import {
  CircleDollarSign,
  Loader2,
  RefreshCcw,
  Save,
  ScrollText,
} from "lucide-react";
import { useMemo, useTransition } from "react";
import { useWatch } from "react-hook-form";
import { toast } from "sonner";
import { useOrderForm } from "./OrderFormContext";

type UpdateOrderActionsCardProps = {
  isSubmitting: boolean;
};

const UpdateOrderActionsCard = ({
  isSubmitting,
}: UpdateOrderActionsCardProps) => {
  const [isGeneratingRates, startGeneratingRates] = useTransition();

  const { id, form, selectedRate, setSelectedRate, setModal } = useOrderForm();

  const fromAddressId = useWatch({ name: "fromAddressId" });
  const toAddress = useWatch({ name: "toAddress" });
  const parcel = useWatch({ name: "parcel" });
  const epShipmentId = useWatch({ name: "epShipmentId" });

  const canGetRates = useMemo(() => {
    const parsedToAddress = AddressSchema.safeParse(toAddress);
    const parsedParcel = ParcelSchema.safeParse(parcel);

    const dataValid =
      fromAddressId && parsedToAddress.success && parsedParcel.success;

    const ratesFetched = !!epShipmentId;

    return (
      (dataValid && !ratesFetched) || (ratesFetched && form.formState.isDirty)
    );
  }, [fromAddressId, toAddress, parcel, epShipmentId, form.formState.isDirty]);

  const handleGetRates = async () => {
    startGeneratingRates(async () => {
      if (!canGetRates || !id) {
        toast.error("cannot get rates, data invalid");
        return;
      }
      const values = form.getValues();

      try {
        const response = await generateRatesAndUpdateOrder(id, values);
        if (!response.success || !response.data) {
          throw new Error(response.error);
        }
        console.log(response.data);
        form.setValue("epShipmentId", response.data.epShipmentId);
      } catch (error) {
        console.error(error);
      }
    });
  };

  return (
    <>
      <div className={cn(styles.panelContainer, styles.panelYSpacing)}>
        <div className="space-y-4">
          <div className="shrink-0 flex justify-between">
            <Header icon={CircleDollarSign}>Rates</Header>
            <Button
              className="ml-auto"
              size="sm"
              variant="link"
              type="button"
              disabled={!canGetRates || isGeneratingRates}
              onClick={handleGetRates}
            >
              <RefreshCcw className={cn(isGeneratingRates && "animate-spin")} />
              {epShipmentId ? "Refresh" : "Get rates"}
            </Button>
          </div>

          {epShipmentId && (
            <RateList
              epShipmentId={epShipmentId}
              onSelect={rate => setSelectedRate(rate)}
              selectedRate={selectedRate}
            />
          )}
        </div>

        <ShippingOptionsForm />

        <div className="flex justify-between gap-4">
          <Button
            type="submit"
            variant="accent"
            className="flex-1"
            disabled={isSubmitting || !form.formState.isDirty}
          >
            {isSubmitting ? <Loader2 className="animate-spin" /> : <Save />}{" "}
            Save Changes
          </Button>
          <Button
            type="button"
            className="flex-1"
            disabled={!selectedRate}
            onClick={() => setModal("buyLabel", true)}
          >
            <ScrollText /> Buy Label
          </Button>
        </div>
      </div>
    </>
  );
};

export default UpdateOrderActionsCard;
