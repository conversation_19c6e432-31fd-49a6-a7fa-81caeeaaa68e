import { cn } from "@/lib/utils";
import Header from "@/components/layout/Header";
import styles from "@/styles/Dashboard.module.css";
import { ReceiptText } from "lucide-react";
import CustomerAddress from "./CustomerAddress";
import OrderIdentifiers from "./OrderIdentifiers";
import OrderItemTable from "./OrderItemTable";

const OrderDetailsForm = () => {
  return (
    <section className={cn(styles.panelContainer, styles.panelYSpacing)}>
      {/* Order Details */}
      <div className="flex flex-col md:flex-row md:justify-between lg:flex-col xl:flex-row gap-4">
        <div className="space-y-4">
          <Header icon={ReceiptText}>Order Details</Header>
          <CustomerAddress />
        </div>
        <OrderIdentifiers />
      </div>

      {/* Items */}
      <OrderItemTable />
    </section>
  );
};

export default OrderDetailsForm;
