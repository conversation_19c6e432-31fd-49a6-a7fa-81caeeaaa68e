import {
  Table,
  TableBody,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import Header from "@/components/layout/Header";
import { getItemQuantity } from "@/lib/utils/order";
import { ClipboardDocumentListIcon } from "@heroicons/react/24/outline";
import OriginalOrderItemRow from "./OriginalOrderItemRow";
import { OrderItem } from "./types";

type OriginalOrderItemsProps = {
  orderItems: OrderItem[];
  distributeItems: (item: OrderItem, quantity: number) => void;
};

const OriginalOrderItems = ({
  orderItems,
  distributeItems,
}: OriginalOrderItemsProps) => {
  const remainingItems = orderItems.filter(item => item.quantity > 0);
  const remainingTotalQty = getItemQuantity(orderItems);

  return (
    <div className="border rounded-md overflow-hidden">
      <div className="p-2 border-b bg-muted">
        <Header icon={ClipboardDocumentListIcon} variant="section">
          Original Order
        </Header>
      </div>
      <Table className="bg-muted rounded-md">
        <TableHeader>
          <TableRow>
            <TableHead>SKU</TableHead>
            <TableHead>Name</TableHead>
            <TableHead className="text-center">Quantity</TableHead>
            <TableHead></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {orderItems.length > 0 &&
            orderItems.map((item, index) => (
              <OriginalOrderItemRow
                key={"originalOrder" + index}
                item={item}
                remainingItems={remainingItems}
                remainingTotalQty={remainingTotalQty}
                distributeItems={distributeItems}
              />
            ))}
        </TableBody>
      </Table>
    </div>
  );
};

export default OriginalOrderItems;
