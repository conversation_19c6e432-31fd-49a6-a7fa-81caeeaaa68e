"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import Header from "@/components/layout/Header";
import { Separator } from "@/components/ui/separator";
import {
  Sheet,
  SheetContent,
  Sheet<PERSON><PERSON><PERSON>,
  SheetFooter,
  Sheet<PERSON>eader,
  She<PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>rigger,
} from "@/components/ui/sheet";
import { cn } from "@/lib/utils";
import OrderItemSchema from "@/schemas/order/OrderItemsSchema";
import { useSplitOrderMutation } from "@/store/api/orderApi";
import styles from "@/styles/Dashboard.module.css";
import {
  CheckCircleIcon,
  ExclamationCircleIcon,
} from "@heroicons/react/24/solid";
import { format } from "date-fns";
import { Loader2, Plus, Split } from "lucide-react";
import { useRouter } from "next/navigation";
import { useWatch } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";
import { useOrderForm } from "../OrderFormContext";
import NewOrderItems from "./NewOrderItems";
import OriginalOrderItems from "./OriginalOrderItems";
import { useSplitOrder } from "./hooks/useSplitOrder";
import { OrderItem } from "./types";

type SplitOrderItemsProps = { orderItems: z.infer<typeof OrderItemSchema>[] };

const SplitOrderItems = ({ orderItems }: SplitOrderItemsProps) => {
  const { push } = useRouter();

  const { form, id, isSplit, parentOrderId, setModal, getModalState } =
    useOrderForm();
  const [orderDate, orderNo, store] = useWatch({
    name: ["orderDate", "orderNo", "store"],
  });
  const { isDirty } = form.formState;

  const canSplit = !isSplit && !parentOrderId && !isDirty;

  const {
    // State
    originalOrderItems,
    newOrders,
    activeNewOrderIndex,
    canAddNewOrder,
    // Actions
    distributeItems,
    returnItem,
    setActiveNewOrderIndex,
    addNewOrder,
    deleteNewOrder,
    resetState,
  } = useSplitOrder(orderItems);

  const [splitOrder, { isLoading }] = useSplitOrderMutation();

  const hasMovedItems = newOrders.every(order =>
    order.items.some(item => item.quantity > 0)
  );

  const handleClose = () => {
    setModal("splitOrder", false);
    resetState();
  };

  const handleSubmit = async () => {
    // Check again if all new orders have at least one item with quantity > 0
    if (!hasMovedItems) {
      toast.error("Error", {
        description: "Please move items to new order(s)",
        icon: <ExclamationCircleIcon className="text-destructive" />,
      });
      return;
    }

    const refinedData = {
      originalOrderItems: originalOrderItems
        .map(item => {
          const { id, ...rest } = item;
          return rest;
        })
        .filter(item => item.quantity > 0),
      newOrders: newOrders.map(order => ({
        items: order.items.map(item => {
          const { id, ...rest } = item;
          return rest;
        }),
      })),
    };

    const payload = {
      orderId: id!,
      data: refinedData,
    };

    try {
      const response = await splitOrder(payload).unwrap();
      toast.success(
        `Order split successfully into ${response.createdOrdersCount + 1} orders`,
        {
          icon: <CheckCircleIcon className="text-green-500" />,
        }
      );

      handleClose();
      push("/orders");
    } catch (error) {
      console.log(error);
      toast.error("Error splitting order", {
        description: "Please try again",
        icon: <ExclamationCircleIcon className="text-destructive" />,
      });
    }
  };
  return (
    <Sheet
      open={getModalState("splitOrder")}
      onOpenChange={value => setModal("splitOrder", value)}
    >
      <SheetTrigger asChild>
        <Button
          type="button"
          size="xs"
          variant="ghost-primary"
          disabled={!canSplit}
        >
          <Split /> Split Order Items
        </Button>
      </SheetTrigger>
      <SheetContent className="w-full sm:max-w-none lg:max-w-5xl p-4 md:px-6 lg:px-8 xl:gap-y-5">
        <SheetTitle className="sr-only">Split Order</SheetTitle>

        <SheetHeader className="gap-1 px-0 pb-2">
          <Header icon={Split} variant="section" as="h3">
            Split Order
          </Header>
          <SheetDescription className="ml-7">
            Split this order into multiple orders to create separate shipments.
          </SheetDescription>
        </SheetHeader>
        <Separator />
        {/* Overview */}
        <div className="rounded-md p-4 space-y-2 bg-primary/5 border border-primary text-sm text-muted-foreground">
          <h4 className="font-medium text-base text-foreground">
            Order Number: {orderNo}
          </h4>
          <div className="flex flex-col gap-1 sm:flex-row sm:justify-between sm:items-end">
            <div className="space-y-1">
              <p>Order Date: {format(orderDate, "MMMM d, yyyy")}</p>
              <p>{store}</p>
            </div>
          </div>
        </div>

        <main
          className={cn(
            "mt-4 grid grid-rows-[auto_1fr] sm:grid-cols-2 sm:grid-rows-1 items-start",
            styles.gridGap
          )}
        >
          <OriginalOrderItems
            orderItems={originalOrderItems}
            distributeItems={distributeItems}
          />

          <div className="space-y-5">
            {newOrders.map((order: { items: OrderItem[] }, index) => (
              <NewOrderItems
                key={"newOrder" + index}
                index={index}
                orderItems={order.items}
                selected={activeNewOrderIndex === index}
                disableDeleteOrder={newOrders.length <= 1}
                setSelected={() => setActiveNewOrderIndex(index)}
                returnItem={returnItem}
                deleteOrder={deleteNewOrder}
              />
            ))}
            <Button
              size="xs"
              variant="secondary"
              onClick={addNewOrder}
              disabled={!canAddNewOrder}
            >
              <Plus />
              Add order
            </Button>
          </div>
        </main>

        <SheetFooter className="flex-row justify-end">
          <Button
            type="button"
            variant="ghost-destructive"
            onClick={handleClose}
          >
            Cancel
          </Button>

          <Button disabled={!hasMovedItems || isLoading} onClick={handleSubmit}>
            {isLoading && <Loader2 className="animate-spin" />} Split Orders
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
};

export default SplitOrderItems;
