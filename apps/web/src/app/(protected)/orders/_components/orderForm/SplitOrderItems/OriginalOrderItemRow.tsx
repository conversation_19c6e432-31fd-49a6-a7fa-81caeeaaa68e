import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { TableCell, TableRow } from "@/components/ui/table";
import { cn } from "@/lib/utils";
import { ExclamationTriangleIcon } from "@heroicons/react/24/solid";
import { ArrowRight } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import { OrderItem } from "./types";

type OriginalOrderItemRowProps = {
  item: OrderItem;
  remainingItems: OrderItem[];
  remainingTotalQty: number;
  distributeItems: (item: OrderItem, quantity: number) => void;
};

const OriginalOrderItemRow = ({
  item,
  remainingItems,
  remainingTotalQty,
  distributeItems,
}: OriginalOrderItemRowProps) => {
  const [enableSplitQty, setEnableSplitQty] = useState(false);
  const [splitQty, setSplitQty] = useState(0);

  const toggleSplitQty = () => {
    if (enableSplitQty) setEnableSplitQty(false);
    else setEnableSplitQty(true);
  };

  const handleSplitQtyAdd = () => {
    const quantityIsValid = splitQty > 0 && splitQty <= item.quantity;

    if (!quantityIsValid) {
      toast.error("Quantity is not valid.", {
        icon: <ExclamationTriangleIcon className="text-destructive" />,
      });
      return;
    }

    if (remainingTotalQty <= splitQty) {
      toast.error("Original order cannot be empty.", {
        icon: <ExclamationTriangleIcon className="text-destructive" />,
      });
      return;
    }

    distributeItems(item, splitQty);
    setSplitQty(0);
    setEnableSplitQty(false);
  };

  return (
    <>
      <TableRow
        className={cn(
          item.quantity === 0 && "opacity-50 bg-gray-200 hover:bg-gray-200"
        )}
      >
        <TableCell>{item.product.sku}</TableCell>
        <TableCell className="max-w-36 truncate">{item.product.name}</TableCell>
        <TableCell className="text-center">{item.quantity}</TableCell>
        <TableCell className="flex items-center justify-end gap-1.5 text-primary text-xs">
          <Button
            variant={enableSplitQty ? "ghost-destructive" : "ghost-primary"}
            size="xs"
            disabled={item.quantity === 0 || remainingTotalQty <= 1}
            onClick={() => toggleSplitQty()}
          >
            {enableSplitQty ? "Cancel" : "Split Qty."}
          </Button>

          <Button
            type="button"
            variant="ghost-primary"
            size="icon-xs"
            disabled={
              item.quantity === 0 ||
              remainingItems.length <= 1 ||
              enableSplitQty
            }
            onClick={() => distributeItems(item, item.quantity)}
          >
            <ArrowRight />
          </Button>
        </TableCell>
      </TableRow>
      {enableSplitQty && (
        <TableRow>
          <TableCell colSpan={4}>
            <div className="flex items-center justify-end gap-1">
              <Input
                inputSize="sm"
                type="number"
                value={splitQty}
                min={0}
                max={item.quantity}
                onChange={e => setSplitQty(Number(e.target.value))}
                className="w-16 rounded-md pr-1.5 text-right"
              />
              <Button
                variant="ghost-primary"
                size="icon-xs"
                onClick={handleSplitQtyAdd}
              >
                <ArrowRight />
              </Button>
            </div>
          </TableCell>
        </TableRow>
      )}
    </>
  );
};

export default OriginalOrderItemRow;
