import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import styles from "@/styles/Dashboard.module.css";
import { CircleDollarSign, Loader2 } from "lucide-react";
import { useRouter } from "next/navigation";
import ShippingOptionsForm from "@/components/common/forms/ShippingOptionsForm";
import Header from "@/components/layout/Header";

type CreateOrderActionsCardProps = {
  isSubmitting: boolean;
};

const CreateOrderActionsCard = ({
  isSubmitting,
}: CreateOrderActionsCardProps) => {
  const router = useRouter();

  return (
    <div className={cn(styles.panelContainer, styles.panelYSpacing)}>
      <div className="space-y-4">
        <div className="shrink-0 flex justify-between">
          <Header icon={CircleDollarSign}>Rates</Header>
        </div>
      </div>

      {/* OPTIONS */}
      <ShippingOptionsForm />

      <div className="flex justify-between gap-4">
        <Button
          type="submit"
          variant="accent"
          className="flex-1"
          disabled={isSubmitting}
        >
          {isSubmitting && <Loader2 className="animate-spin" />} Create Order
        </Button>
        <Button
          className="flex-1"
          type="button"
          onClick={() => router.push("/orders")}
          variant={"muted"}
        >
          Cancel
        </Button>
      </div>
    </div>
  );
};

export default CreateOrderActionsCard;
