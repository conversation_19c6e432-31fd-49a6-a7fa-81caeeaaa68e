import { But<PERSON> } from "@/components/ui/button";
import Header from "@/components/layout/Header";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { cn } from "@/lib/utils";
import { Plus, Trash, X } from "lucide-react";
import { OrderItem } from "./types";

type NewOrderItemsProps = {
  index: number;
  orderItems: OrderItem[];
  selected: boolean;
  disableDeleteOrder: boolean;
  setSelected: () => void;
  returnItem: (item: OrderItem, selectedIndex: number) => void;
  deleteOrder: (orderIndex: number) => void;
};

const NewOrderItems = ({
  index,
  orderItems,
  selected,
  disableDeleteOrder,
  setSelected,
  returnItem,
  deleteOrder,
}: NewOrderItemsProps) => {
  return (
    <div
      className={cn(
        selected && "border-primary bg-secondary/20",
        "border rounded-md overflow-hidden transition-colors duration-300"
      )}
    >
      <div className="p-2 border-b flex justify-between">
        <Header
          icon={Plus}
          variant="section"
        >{`New Order ${index + 1}`}</Header>
        <div className="flex items-center gap-2">
          <Button
            size="icon-xs"
            variant="ghost-destructive"
            onClick={() => deleteOrder(index)}
            disabled={disableDeleteOrder}
          >
            <Trash />
          </Button>

          <Button
            size="xxs"
            variant={selected ? "secondary" : "accent"}
            onClick={setSelected}
            className={cn(!selected && "cursor-pointer")}
          >
            {selected ? "Active" : "Select"}
          </Button>
        </div>
      </div>
      <Table
        className={cn(
          selected && "bg-secondary/20",
          "transition-colors duration-300"
        )}
      >
        <TableHeader>
          <TableRow>
            <TableHead>SKU</TableHead>
            <TableHead>Name</TableHead>
            <TableHead className="text-center">Quantity</TableHead>
            <TableHead></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {orderItems.length > 0 ? (
            orderItems.map((item, j) => (
              <TableRow key={"newOrders" + j}>
                <TableCell>{item.product.sku}</TableCell>
                <TableCell className="max-w-36 truncate">
                  {item.product.name}
                </TableCell>
                <TableCell className="text-center">{item.quantity}</TableCell>
                <TableCell className="text-right">
                  <Button
                    variant="ghost-destructive"
                    size="icon-xs"
                    onClick={() => returnItem(item, index)}
                  >
                    <X />
                  </Button>
                </TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell
                colSpan={4}
                className="py-5 col-span-4 text-center text-xs text-muted-foreground/70"
              >
                No items added
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
};

export default NewOrderItems;
