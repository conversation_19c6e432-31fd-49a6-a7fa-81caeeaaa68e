import { generateRandomId } from "@/lib/utils/generateRandomId";
import { useEffect, useMemo, useState } from "react";
import { z } from "zod";
import { OrderItem, SplitOrderState } from "../types";
import OrderItemSchema from "@/schemas/order/OrderItemsSchema";

const MAX_NEW_ORDERS = 2;

export const useSplitOrder = (
  initialOrderItems: z.infer<typeof OrderItemSchema>[]
) => {
  // Transform initial items to include IDs
  const itemsWithId = useMemo(
    () => initialOrderItems.map(item => ({ ...item, id: generateRandomId() })),
    [initialOrderItems]
  );

  // State
  const [splitOrderState, setSplitOrderState] = useState<SplitOrderState>({
    originalOrderItems: itemsWithId,
    newOrders: [{ items: [] }],
    activeNewOrderIndex: 0,
  });

  useEffect(() => {
    setSplitOrderState(prev => ({ ...prev, originalOrderItems: itemsWithId }));
  }, [itemsWithId]);

  // Memoized values
  const canAddNewOrder = useMemo(
    () => splitOrderState.newOrders.length < MAX_NEW_ORDERS,
    [splitOrderState.newOrders]
  );

  // Actions
  const distributeItems = (orderItem: OrderItem, quantity: number) => {
    const selectedIndex = splitOrderState.activeNewOrderIndex;

    // check if active new order index is valid
    const newOrderIndex =
      splitOrderState.newOrders[splitOrderState.activeNewOrderIndex];

    if (!newOrderIndex) {
      console.log("Please selected new order you want to add items to.");
      return;
    }

    // Update original order items
    const updatedOriginalOrderItems = splitOrderState.originalOrderItems.map(
      item => {
        if (item.id === orderItem.id) {
          return { ...item, quantity: item.quantity - quantity };
        }
        return item;
      }
    );

    // Update new order items
    const newOrderItems = splitOrderState.newOrders[selectedIndex].items;
    const existingItemIndex = splitOrderState.newOrders[
      selectedIndex
    ].items.findIndex(item => item.id === orderItem.id);

    // Find if the item is already in the new order
    if (existingItemIndex >= 0) {
      newOrderItems[existingItemIndex] = {
        ...newOrderItems[existingItemIndex],
        quantity: newOrderItems[existingItemIndex].quantity + quantity,
      };
    } else {
      newOrderItems.push({ ...orderItem, quantity });
    }

    setSplitOrderState(prev => ({
      ...prev,
      originalOrderItems: updatedOriginalOrderItems,
      newOrders: [
        ...prev.newOrders.slice(0, selectedIndex),
        { items: newOrderItems },
        ...prev.newOrders.slice(selectedIndex + 1),
      ],
    }));
  };

  const returnItem = (orderItem: OrderItem, selectedIndex: number) => {
    const updatedOriginalOrderItem = splitOrderState.originalOrderItems.map(
      item => {
        if (item.id === orderItem.id) {
          return { ...item, quantity: item.quantity + orderItem.quantity };
        }
        return item;
      }
    );

    const updatedNewOrderItems = splitOrderState.newOrders[
      selectedIndex
    ].items.filter(item => item.id !== orderItem.id);

    setSplitOrderState(prev => ({
      ...prev,
      originalOrderItems: updatedOriginalOrderItem,
      newOrders: [
        ...prev.newOrders.slice(0, selectedIndex),
        { items: updatedNewOrderItems },
        ...prev.newOrders.slice(selectedIndex + 1),
      ],
    }));
  };

  const setActiveNewOrderIndex = (index: number) => {
    setSplitOrderState(prev => ({ ...prev, activeNewOrderIndex: index }));
  };

  const addNewOrder = () => {
    setSplitOrderState(prev => ({
      ...prev,
      newOrders: [...prev.newOrders, { items: [] }],
    }));
  };

  const deleteNewOrder = (orderIndex: number) => {
    const itemsToReturn = [...splitOrderState.newOrders[orderIndex].items];

    const updatedOriginalOrderItems = splitOrderState.originalOrderItems.map(
      item => {
        const itemToReturn = itemsToReturn.find(i => i.id === item.id);
        if (itemToReturn) {
          return { ...item, quantity: item.quantity + itemToReturn.quantity };
        }
        return item;
      }
    );

    setSplitOrderState(prev => ({
      ...prev,
      originalOrderItems: updatedOriginalOrderItems,
      newOrders: prev.newOrders.filter((_order, i) => i !== orderIndex),
    }));
  };

  const resetState = () => {
    setSplitOrderState({
      originalOrderItems: itemsWithId,
      newOrders: [{ items: [] }],
      activeNewOrderIndex: 0,
    });
  };

  return {
    // State
    originalOrderItems: splitOrderState.originalOrderItems,
    newOrders: splitOrderState.newOrders,
    activeNewOrderIndex: splitOrderState.activeNewOrderIndex,
    canAddNewOrder,

    // Actions
    distributeItems,
    returnItem,
    setActiveNewOrderIndex,
    addNewOrder,
    deleteNewOrder,
    resetState,
  };
};
