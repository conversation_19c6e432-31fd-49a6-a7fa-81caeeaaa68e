import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { orderStatusColor } from "@/lib/mappings/statusColor";
import { capitalize } from "@/lib/utils/strings";
import { GetOrdersResponse } from "@/store/types/orders";
import { OrderStatus } from "@repo/database";
import { Table } from "@tanstack/react-table";

type StatusFilterValue = OrderStatus | "ALL";

const statusOptions = [
  { value: "ALL", label: "All" },
  ...Object.values(OrderStatus).map(value => ({
    value,
    label: capitalize(value),
  })),
];

type StatusSelectProps = {
  table: Table<GetOrdersResponse["data"][number]>;
};

const StatusSelect = ({ table }: StatusSelectProps) => {
  const statusFilterValue =
    (table.getColumn("status")?.getFilterValue() as StatusFilterValue) ?? "ALL";

  const handleStatusSelect = (value: StatusFilterValue) =>
    table
      .getColumn("status")
      ?.setFilterValue(value === "ALL" ? undefined : value);

  return (
    <Select value={statusFilterValue} onValueChange={handleStatusSelect}>
      <SelectTrigger className="w-[140px]">
        <SelectValue />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          <SelectLabel>Status</SelectLabel>
          {statusOptions.map(status => (
            <SelectItem
              key={status.value}
              value={status.value}
              className={
                orderStatusColor[
                  status.value as keyof typeof orderStatusColor
                ] ?? ""
              }
            >
              {status.label}
            </SelectItem>
          ))}
        </SelectGroup>
      </SelectContent>
    </Select>
  );
};

export default StatusSelect;
