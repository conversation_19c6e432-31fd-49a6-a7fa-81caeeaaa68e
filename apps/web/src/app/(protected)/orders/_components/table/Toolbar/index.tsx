import QuickSearch from "@/components/common/table/toolbar/QuickSearch";
import ViewOptions from "@/components/common/table/toolbar/ViewOptions";
import { GetOrdersResponse } from "@/store/types/orders";
import { Table } from "@tanstack/react-table";
import AdvancedSearch from "./AdvancedSearch";
import SelectionActions from "./SelectionActions";
import StatusSelect from "./StatusSelect";

type ToolbarProps = {
  table: Table<GetOrdersResponse["data"][number]>;
  setSearch: React.Dispatch<React.SetStateAction<string>>;
};

const Toolbar = ({ table, setSearch }: ToolbarProps) => {
  const noOfSelectedRows = table.getFilteredSelectedRowModel().rows.length;

  return (
    <div className="flex md:items-center md:justify-between gap-4 md:flex-row flex-col">
      {/* LEFT */}
      <div className="flex items-center gap-2 h-8">
        {noOfSelectedRows > 0 ? (
          <SelectionActions table={table} noOfSelectedRows={noOfSelectedRows} />
        ) : (
          <QuickSearch setSearch={setSearch} />
        )}
      </div>
      {/* RIGHT */}
      <div className="flex items-center gap-2 self-end">
        <AdvancedSearch table={table} />
        <ViewOptions table={table} />
        <StatusSelect table={table} />
      </div>
    </div>
  );
};

export default Toolbar;
