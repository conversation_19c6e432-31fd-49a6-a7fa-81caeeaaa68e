import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";

type SkeletonTableProps = {
  className?: string;
};

const SkeletonTable = ({ className }: SkeletonTableProps) => {
  return (
    <div className={cn(className, "flex flex-col gap-4")}>
      <div className="flex justify-between items-center space-x-2">
        <div className="flex gap-1.5">
          <Skeleton className="w-[200px] h-9 rounded-full" />
          <Skeleton className="size-9 rounded-full" />
        </div>
        <div className="flex space-x-2">
          <Skeleton className="size-9 rounded-full" />
          <Skeleton className="size-9 rounded-full" />
          <Skeleton className="w-[140px] h-9 rounded-full" />
        </div>
      </div>
      <Skeleton className="grow-1 h-[calc(100dvh-375px)] rounded-lg" />
    </div>
  );
};

export default SkeletonTable;
