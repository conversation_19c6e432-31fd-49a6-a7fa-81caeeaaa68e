import CopyTextWrapper from "@/components/common/CopyTextWrapper";
import Status from "@/components/common/Status";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { UNIT_SYSTEMS } from "@/config/unitSystem";
import { getCarrierIcon } from "@/lib/mappings/carriers";
import { calculateAge, displayLargestTimeUnit } from "@/lib/utils/dates";
import { FindAllOrder } from "@/types/Order/Order";
import { HomeIcon } from "@heroicons/react/24/solid";
import { Parcel } from "@repo/database";
import { getCarrierBasicInfo, getServiceLevel } from "@repo/shared-data";
import { ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";
import { EllipsisVertical, Pencil, X } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import OrderItemHoverCard from "../../../../../components/common/table/customCells/OrderItemHoverCard";
import RatesCell from "./customCells/RatesCell";

export const createOrderColumns = (): ColumnDef<FindAllOrder>[] => [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={value => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={value => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "orderNo",
    header: "Order No.",
    cell: ({ row }) => {
      const data = row.original;
      return (
        <Link
          href={`/orders/${data.id}`}
          className="hover:text-primary hover:underline underline-offset-2"
        >
          {data?.orderNo}
        </Link>
      );
    },
  },
  {
    accessorKey: "createdAt",
    header: "Order Date",
    cell: ({ row }) => {
      const createdAt = row.original.orderDate;
      return <div>{format(createdAt, "MM/dd/yyyy")}</div>;
    },
  },
  {
    id: "age",
    header: "Age",
    cell: ({ row }) => {
      const createdAt = row.original.createdAt;
      const age = calculateAge(new Date(createdAt));

      return <div>{displayLargestTimeUnit(age)}</div>;
    },
  },
  {
    accessorKey: "batchName",
    header: "Batch",
    cell: ({ row }) => {
      const batchId = row.original.batchId;
      const batch = row.original.batch;
      return (
        <Link
          className="hover:text-primary hover:underline underline-offset-2"
          href={`/orders/batches/${batchId}`}
        >
          {batch?.name}
        </Link>
      );
    },
  },
  {
    accessorKey: "toAddress.name",
    id: "customerName",
    header: "Customer",
    cell: ({ row }) => {
      const toAddress = row.original.toAddress as {
        name: string;
        state: string;
        country: string;
        residential: boolean;
      };

      const { name, state, country, residential } = toAddress;
      return (
        <div className="leading-4">
          <p>
            {residential && (
              <HomeIcon className="size-3.5 inline-block text-primary mr-1 mb-1" />
            )}
            {name}
          </p>
          <p className="text-xs text-muted-foreground">
            {state}, {country}
          </p>
        </div>
      );
    },
  },
  {
    accessorKey: "parcel",
    header: "Parcel Info",
    cell: ({ row }) => {
      const parcel = row.getValue("parcel") as Parcel;
      const { weight, length, width, height } = parcel;

      return (
        <div className="text-xs leading-4">
          <p>
            {length} x {width} x {height}{" "}
            {UNIT_SYSTEMS.imperial.dimension.shortLabel}
          </p>
          <p>
            {weight} {UNIT_SYSTEMS.imperial.weight.shortLabel}
          </p>
        </div>
      );
    },
  },
  {
    accessorKey: "service",
    header: () => <div className="min-w-20">Service</div>,
    cell: ({ row }) => {
      const carrier = getCarrierBasicInfo(row.original.carrier ?? "");
      const service = getServiceLevel(carrier?.id, row.original.service ?? "");
      if (!carrier || !service) return null;
      const carrierIcon = getCarrierIcon(carrier.id);

      return (
        <div className="flex flex-col gap-0.5 px-1">
          <div className="flex items-center justify-start gap-1">
            <Image
              src={carrierIcon}
              alt={carrier.fullName}
              className="h-4 w-auto mr-auto max-w-20"
            />
            <span className="text-xs grow-1 font-semibold">
              {carrier.shortName}
            </span>
          </div>
          <span className="text-xs text-muted-foreground">{service.label}</span>
        </div>
      );
    },
  },
  {
    id: "rates",
    header: "Rates",
    cell: ({ row }) => {
      const { epShipmentId, service, status } = row.original;

      return (
        <RatesCell
          orderId={row.original.id}
          epShipmentId={epShipmentId}
          service={service}
          status={status}
        />
      );
    },
  },
  {
    id: "sku",
    header: "SKU",
    cell: ({ row }) => {
      const { orderItems } = row.original;

      return orderItems.length <= 1 ? (
        <div>{orderItems[0]?.product?.sku}</div>
      ) : (
        <OrderItemHoverCard orderItems={orderItems} />
      );
    },
  },
  {
    id: "orderItems",
    header: "Item Name",
    cell: ({ row }) => {
      const { orderItems } = row.original;
      return orderItems.length <= 1 ? (
        <div>{orderItems[0].product.name}</div>
      ) : (
        <OrderItemHoverCard orderItems={orderItems} />
      );
    },
  },
  {
    id: "itemQuantity",
    header: () => <div className="text-right">Item Quantity</div>,
    cell: ({ row }) => {
      const { orderItems } = row.original;
      return <div className="text-right">{orderItems.length}</div>;
    },
  },
  { accessorKey: "notes", header: "Notes" },
  {
    accessorKey: "store",
    header: "Store",
    cell: ({ row }) => {
      const store = row.original.store as { name: string };

      return <div className="text-right">{store?.name}</div>;
    },
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const { status } = row.original;

      return <Status text={status} />;
    },
  },
];

type createOrderActionsProps = {
  handleEditOrder: (id: string) => void;
  handleCancel: (id: string) => void;
};

export const createOrderActions = ({
  handleEditOrder,
  handleCancel,
}: createOrderActionsProps): ColumnDef<FindAllOrder>[] => [
  {
    id: "actions",
    cell: ({ row }) => {
      const order = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon-xs">
              <span className="sr-only">Open menu</span>
              <EllipsisVertical />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <CopyTextWrapper text={order.orderNo} isTable={true}>
              <DropdownMenuItem>Copy Order No.</DropdownMenuItem>
            </CopyTextWrapper>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => handleEditOrder(order.id)}>
              <Pencil />
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem
              variant="destructive"
              onClick={() => handleCancel(order.id)}
            >
              <X />
              Cancel
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];
