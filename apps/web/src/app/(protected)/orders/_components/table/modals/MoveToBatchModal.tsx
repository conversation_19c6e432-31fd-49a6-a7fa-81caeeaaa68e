"use client";

import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert";
import Header from "@/components/layout/Header";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { DropdownMenuShortcut } from "@/components/ui/dropdown-menu";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useGetBatchesQuery } from "@/store/api/batchApi";
import { useAddOrdersToBatchMutation } from "@/store/api/orderApi";
import { ClipboardDocumentListIcon } from "@heroicons/react/24/outline";
import { BatchStatus } from "@repo/database";
import { Loader2, <PERSON>Alert } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";
import CreateNewBatchWrapper from "../../../batches/_components/CreateNewBatchWrapper";

/* 
NOTE:
To stop event bubbling to the parent dropdown menu, we need to stop propagation of the event from the submit button and the form's onKeyDown event
*/
type MoveToBatchModalProps = {
  selectedOrderIds: string[];
  onSuccess?: () => void;
};

const MoveToBatchModal = ({
  selectedOrderIds,
  onSuccess,
}: MoveToBatchModalProps) => {
  const [open, setOpen] = useState(false);
  const [batchId, setBatchId] = useState<string | "">("");
  const [error, setError] = useState<string | null>(null);

  const filters = { [`filter[status]`]: BatchStatus.open };
  const params = new URLSearchParams(filters);
  const queryStr = params.toString();
  const { push } = useRouter();

  const { data: batches, isLoading: loadingBatches } =
    useGetBatchesQuery(queryStr);
  const [addOrdersToBatch, { isLoading: isAdding }] =
    useAddOrdersToBatchMutation();

  const handleChange = (value: string) => {
    setBatchId(value);
    setError(null);
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!batchId) {
      setError("Please select a batch");
      return;
    }

    try {
      const { count } = await addOrdersToBatch({
        batchId,
        orderIds: selectedOrderIds,
      }).unwrap();

      const selectedBatch = batches?.data.find(b => b.id === batchId);

      setBatchId("");
      onSuccess?.();
      setOpen(false);
      push(`/orders/batches/${batchId}`);

      toast.success(
        `Successfully moved ${count} orders to ${selectedBatch?.name ?? "selected batch"}.`
      );
    } catch (error) {
      if (process.env.NODE_ENV === "development") console.log(error);
      setError(
        "Failed to assign batch. Please make sure the orders you are trying to assign are open"
      );
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger className="w-full h-full flex items-center gap-2 justify-between">
        Move to Batch
        <DropdownMenuShortcut>⌘B</DropdownMenuShortcut>
      </DialogTrigger>
      <DialogContent
        className="sm:max-w-md gap-y-5 xl:gap-y-6"
        onInteractOutside={e => {
          e.preventDefault();
        }}
      >
        <DialogTitle className="sr-only">Move to Batch</DialogTitle>
        <DialogDescription className="sr-only">
          Form to move orders to a batch
        </DialogDescription>
        <DialogHeader>
          <Header icon={ClipboardDocumentListIcon} as="h3">
            Move to Batch
          </Header>
        </DialogHeader>
        <form
          className="space-y-5 xl:space-y-6"
          onSubmit={handleSubmit}
          onKeyDown={e => {
            if (e.key === "Enter") e.stopPropagation();
          }}
        >
          <div className="flex flex-col gap-y-4">
            <Label className="flex-col items-start text-foreground">
              Batch Name
              <div className="font-normal text-muted-foreground">
                Only batches with the status &apos;open&apos; is available .
              </div>
            </Label>
            <div>
              {batches && batches?.pagination.total > 0 ? (
                <>
                  <Select value={batchId} onValueChange={handleChange}>
                    <SelectTrigger
                      className="w-full"
                      disabled={loadingBatches}
                      aria-disabled={loadingBatches}
                    >
                      <SelectValue placeholder="Select a batch" />
                    </SelectTrigger>
                    <SelectContent>
                      {batches?.data.map(batch => (
                        <SelectItem key={batch.id} value={batch.id}>
                          {batch.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {error && (
                    <Alert variant="destructive" className="mt-2 text-sm">
                      <TriangleAlert />

                      <AlertDescription>{error}</AlertDescription>
                    </Alert>
                  )}
                </>
              ) : (
                <p className="text-muted-foreground text-sm">
                  You don&apos;t have any open batches.{" "}
                  <CreateNewBatchWrapper className="inline">
                    <Button size="sm" variant="link" className="inline px-0!">
                      Create new batch
                    </Button>
                  </CreateNewBatchWrapper>{" "}
                  to move orders to.
                </p>
              )}
            </div>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button type="button" variant="ghost-destructive">
                Cancel
              </Button>
            </DialogClose>
            <Button
              type="submit"
              disabled={isAdding}
              onClick={e => e.stopPropagation()}
            >
              {isAdding && <Loader2 className="animate-spin" />}Select Batch
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default MoveToBatchModal;
