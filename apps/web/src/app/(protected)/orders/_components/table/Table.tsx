"use client";

import SkeletonTable from "@/components/loading/SkeletonTable";
import DataTable from "@/components/common/table/DataTable";
import { createPaginationHandler } from "@/lib/utils/table/pagination";
import {
  useGetOrdersQuery,
  useUpdateOrderStatusMutation,
} from "@/store/api/orderApi";
import { GetOrdersResponse } from "@/store/types/orders";
import { FindAllOrder } from "@/types/Order/Order";
import { OrderStatus } from "@repo/database";
import {
  ColumnDef,
  ColumnFilter,
  ColumnFiltersState,
  getCoreRowModel,
  OnChangeFn,
  PaginationState,
  Updater,
  useReactTable,
  VisibilityState,
} from "@tanstack/react-table";
import { Loader2 } from "lucide-react";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useMemo, useState } from "react";
import { toast } from "sonner";
import Toolbar from "./Toolbar";
import { createOrderActions, createOrderColumns } from "./useColumns";

const PAGE_SIZE_OPTIONS = [100, 250, 500, 1000];
const DEFAULT_PAGE_SIZE = PAGE_SIZE_OPTIONS[0];
const DEFAULT_PAGE_INDEX = 0;

const TableWrapper = () => {
  const [search, setSearch] = useState("");
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: DEFAULT_PAGE_INDEX,
    pageSize: DEFAULT_PAGE_SIZE,
  });
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([
    { id: "status", value: OrderStatus.open },
  ]);

  const queryStr = useMemo(() => {
    const query = new URLSearchParams();
    // Pagination
    query.append("limit", pagination.pageSize.toString());
    query.append(
      "offset",
      (pagination.pageIndex * pagination.pageSize).toString()
    );
    // Filters
    columnFilters.forEach((filter: ColumnFilter) => {
      if (typeof filter.value === "object" && filter.value !== null) {
        Object.entries(filter.value).forEach(([key, value]) => {
          query.append(`filter[${filter.id}][${key}]`, value);
        });
      } else query.append(`filter[${filter.id}]`, `${filter.value}`);
    });
    // Search
    if (search.trim()) query.append("search", search);

    return query.toString();
  }, [pagination, columnFilters, search]);

  const { data: orders, isLoading, isFetching } = useGetOrdersQuery(queryStr);
  const [updateOrderStatus] = useUpdateOrderStatusMutation();

  const { push } = useRouter();

  const handleEditOrder = useCallback(
    (id: string) => push(`/orders/${id}`),
    [push]
  );

  const handleCancel = useCallback(
    async (id: string) => {
      try {
        await updateOrderStatus({
          orderId: id,
          status: OrderStatus.cancelled,
        }).unwrap();
        toast.success("Order cancelled successfully");
      } catch (error) {
        console.error("Failed to cancel order:", error);
        toast.error("Failed to cancel order");
      }
    },
    [updateOrderStatus]
  );

  const columns = useMemo(
    () => [
      ...createOrderColumns(),
      ...createOrderActions({ handleEditOrder, handleCancel }),
    ],
    [handleEditOrder, handleCancel]
  );

  const [mounted, setMounted] = useState(false);
  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  return (
    <>
      {!orders || !columns || isLoading ? (
        <SkeletonTable />
      ) : (
        <Table
          isFetching={isFetching}
          data={orders}
          columns={columns}
          pagination={pagination}
          setPagination={setPagination}
          columnFilters={columnFilters}
          setColumnFilters={setColumnFilters}
          setSearch={setSearch}
        />
      )}
    </>
  );
};

type TableProps = {
  isFetching: boolean;
  data: GetOrdersResponse;
  columns: ColumnDef<FindAllOrder>[];
  pagination: PaginationState;
  setPagination: React.Dispatch<React.SetStateAction<PaginationState>>;
  columnFilters: ColumnFiltersState;
  setColumnFilters: React.Dispatch<React.SetStateAction<ColumnFiltersState>>;
  setSearch: React.Dispatch<React.SetStateAction<string>>;
};

const Table = ({
  isFetching,
  data,
  columns,
  pagination,
  setPagination,
  columnFilters,
  setColumnFilters,
  setSearch,
}: TableProps) => {
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});

  const onPaginationChange = useCallback(
    (updater: Updater<PaginationState>) =>
      createPaginationHandler(
        data.pagination,
        pagination,
        setPagination
      )(updater),
    [data.pagination, pagination, setPagination]
  );

  const table = useReactTable({
    data: data.data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    // Pagination
    manualPagination: true,
    rowCount: data.pagination.total,
    onPaginationChange: onPaginationChange as OnChangeFn<PaginationState>,
    // Filtering
    manualFiltering: true,
    onColumnFiltersChange: setColumnFilters as OnChangeFn<ColumnFiltersState>,
    // Column Visibility Settings
    onColumnVisibilityChange: setColumnVisibility,
    // Row Selection
    onRowSelectionChange: setRowSelection,
    state: {
      pagination,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  return (
    <div className="flex flex-col gap-4">
      <Toolbar table={table} setSearch={setSearch} />

      {!isFetching ? (
        <DataTable
          table={table}
          pageSizeOptions={PAGE_SIZE_OPTIONS}
          height="h-[calc(100dvh-412px)]"
        />
      ) : (
        <div className="h-[calc(100dvh-412px)] grid place-items-center border rounded-md">
          <Loader2 className="size-6 animate-spin text-primary" />
        </div>
      )}
    </div>
  );
};

export default TableWrapper;
