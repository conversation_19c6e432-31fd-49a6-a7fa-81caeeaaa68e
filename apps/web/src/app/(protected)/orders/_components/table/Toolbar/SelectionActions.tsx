import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Separator } from "@/components/ui/separator";
import { useConfirm } from "@/hooks/useConfirm";
import { useUpdateMultipleOrderStatusMutation } from "@/store/api/orderApi";
import { GetOrdersResponse } from "@/store/types/orders";
import { OrderStatus } from "@repo/database";
import { Table } from "@tanstack/react-table";
import { ChevronDown, EllipsisVertical, ReceiptText } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import MoveToBatchModal from "../modals/MoveToBatchModal";
import OneCallBuyModal from "../modals/OneCallBuyModal";

type SelectionActionsProps = {
  table: Table<GetOrdersResponse["data"][number]>;
  noOfSelectedRows: number;
};

const SelectionActions = ({
  table,
  noOfSelectedRows,
}: SelectionActionsProps) => {
  const { confirm, ConfirmModal } = useConfirm();
  const [isOpenOneCallModal, setIsOpenOneCallModal] = useState(false);

  const selectedOrderIds = table
    .getFilteredSelectedRowModel()
    .rows.map(row => row.original.id);

  const selectedRows = table.getSelectedRowModel().rows;
  const selectedData = selectedRows.map(item => item.original);

  const canCancel = selectedRows.every(
    row => row.original.status === OrderStatus.open
  );
  const canOneCallBuy = selectedRows.every(
    row => row.original.service && row.original.carrier
  );

  const [cancelOrders] = useUpdateMultipleOrderStatusMutation();

  const handleCancel = async () => {
    if (!canCancel) {
      return toast.error("Failed to cancel orders, These status can't cancel");
    }

    const confirmed = await confirm({
      title: "Cancel Orders",
      description: `Are you want to cancel ${selectedOrderIds.length} orders?`,
      confirmText: "Confirm",
      variant: "destructive",
    });

    if (!confirmed) return;
    if (selectedOrderIds.length === 0) return;

    try {
      const { count } = await cancelOrders({
        orderIds: selectedOrderIds,
        status: OrderStatus.cancelled,
      }).unwrap();
      table.setRowSelection({});
      toast.success(`Successfully cancelled ${count} orders`);
    } catch (error) {
      console.error("Failed to cancel orders", error);
      toast.error("Failed to cancel orders");
    }
  };

  return (
    <>
      <span className="text-sm font-semibold">{noOfSelectedRows} selected</span>
      <Separator orientation="vertical" className="mx-2" />
      <Button variant="accent">
        <ReceiptText /> Create label
      </Button>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="muted">
            <EllipsisVertical /> Actions
            <ChevronDown />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-56">
          <DropdownMenuGroup>
            {/* <DropdownMenuItem>
              Get Cheapest Rates
              <DropdownMenuShortcut>⌘R</DropdownMenuShortcut>
            </DropdownMenuItem> */}
            <DropdownMenuItem
              onClick={e => {
                e.preventDefault();
                e.stopPropagation();
              }}
            >
              <MoveToBatchModal
                selectedOrderIds={selectedOrderIds}
                onSuccess={() => table.setRowSelection({})}
              />
            </DropdownMenuItem>

            <DropdownMenuItem
              onClick={e => {
                e.preventDefault();
                e.stopPropagation();
                if (canOneCallBuy) setIsOpenOneCallModal(true);
                else toast.error("Service is required for all selected orders");
              }}
            >
              One-Call Buy
            </DropdownMenuItem>
          </DropdownMenuGroup>
          <DropdownMenuSeparator />
          <DropdownMenuGroup>
            <DropdownMenuLabel>Status</DropdownMenuLabel>
            {/* <DropdownMenuItem>Put on hold</DropdownMenuItem> */}
            <DropdownMenuItem variant="destructive" onClick={handleCancel}>
              Cancel
            </DropdownMenuItem>
          </DropdownMenuGroup>
          {/* <DropdownMenuSeparator /> */}
          {/* <DropdownMenuItem>
            <Pencil /> Edit
          </DropdownMenuItem> */}
        </DropdownMenuContent>
      </DropdownMenu>
      {/* <Button variant="muted">
        <Settings /> Configuration
      </Button> */}
      <ConfirmModal />

      {isOpenOneCallModal && (
        <OneCallBuyModal
          open={isOpenOneCallModal}
          onOpenChange={open => {
            setIsOpenOneCallModal(open);
            if (!open) {
              table.setRowSelection({});
            }
          }}
          orders={selectedData}
        />
      )}
    </>
  );
};

export default SelectionActions;
