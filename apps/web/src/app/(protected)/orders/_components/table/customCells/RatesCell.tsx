import { Button } from "@/components/ui/button";
import {
  useGenerateRatesWithOrderIdMutation,
  useOneCallBuyMutation,
} from "@/store/api/orderApi";
import { OrderStatus } from "@repo/database";
import { Loader2, ReceiptText, RefreshCcw } from "lucide-react";
import { toast } from "sonner";

type RatesCellProps = {
  orderId: string;
  epShipmentId?: string | null;
  service?: string | null;
  status: OrderStatus;
};

const RatesCell = ({
  orderId,
  epShipmentId,
  service,
  status,
}: RatesCellProps) => {
  const [oneCallBuy, { isLoading }] = useOneCallBuyMutation();
  const [generateRates, { isLoading: isGenerating }] =
    useGenerateRatesWithOrderIdMutation();

  const handleOneCallBuy = async (orderId: string) => {
    try {
      const data = await oneCallBuy(orderId).unwrap();
      console.log(data);
      toast.success("Successfully one call bought");
      console.log("open print label modal"); // TODO
    } catch (error) {
      console.error("Failed to one call buy", error);
      toast.error("Failed to one call buy");
    }
  };

  const handleGetRates = async (orderId: string) => {
    try {
      const data = await generateRates(orderId).unwrap();
      console.log(data);
      toast.success("Rates generated successfully");
    } catch (error) {
      console.log(error);
      toast.error("Failed to generate rates");
    }
  };

  if (status === "processing")
    return <div className="text-xs text-primary/60">Label purchased</div>;

  if (status === "cancelled")
    return <div className="text-xs text-primary/60">-</div>;

  if (epShipmentId)
    return <div className="text-xs text-primary/60">Rates fetched</div>;

  if (service)
    return (
      <Button
        variant="link"
        size="xs"
        className="px-0!"
        onClick={() => handleOneCallBuy(orderId)}
        disabled={isLoading}
      >
        {isLoading ? <Loader2 className="animate-spin" /> : <ReceiptText />}
        Buy label
      </Button>
    );

  return (
    <Button
      variant="link"
      size="xs"
      className="px-0!"
      onClick={() => handleGetRates(orderId)}
      disabled={isGenerating}
    >
      {isGenerating ? <Loader2 className="animate-spin" /> : <RefreshCcw />}
      Get rates
    </Button>
  );
};

export default RatesCell;
