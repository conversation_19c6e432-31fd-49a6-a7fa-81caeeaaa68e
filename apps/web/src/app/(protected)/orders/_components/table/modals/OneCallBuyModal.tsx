import { Button } from "@/components/ui/button";
import Header from "@/components/layout/Header";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import { getCarrierIcon } from "@/lib/mappings/carriers";
import { useOneCallBuyMutation } from "@/store/api/orderApi";
import { Shipment } from "@/types/Shipment/Shipment";
import { Order } from "@repo/database";
import { getCarrierBasicInfo, getServiceLevel } from "@repo/shared-data";
import { Loader2, Printer, ScrollTextIcon } from "lucide-react";
import Image from "next/image";
import { useState } from "react";

type OneCallBuyModalProps = {
  orders: Order[];
  open: boolean;
  onOpenChange: (open: boolean) => void;
};

type OrderStatus = {
  item: {
    id: string;
    orderNo: string;
    carrier: string;
    service: string;
    rate: string | null;
  };
  status: "idle" | "processing" | "success" | "failed";
  error: string | null;
  data: Shipment | null;
};

const OneCallBuyModal = ({
  orders,
  open,
  onOpenChange,
}: OneCallBuyModalProps) => {
  const [oneCallBuy] = useOneCallBuyMutation();
  const [isCompleted, setIsCompleted] = useState<boolean>(false);
  const [orderStatuses, setOrderStatuses] = useState<OrderStatus[]>(
    orders
      .filter(order => order.carrier && order.service)
      .map(order => ({
        item: {
          id: order.id,
          orderNo: order.orderNo,
          carrier: order.carrier as string,
          service: order.service as string,
          rate: null,
        },
        status: "idle",
        error: null,
        data: null,
      }))
  );

  const updateItemStatus = (
    id: string,
    status: "idle" | "processing" | "success" | "failed",
    data: Shipment | null,
    error: string | null
  ) => {
    setOrderStatuses(prev =>
      prev.map(order =>
        order.item.id === id ? { ...order, status, data, error } : order
      )
    );
  };

  const handleOneCallBuy = async () => {
    setOrderStatuses(prev => prev.map(p => ({ ...p, status: "processing" })));

    const promises = orderStatuses.map(async order => {
      try {
        const response = await oneCallBuy(order.item.id).unwrap();
        updateItemStatus(order.item.id, "success", response.shipment, null);
        return { data: response.shipment };
      } catch (error) {
        console.log(error);

        return updateItemStatus(
          order.item.id,
          "failed",
          null,
          "Failed to one call buy for order"
        );
      }
    });

    await Promise.allSettled(promises);
    setIsCompleted(true);
  };

  const handleDialogOpenChange = (isOpen: boolean) => {
    onOpenChange(isOpen);
    if (!isOpen) {
      setIsCompleted(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleDialogOpenChange}>
      <DialogContent className="md:min-w-2xl xl:gap-y-5">
        <DialogTitle className="hidden">One Call Buy</DialogTitle>
        <DialogDescription className="hidden">
          One Call Buy Description
        </DialogDescription>
        <DialogTitle className="hidden" />
        <DialogDescription className="hidden" />
        <DialogHeader>
          <Header icon={ScrollTextIcon} as="h3">
            One-Call Buy
          </Header>
        </DialogHeader>
        <Separator />

        {orderStatuses.map(order => {
          return (
            <ServiceCard
              key={order.item.id}
              item={order.item}
              data={order.data}
              status={order.status}
            />
          );
        })}

        <Separator />
        <DialogFooter className="w-full">
          {isCompleted ? (
            <>
              <Button
                type="button"
                variant="ghost-destructive"
                size="lg"
                className="md:w-1/2"
                onClick={() => handleDialogOpenChange(false)}
              >
                Print Later
              </Button>
              <Button type="button" size="lg" className="md:w-1/2">
                <Printer />
                Print Labels
              </Button>
            </>
          ) : (
            <>
              <Button
                type="button"
                variant="ghost-destructive"
                size="lg"
                className="md:w-1/2"
                onClick={() => handleDialogOpenChange(false)}
              >
                Cancel
              </Button>
              <Button
                type="button"
                size="lg"
                className="md:w-1/2"
                onClick={handleOneCallBuy}
              >
                Confirm
              </Button>
            </>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

type ServiceCardProps = {
  item: {
    id: string;
    orderNo: string;
    carrier: string;
    service: string;
  };
  data: Shipment | null;
  status: "idle" | "processing" | "success" | "failed";
};

const ServiceCard = ({ item, data, status }: ServiceCardProps) => {
  const carrier = getCarrierBasicInfo(item.carrier);
  const service = getServiceLevel(item.carrier, item.service ?? "");
  const carrierIcon = getCarrierIcon(item.carrier ?? "");

  return (
    <div className="bg-muted py-3 px-4 rounded-md">
      <div className="text-sm font-semibold">{item.orderNo}</div>

      <div className="mt-2">
        {!carrier || !service || !carrierIcon ? (
          <div>Unavailable</div>
        ) : (
          <div className="flex justify-between">
            {carrier && service && (
              <div className="flex items-center gap-2 lg:gap-3">
                <Image
                  src={carrierIcon}
                  alt={carrier.fullName}
                  width={48}
                  height={48}
                />
                <div>
                  <div className="xl:text-lg font-semibold leading-snug">
                    {carrier.shortName}
                  </div>
                  <div className="text-muted-foreground text-xs">
                    {service.label}
                  </div>
                </div>
              </div>
            )}

            {status === "processing" && (
              <Loader2 className="animate-spin text-primary" />
            )}

            {status === "success" && data?.rate && (
              <div className="flex flex-col justify-end items-end">
                <div className="font-semibold">$ {data?.rate}</div>
                <div className="text-xs text-accent">Label created</div>
              </div>
            )}

            {status === "failed" && (
              <div className="flex items-end">
                <div className="text-xs text-destructive">Failed</div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default OneCallBuyModal;
