import { <PERSON><PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import styles from "@/styles/Dashboard.module.css";
import { ClipboardDocumentListIcon } from "@heroicons/react/24/outline";
import { ClipboardList } from "lucide-react";
import Link from "next/link";
import PageHeader from "../../../components/layout/PageHeader";
import Table from "./_components/table/Table";
import CreateNewBatchWrapper from "./batches/_components/CreateNewBatchWrapper";

export default function OrdersPage() {
  return (
    <div
      className={cn(
        styles.pagePadding,
        "flex flex-col justify-between gap-8 lg:gap-10"
      )}
    >
      <div>
        <PageHeader
          header="Orders"
          icon={ClipboardList}
          description={
            <p>
              Lets create your orders! Please fill in the details to get the
              best shipping rate possible.{" "}
              <Link
                href="/support"
                className="text-primary font-medium hover:underline underline-offset-2"
              >
                See tutorials.
              </Link>
            </p>
          }
        />
        <div className="mt-4 flex gap-3 items-center flex-wrap">
          <Button size="lg" asChild>
            <Link href="/orders/create">
              <ClipboardList />
              Create Order
            </Link>
          </Button>
          <CreateNewBatchWrapper>
            <Button size="lg">
              <ClipboardDocumentListIcon />
              Create Batch Order
            </Button>
          </CreateNewBatchWrapper>
        </div>
      </div>

      <Table />
    </div>
  );
}
