import PageHeader from "@/components/layout/PageHeader";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import styles from "@/styles/Dashboard.module.css";
import { ClipboardDocumentListIcon } from "@heroicons/react/24/outline";
import { Plus } from "lucide-react";
import Link from "next/link";
import CreateNewBatchWrapper from "./_components/CreateNewBatchWrapper";
import Table from "./_components/table/Table";
import UploadXlsxModal from "./_components/UploadXlsxModal";

const BatchesPage = () => {
  return (
    <div className={cn(styles.pagePadding, "flex flex-col gap-8 lg:gap-10")}>
      <div>
        <PageHeader
          header="Create Batch Orders"
          icon={ClipboardDocumentListIcon}
          description={
            <p>
              Use our template to fill out your batch orders. Click here to
              download the{" "}
              <Link
                href=""
                className="text-primary font-medium hover:underline underline-offset-2"
              >
                domestic template
              </Link>{" "}
              or download the{" "}
              <Link
                href=""
                className="text-primary font-medium hover:underline underline-offset-2"
              >
                international template.
              </Link>
            </p>
          }
        />
        <div className="mt-4 flex gap-3 items-center flex-wrap">
          {/* Creates a new batch, gets the batch id and redirect to the batch page */}
          <CreateNewBatchWrapper>
            <Button size="lg">
              <Plus />
              New Batch
            </Button>
          </CreateNewBatchWrapper>
          <UploadXlsxModal />
        </div>
      </div>

      <Table />
    </div>
  );
};

export default BatchesPage;
