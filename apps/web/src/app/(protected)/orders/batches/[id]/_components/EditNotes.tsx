"use client";

import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { useUpdateBatchMutation } from "@/store/api/batchApi";
import { Loader2, Pencil } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

type EditNotesProps = {
  id: string;
  notes: string | null;
};

const EditNotes = ({ id, notes }: EditNotesProps) => {
  const [batchNotes, setBatchNotes] = useState({
    original: notes || "",
    edited: notes || "",
  });
  const [isEditing, setIsEditing] = useState(false);

  const [updateBatch, { isLoading }] = useUpdateBatchMutation();

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) =>
    setBatchNotes(prev => ({ ...prev, edited: e.target.value }));

  const handleSubmit = async (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();

    if (notes === batchNotes.edited) {
      setIsEditing(false);
      return;
    }

    try {
      await updateBatch({
        id,
        data: { notes: batchNotes.edited },
      }).unwrap();

      setBatchNotes(prev => ({ ...prev, original: batchNotes.edited }));
      toast.success("Notes updated");
    } catch (error) {
      console.log(error);
    } finally {
      setIsEditing(false);
    }
  };

  return (
    <div className="pl-9 max-w-md">
      {isEditing ? (
        <div>
          <Textarea
            value={batchNotes.edited}
            onChange={handleChange}
            className="text-xs! max-h-40 h-auto"
          />
          <div className="p-2 flex justify-end gap-2">
            <Button
              type="button"
              size="xs"
              variant="ghost-destructive"
              onClick={() => setIsEditing(false)}
            >
              Cancel
            </Button>
            <Button
              type="button"
              size="xs"
              disabled={isLoading}
              onClick={handleSubmit}
            >
              {isLoading && <Loader2 className="animate-spin" />}Save
            </Button>
          </div>
        </div>
      ) : (
        <div className="text-muted-foreground text-xs leading-normal group">
          <span className="text-primary font-medium">Notes:</span>{" "}
          {batchNotes.original}{" "}
          <div className="relative ml-0.5 size-2 inline-block">
            <Button
              size="icon-xs"
              variant="ghost-primary"
              className="absolute top-1/2 -translate-y-1/2 -left-1/2 size-4 ml-0.5 opacity-0  group-hover:opacity-100"
              onClick={() => setIsEditing(true)}
            >
              <Pencil />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default EditNotes;
