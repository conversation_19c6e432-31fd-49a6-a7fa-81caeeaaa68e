import Status from "@/components/common/Status";
import { Button } from "@/components/ui/button";
import { BatchWithOrders } from "@/store/types/batch";
import { BatchStatus, OrderStatus } from "@repo/database";
import { format } from "date-fns";
import { ChevronLeft } from "lucide-react";
import Link from "next/link";
import EditName from "./EditName";
import EditNotes from "./EditNotes";
import SettingsDropdown from "./SettingsDropdown";

type HeaderProps = {
  batch: BatchWithOrders;
};

const Header = ({ batch }: HeaderProps) => {
  const { id, name, batchNo, createdAt, notes, status, orders } = batch;
  const orderCount = orders.length;

  const canMark =
    orders?.length > 0 &&
    orders.every(
      order =>
        order.status === OrderStatus.processing &&
        batch.status === BatchStatus.open
    );

  return (
    <div className="flex flex-col md:flex-row gap-8 justify-between items-start">
      {/* Left */}
      <div className="w-full flex flex-col gap-2 ">
        <div className="flex gap-1">
          <Link href="/orders/batches">
            <Button size="icon-sm" variant="ghost">
              <ChevronLeft />
            </Button>
          </Link>
          <EditName id={id} name={name} />
        </div>
        <EditNotes id={id} notes={notes} />
      </div>
      {/* Right */}
      <div className="flex flex-col items-end gap-4 w-full md:max-w-88">
        <div className="w-full rounded-md bg-muted p-4 text-sm space-y-1.5">
          <div className="flex justify-between items-center gap-2">
            <div className="font-medium">Date Created:</div>
            <div>{format(createdAt, "MM/dd/yyyy")}</div>
          </div>
          <div className="flex justify-between items-center gap-2">
            <div className="font-medium">Status:</div>
            <div>
              <Status text={status} />
            </div>
          </div>
          <div className="flex justify-between items-center gap-2">
            <div className="font-medium">Batch No:</div>
            <div>{batchNo}</div>
          </div>
          <div className="flex justify-between items-center gap-2">
            <div className="font-medium">No. of Orders:</div>
            <div>{orderCount}</div>
          </div>
        </div>
        <SettingsDropdown
          id={id}
          canCancel={status === BatchStatus.open}
          canArchive={status === BatchStatus.processed}
          canMark={canMark}
        />
      </div>
    </div>
  );
};

export default Header;
