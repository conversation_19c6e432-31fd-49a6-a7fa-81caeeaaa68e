import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { FindAllOrder } from "@/types/Order/Order";
import { ColumnDef } from "@tanstack/react-table";
import { EllipsisVertical, Undo } from "lucide-react";

export const createOrderActions = ({
  handleRemoveFromBatch,
}: {
  handleRemoveFromBatch: (id: string) => void;
}): ColumnDef<FindAllOrder>[] => [
  {
    id: "actions",
    cell: ({ row }) => {
      const order = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon-xs">
              <span className="sr-only">Open menu</span>
              <EllipsisVertical />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              variant="destructive"
              onClick={() => handleRemoveFromBatch(order.id)}
            >
              <Undo />
              Remove from batch
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];
