import { getBatchById } from "@/data/batch";
import { cn } from "@/lib/utils";
import styles from "@/styles/Dashboard.module.css";
import Table from "./_components/table/Table";
import Header from "./_components/Header";

type BatchDetailsPageProps = {
  params: Promise<{ id: string }>;
};

const BatchDetailsPage = async ({ params }: BatchDetailsPageProps) => {
  const { id } = await params;

  const batch = await getBatchById(id);

  if (!batch) {
    // TODO: Create not found page and use notFound()
    return <div>Batch not found</div>;
  }

  return (
    <div className={cn(styles.pagePadding, "relative")}>
      <Header batch={batch} />
      <Table orders={batch.orders} />
    </div>
  );
};

export default BatchDetailsPage;
