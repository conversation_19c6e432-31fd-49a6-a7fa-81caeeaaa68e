"use client";

import DataTable from "@/components/common/table/DataTable";
import { createOrderColumns } from "@/app/(protected)/orders/_components/table/useColumns";
import { usePatchOrderMutation } from "@/store/api/orderApi";
import { FindAllOrder } from "@/types/Order/Order";
import { getCoreRowModel, useReactTable } from "@tanstack/react-table";
import { useCallback, useMemo } from "react";
import { toast } from "sonner";
import Toolbar from "./Toolbar";
import { createOrderActions } from "./useColumns";

const TableWrapper = ({ orders }: { orders: FindAllOrder[] }) => {
  const [patchOrder] = usePatchOrderMutation();

  const handleRemoveFromBatch = useCallback(
    async (orderId: string) => {
      try {
        const patchedOrder = await patchOrder({
          id: orderId,
          data: { batchId: null },
        }).unwrap();

        toast.success(`Order ${patchedOrder.orderNo} removed from batch`);
      } catch (error) {
        if (process.env.NODE_ENV === "development") console.error(error);
        toast.error("Failed to remove order from batch");
      }
    },
    [patchOrder]
  );

  const columns = useMemo(
    () => [
      ...createOrderColumns(),
      ...createOrderActions({ handleRemoveFromBatch }),
    ],
    [handleRemoveFromBatch]
  );

  const table = useReactTable({
    data: orders,
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  return (
    <div className="mt-8 lg:mt-10 w-full max-w-full flex flex-col">
      <Toolbar table={table} />
      <DataTable table={table} height="h-[calc(100dvh-461px)]" />
    </div>
  );
};

export default TableWrapper;
