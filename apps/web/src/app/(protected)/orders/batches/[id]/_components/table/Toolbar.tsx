import OneCallBuyModal from "@/app/(protected)/orders/_components/table/modals/OneCallBuyModal";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Separator } from "@/components/ui/separator";
import { usePatchOrderMutation } from "@/store/api/orderApi";
import { FindAllOrder } from "@/types/Order/Order";
import { OrderStatus } from "@repo/database";
import { Table } from "@tanstack/react-table";
import {
  ChevronDown,
  EllipsisVertical,
  ReceiptText,
  Settings,
} from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

type ToolbarProps = {
  table: Table<FindAllOrder>;
};

const Toolbar = ({ table }: ToolbarProps) => {
  const [isOpenOneCallModal, setIsOpenOneCallModal] = useState(false);
  const noOfSelectedRows = table.getFilteredSelectedRowModel().rows.length;
  const selectedOrder = table
    .getFilteredSelectedRowModel()
    .rows.map(row => row.original);

  const [patchOrder] = usePatchOrderMutation();

  const canOneCallBuy = selectedOrder.every(
    order => order.service && order.carrier
  );

  const handleRemoveFromBatch = async (data?: {
    batchId: string | null;
    status?: string;
  }) => {
    const isCancelling = data?.status === OrderStatus.cancelled;
    const canCancel = selectedOrder.every(
      order => order.status !== OrderStatus.open
    );

    if (isCancelling && !canCancel) {
      return toast.error(
        "Failed: All orders must have status of 'open' to cancel."
      );
    }

    const promises = selectedOrder.map(async order => {
      try {
        await patchOrder({
          id: order.id,
          data: {
            ...data,
          },
        }).unwrap();
        toast.success("Removed order from batch successfully");
      } catch (error) {
        if (process.env.NODE_ENV === "development") console.error(error);
        toast.error("Failed to remove order from batch");
      }
    });

    await Promise.allSettled(promises);
  };

  return (
    <div className="flex items-center justify-between gap-4 py-4">
      {/* LEFT */}
      <div className="flex items-center gap-2 h-8">
        {noOfSelectedRows > 0 ? (
          <>
            <span className="text-sm font-semibold">
              {noOfSelectedRows} selected
            </span>
            <Separator orientation="vertical" className="mx-2" />
            <Button variant="accent">
              <ReceiptText /> Create label
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="muted">
                  <EllipsisVertical /> Actions
                  <ChevronDown />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56">
                <DropdownMenuGroup>
                  {/* <DropdownMenuItem>
                    Get Cheapest Rates
                    <DropdownMenuShortcut>⌘R</DropdownMenuShortcut>
                  </DropdownMenuItem> */}
                  {canOneCallBuy ? (
                    <DropdownMenuItem
                      onClick={e => {
                        e.preventDefault();
                        e.stopPropagation();
                        setIsOpenOneCallModal(true);
                      }}
                    >
                      One-Call Buy
                    </DropdownMenuItem>
                  ) : (
                    <DropdownMenuItem
                      onClick={() => toast.error("Failed, Service is required")}
                    >
                      One-Call Buy
                    </DropdownMenuItem>
                  )}
                  <DropdownMenuItem
                    onClick={() =>
                      handleRemoveFromBatch({
                        batchId: null,
                      })
                    }
                    variant="destructive"
                  >
                    Remove from batch
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    variant="destructive"
                    onClick={() =>
                      handleRemoveFromBatch({
                        batchId: null,
                        status: "cancelled",
                      })
                    }
                  >
                    Cancel and remove from batch
                  </DropdownMenuItem>
                </DropdownMenuGroup>
              </DropdownMenuContent>
            </DropdownMenu>
            <Button variant="muted">
              <Settings /> Configuration
            </Button>
          </>
        ) : (
          <Button variant="accent">
            <ReceiptText /> Create batch labels
          </Button>
        )}
      </div>

      {isOpenOneCallModal && (
        <OneCallBuyModal
          open={isOpenOneCallModal}
          onOpenChange={open => {
            setIsOpenOneCallModal(open);
            if (!open) {
              table.setRowSelection({});
            }
          }}
          orders={selectedOrder}
        />
      )}
    </div>
  );
};

export default Toolbar;
