"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useConfirm } from "@/hooks/useConfirm";
import {
  useArchiveBatchMutation,
  useCancelBatchMutation,
  useUpdateBatchMutation,
} from "@/store/api/batchApi";
import { Archive, BookMarkedIcon, Settings, X } from "lucide-react";
import { BookmarkIcon } from "@heroicons/react/24/solid";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

type SettingsDropdownProps = {
  id: string;
  canCancel: boolean;
  canArchive: boolean;
  canMark: boolean;
};

const SettingsDropdown = ({
  id,
  canCancel,
  canArchive,
  canMark,
}: SettingsDropdownProps) => {
  const { confirm, ConfirmModal } = useConfirm();
  const router = useRouter();

  const [cancelBatch] = useCancelBatchMutation();
  const [archiveBatch] = useArchiveBatchMutation();
  const [updateBatch] = useUpdateBatchMutation();

  const handleCancelBatch = async () => {
    const confirmed = await confirm({
      title: "Cancel Batch",
      description:
        "Cancelling this batch will remove all orders from the batch. The orders themselves will remain active.",
      confirmText: "Cancel Batch",
      cancelText: "Cancel",
      variant: "destructive",
      icon: <X />,
    });
    if (!confirmed) return;

    try {
      await cancelBatch(id).unwrap();
      toast.success("Batch canceled successfully");
      router.push("/orders/batches");
    } catch (error) {
      if (process.env.NODE_ENV === "development") console.error(error);
      toast.error("Failed to cancel batch");
    }
  };

  const handleArchiveBatch = async () => {
    const confirmed = await confirm({
      title: "Archive Batch",
      description:
        "Once archived, you can no longer perform batch actions, such as printing labels.",
      confirmText: "Archive Batch",
      cancelText: "Cancel",
      variant: "destructive",
      icon: <X />,
    });
    if (!confirmed) return;

    try {
      await archiveBatch({ id }).unwrap();
      toast.success("Batch archived successfully");
      router.push("/orders/batches");
    } catch (error) {
      if (process.env.NODE_ENV === "development") console.error(error);
      toast.error("Failed to archive batch");
    }
  };

  const handleMarkProcessBatch = async () => {
    const confirmed = await confirm({
      title: "Mark batch as processed",
      description:
        "All orders in this batch are processed. Marking as processed will update the batch status to processed.",
      confirmText: "Mark Batch",
      cancelText: "Cancel",
      variant: "default",
      icon: <BookMarkedIcon />,
    });
    if (!confirmed) return;

    try {
      await updateBatch({
        id,
        data: { status: "processed" },
      }).unwrap();
      toast.success("Batch marked successfully");
    } catch (error) {
      if (process.env.NODE_ENV === "development") console.error(error);
      toast.error("Failed to marked batch");
    }
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button size="xs" variant="ghost">
            <Settings /> Settings
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem
            variant="destructive"
            onClick={handleCancelBatch}
            disabled={!canCancel}
          >
            <X /> Cancel Batch
          </DropdownMenuItem>
          <DropdownMenuItem
            variant="destructive"
            onClick={handleArchiveBatch}
            disabled={!canArchive}
          >
            <Archive /> Archive Batch
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={handleMarkProcessBatch}
            disabled={!canMark}
          >
            <BookmarkIcon /> Mark as processed
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <ConfirmModal />
    </>
  );
};

export default SettingsDropdown;
