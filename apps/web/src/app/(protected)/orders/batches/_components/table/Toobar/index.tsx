import QuickSearch from "@/components/common/table/toolbar/QuickSearch";
import ViewOptions from "@/components/common/table/toolbar/ViewOptions";
import StatusSelect from "@/app/(protected)/orders/batches/_components/table/Toobar/StatusSelect";
import { Table } from "@tanstack/react-table";
import SelectionActions from "./SelectionActions";
import { BatchesWithOrderCount } from "@/types/Batch/Batch";

type ToolbarProps = {
  table: Table<BatchesWithOrderCount>;
  setSearch: React.Dispatch<React.SetStateAction<string>>;
};

const Toolbar = ({ table, setSearch }: ToolbarProps) => {
  const noOfSelectedRows = table.getFilteredSelectedRowModel().rows.length;

  return (
    <div className="flex md:items-center md:justify-between gap-4 md:flex-row flex-col">
      {/* LEFT */}
      <div className="flex items-center gap-2 h-8">
        {noOfSelectedRows > 0 ? (
          <SelectionActions table={table} />
        ) : (
          <QuickSearch setSearch={setSearch} />
        )}
      </div>
      {/* RIGHT */}
      <div className="flex items-center gap-2 self-end">
        <ViewOptions table={table} />
        <StatusSelect table={table} />
      </div>
    </div>
  );
};

export default Toolbar;
