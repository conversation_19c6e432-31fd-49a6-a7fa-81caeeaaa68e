"use client";

import DataTable from "@/components/common/table/DataTable";
import SkeletonTable from "@/components/loading/SkeletonTable";
import { createPaginationHandler } from "@/lib/utils/table/pagination";
import {
  useArchiveBatchMutation,
  useGetBatchesQuery,
} from "@/store/api/batchApi";
import { Pagination } from "@/types";
import { BatchesWithOrderCount } from "@/types/Batch/Batch";
import { BatchStatus } from "@repo/database";
import {
  ColumnDef,
  ColumnFilter,
  ColumnFiltersState,
  getCoreRowModel,
  OnChangeFn,
  PaginationState,
  Updater,
  useReactTable,
  VisibilityState,
} from "@tanstack/react-table";
import { Loader2 } from "lucide-react";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useMemo, useState } from "react";
import { toast } from "sonner";
import Toolbar from "./Toobar";
import { createBatchActions, createBatchColumns } from "./useColumns";

const PAGE_SIZE_OPTIONS = [50, 100, 200];
const DEFAULT_PAGE_SIZE = PAGE_SIZE_OPTIONS[0];
const DEFAULT_PAGE_INDEX = 0;

const TableWrapper = () => {
  const [search, setSearch] = useState("");
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: DEFAULT_PAGE_INDEX,
    pageSize: DEFAULT_PAGE_SIZE,
  });
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([
    { id: "status", value: BatchStatus.open },
  ]);

  const queryStr = useMemo(() => {
    const query = new URLSearchParams();
    // Pagination
    query.append("limit", pagination.pageSize.toString());
    query.append(
      "offset",
      (pagination.pageIndex * pagination.pageSize).toString()
    );

    // Filters
    columnFilters.forEach((filter: ColumnFilter) => {
      query.append(`filter[${filter.id}]`, `${filter.value}`);
    });
    // Search
    if (search.trim()) query.append("search", search);

    return query.toString();
  }, [pagination, columnFilters, search]);

  const { data: batches, isLoading, isFetching } = useGetBatchesQuery(queryStr);
  const [archiveBatch] = useArchiveBatchMutation();

  const { push } = useRouter();

  const handleEditBatch = useCallback(
    (id: string) => push(`/orders/batches/${id}`),
    [push]
  );

  const handleArchiveBatch = useCallback(
    async (id: string) => {
      try {
        await archiveBatch({ id }).unwrap();
        toast.success("Batch archived successfully");
      } catch (error) {
        toast.error("Failed to archived batch");
        console.error(error);
      }
    },
    [archiveBatch]
  );

  const columns = useMemo(
    () => [
      ...createBatchColumns(),
      ...createBatchActions({
        handleEditBatch,
        handleArchiveBatch,
      }),
    ],
    [handleEditBatch, handleArchiveBatch]
  );

  const [mounted, setMounted] = useState(false);
  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  return (
    <>
      {!batches || !columns || isLoading ? (
        <SkeletonTable advancedSearch={false} />
      ) : (
        <Table
          isFetching={isFetching}
          data={batches}
          columns={columns}
          pagination={pagination}
          setPagination={setPagination}
          columnFilters={columnFilters}
          setColumnFilters={setColumnFilters}
          setSearch={setSearch}
        />
      )}
    </>
  );
};

type TableProps = {
  isFetching: boolean;
  data: {
    data: BatchesWithOrderCount[];
    pagination: Pagination;
  };
  columns: ColumnDef<BatchesWithOrderCount>[];
  pagination: PaginationState;
  setPagination: React.Dispatch<React.SetStateAction<PaginationState>>;
  columnFilters: ColumnFiltersState;
  setColumnFilters: React.Dispatch<React.SetStateAction<ColumnFiltersState>>;
  setSearch: React.Dispatch<React.SetStateAction<string>>;
};

const Table = ({
  isFetching,
  data,
  columns,
  pagination,
  setPagination,
  columnFilters,
  setColumnFilters,
  setSearch,
}: TableProps) => {
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});

  const onPaginationChange = useCallback(
    (updater: Updater<PaginationState>) =>
      createPaginationHandler(
        data.pagination,
        pagination,
        setPagination
      )(updater),
    [data.pagination, pagination, setPagination]
  );

  const table = useReactTable({
    data: data.data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    // Pagination
    manualPagination: true,
    rowCount: data.pagination.total,
    onPaginationChange: onPaginationChange as OnChangeFn<PaginationState>,
    // Filtering
    manualFiltering: true,
    onColumnFiltersChange: setColumnFilters as OnChangeFn<ColumnFiltersState>,
    // Column Visibility Settings
    onColumnVisibilityChange: setColumnVisibility,
    state: {
      pagination,
      columnFilters,
      columnVisibility,
    },
  });

  return (
    <div className="flex flex-col gap-4">
      <Toolbar table={table} setSearch={setSearch} />

      {!isFetching ? (
        <DataTable
          table={table}
          pageSizeOptions={PAGE_SIZE_OPTIONS}
          height="h-[calc(100dvh-412px)]"
        />
      ) : (
        <div className="h-[calc(100dvh-412px)] grid place-items-center border rounded-md">
          <Loader2 className="size-6 animate-spin text-primary" />
        </div>
      )}
    </div>
  );
};

export default TableWrapper;
