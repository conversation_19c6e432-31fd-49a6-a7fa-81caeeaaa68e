import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Separator } from "@/components/ui/separator";
import { useConfirm } from "@/hooks/useConfirm";
import { useCancelBatchMutation } from "@/store/api/batchApi";
import { BatchesWithOrderCount } from "@/types/Batch/Batch";
import { BatchStatus } from "@repo/database";
import { Table } from "@tanstack/react-table";
import { ChevronDown, EllipsisVertical, X } from "lucide-react";
import { toast } from "sonner";

type SelectionActionsProps = {
  table: Table<BatchesWithOrderCount>;
};

const SelectionActions = ({ table }: SelectionActionsProps) => {
  const { confirm, ConfirmModal } = useConfirm();
  const [cancelBatch] = useCancelBatchMutation();

  const selectedBatchIds = table
    .getFilteredSelectedRowModel()
    .rows.map(row => row.original.id);

  const noOfSelectedRows = selectedBatchIds.length;

  const selectedRows = table.getSelectedRowModel().rows;
  const canCancel = selectedRows.every(
    row => row.original.status === BatchStatus.open
  );

  const handleCancelBatch = async () => {
    if (!canCancel) {
      return toast.error("Failed to cancel orders, These status can't cancel");
    }

    const batchCounts = selectedRows.reduce(
      (acc, row) => {
        if (row.original._count.orders > 0) {
          acc.nonEmpty++;
        }
        acc.total++;
        return acc;
      },
      { nonEmpty: 0, total: 0 }
    );

    const confirmed = await confirm({
      title: "Cancel Batch",
      description: `Are you sure you want to cancel? ${batchCounts.nonEmpty} out of ${batchCounts.total} batches still contain orders.`,
      confirmText: "Cancel Batch",
      cancelText: "Cancel",
      variant: "destructive",
      icon: <X />,
    });
    if (!confirmed) return;

    const promises = selectedBatchIds.map(async batchId => {
      try {
        const response = await cancelBatch(batchId).unwrap();
        console.log(response);
        toast.success("Batch canceled successfully");
        table.setRowSelection({});
        return { data: response };
      } catch (error) {
        if (process.env.NODE_ENV === "development") console.error(error);
        toast.error("Failed to cancel batch");
      }
    });

    await Promise.allSettled(promises);
  };

  return (
    <>
      <span className="text-sm font-semibold">{noOfSelectedRows} selected</span>
      <Separator orientation="vertical" className="mx-2" />
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="muted">
            <EllipsisVertical /> Actions
            <ChevronDown />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-56">
          <DropdownMenuGroup>
            <DropdownMenuLabel>Status</DropdownMenuLabel>

            <DropdownMenuItem variant="destructive" onClick={handleCancelBatch}>
              Cancel
            </DropdownMenuItem>
          </DropdownMenuGroup>
        </DropdownMenuContent>
      </DropdownMenu>

      <ConfirmModal />
    </>
  );
};

export default SelectionActions;
