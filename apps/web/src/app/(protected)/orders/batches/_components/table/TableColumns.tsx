import CopyTextWrapper from "@/components/common/CopyTextWrapper";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Batch } from "@repo/database";
import { ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";
import { ArrowUpDown, EllipsisVertical, Pencil, X } from "lucide-react";
import Link from "next/link";

export const createBatchColumns = (): ColumnDef<Batch>[] => [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={value => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={value => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "createdAt",
    header: ({ column }) => {
      return (
        <Button
          variant="link-foreground"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Created Date <ArrowUpDown />
        </Button>
      );
    },
    cell: ({ row }) => (
      <div>{format(row.getValue("createdAt"), "MM/dd/yyyy")}</div>
    ),
  },

  {
    accessorKey: "batchNo",
    header: "Batch No",
    cell: ({ row }) => (
      <Link
        href={`/orders/batches/${row.original.id}`}
        className="hover:text-primary hover:underline underline-offset-2"
      >
        {row.original.batchNo}
      </Link>
    ),
  },
  { accessorKey: "name", header: "Name" },
  { accessorKey: "notes", header: "Notes" },
];

type CreateBatchActionsProps = {
  handleEditBatch: (id: string) => void;
};

export const createBatchActions = ({
  handleEditBatch,
}: CreateBatchActionsProps): ColumnDef<Batch>[] => [
  {
    id: "actions",
    cell: ({ row }) => {
      const batch = row.original;
      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon-xs">
              <span className="sr-only">Open menu</span>
              <EllipsisVertical />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <CopyTextWrapper text={batch.batchNo} isTable={true}>
              <DropdownMenuItem>Copy Batch No.</DropdownMenuItem>
            </CopyTextWrapper>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => handleEditBatch(batch.id)}>
              <Pencil />
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem variant="destructive">
              <X />
              Cancel Batch
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];
