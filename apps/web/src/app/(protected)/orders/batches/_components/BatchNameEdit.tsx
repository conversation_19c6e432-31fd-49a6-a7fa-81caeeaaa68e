"use client";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { useUpdateBatchMutation } from "@/store/api/batchApi";
import { Loader2, Pencil, Save } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { toast } from "sonner";
import { z } from "zod";

type BatchNameEditProps = {
  id: string;
  name: string;
};

const batchNameSchema = z.object({
  edited: z.string().min(1, { message: "Batch name is required" }),
});

const BatchNameEdit = ({ id, name }: BatchNameEditProps) => {
  const [batchName, setBatchName] = useState({ original: name, edited: name });
  const [isEditing, setIsEditing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [updateBatch, { isLoading }] = useUpdateBatchMutation();

  const formRef = useRef<HTMLFormElement>(null);

  useEffect(() => {
    if (!isEditing) return;

    const handleClickOutside = (e: MouseEvent) => {
      if (!formRef.current?.contains(e.target as Node)) {
        setIsEditing(false);
        setBatchName(prev => ({ ...prev, edited: prev.original }));
        setError(null);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [isEditing]);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setError(null);

    // If the batch name is the same as the original, don't update
    if (batchName.edited === batchName.original) return;

    const { success, data, error } = batchNameSchema.safeParse(batchName);
    if (!success) {
      setError(error.issues[0].message);
      toast.error(error.issues[0].message);
      return;
    }

    try {
      const response = await updateBatch({
        id,
        data: { name: data.edited },
      }).unwrap();

      setBatchName({ original: response.name, edited: response.name });
      toast.success("Batch name updated");
    } catch (error) {
      console.log(error);
      toast.error("Failed to update batch name");
    } finally {
      setIsEditing(false);
    }
  };

  if (isEditing) {
    return (
      <div className="flex items-center font-semibold text-lg gap-1">
        Batch -
        <form
          ref={formRef}
          onSubmit={handleSubmit}
          className="flex items-center gap-2"
        >
          <Input
            value={batchName.edited}
            onChange={e =>
              setBatchName({ ...batchName, edited: e.target.value })
            }
            className={cn("min-w-[250px]")}
            aria-invalid={!!error}
            autoFocus
          />
          <Button type="submit" disabled={isLoading}>
            {isLoading ? (
              <Loader2 className="size-4 animate-spin" />
            ) : (
              <Save className="h-4 w-4" />
            )}
            Save
          </Button>
        </form>
      </div>
    );
  }

  return (
    <div className="flex items-center font-semibold text-lg gap-1">
      Batch - {batchName.original}
      <Button
        size="icon-sm"
        variant="ghost-primary"
        className="ml-1"
        onClick={() => setIsEditing(true)}
      >
        <Pencil className="h-4 w-4" />
      </Button>
    </div>
  );
};

export default BatchNameEdit;
