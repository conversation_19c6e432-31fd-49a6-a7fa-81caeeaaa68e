"use client";

import { cn } from "@/lib/utils";
import { useCreateBatchMutation } from "@/store/api/batchApi";
import { useRouter } from "next/navigation";
import { useEffect, useRef } from "react";

const CreateNewBatchWrapper = ({
  className = "",
  children,
}: {
  className?: string;
  children: React.ReactElement<HTMLButtonElement>;
}) => {
  const [createBatch, { isLoading }] = useCreateBatchMutation();

  const { push } = useRouter();

  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!ref.current) return;

    const button = ref.current.getElementsByTagName("button")[0];
    if (!button) throw new Error("This wrapper is for buttons only!!!!!");

    if (isLoading) button.disabled = true;
    else button.disabled = false;
  }, [isLoading]);

  const handleCreateBatch = async () => {
    try {
      const data = await createBatch().unwrap();

      if (!data?.id) throw new Error("Failed to create batch");
      push(`/orders/batches/${data?.id}`);
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <div className={cn(className)} ref={ref} onClick={handleCreateBatch}>
      {children}
    </div>
  );
};

export default CreateNewBatchWrapper;
