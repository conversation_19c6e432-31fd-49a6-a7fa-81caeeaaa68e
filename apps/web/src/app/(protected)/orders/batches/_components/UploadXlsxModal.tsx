"use client";

import FileUpload from "@/components/common/FileUpload";
import { Button } from "@/components/ui/button";
import Header from "@/components/layout/Header";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  <PERSON><PERSON>T<PERSON>le,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useUploadBatchMutation } from "@/store/api/batchApi";
import { orderApi } from "@/store/api/orderApi";
import { useAppDispatch } from "@/store/hooks";
import { ClipboardDocumentListIcon } from "@heroicons/react/24/outline";
import { AlertTriangle, Check, FileUp, Loader2 } from "lucide-react";
import { useState } from "react";

type UploadResult = {
  errors: string[];
  failed: number;
  successful: number;
  total: number;
};

const MAX_FILE_NUM = 1;

const UploadXlsxModal = () => {
  const [open, setOpen] = useState(false);
  const [files, setFiles] = useState<File[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [uploadResult, setUploadResult] = useState<UploadResult | null>(null);

  const dispatch = useAppDispatch();
  const [uploadBatch, { isLoading }] = useUploadBatchMutation();

  const handleClose = () => {
    setOpen(false);
    setFiles([]);
    setUploadResult(null);
    setError(null);
  };

  const onSubmit = async (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    try {
      if (files.length === 0) {
        setError("Please upload a file");
        return;
      }

      const formData = new FormData();
      formData.append("file", files[0]);

      const response = await uploadBatch(formData).unwrap();
      dispatch(orderApi.util.invalidateTags(["Order"]));

      setUploadResult(response);
    } catch (error) {
      console.error(error);
      setError("Could not upload file. Please try again.");
    }

    // TODO: send to backend to parse file
  };

  return (
    <Dialog
      open={open}
      onOpenChange={open => {
        if (open) setOpen(open);
        else handleClose();
      }}
    >
      <DialogTrigger asChild>
        <Button size="lg">
          <FileUp />
          Upload xlsx
        </Button>
      </DialogTrigger>
      <DialogContent className="w-full sm:min-w-xl md:min-w-2xl lg:min-w-3xl xl:gap-y-5">
        <DialogTitle className="sr-only">Import Batch</DialogTitle>
        <DialogDescription className="sr-only">
          Import a batch of orders.
        </DialogDescription>
        <DialogHeader>
          <Header icon={ClipboardDocumentListIcon} variant="section" as="h3">
            Upload Batch
          </Header>
        </DialogHeader>
        {uploadResult ? (
          <UploadResult uploadResult={uploadResult} />
        ) : (
          <FileUpload
            files={files}
            onChange={setFiles}
            accept={[".csv", ".xls"]}
            maxFiles={MAX_FILE_NUM}
            className="relative min-h-40 lg:min-h-48 xl:min-h-56"
            onError={setError}
          />
        )}
        {error && (
          <div className="px-4 py-2.5 bg-destructive/10 rounded-md flex items-center gap-2">
            <AlertTriangle className="text-destructive size-4" />
            <p className="text-destructive text-sm">{error}</p>
          </div>
        )}

        <DialogFooter>
          {!uploadResult ? (
            <>
              <Button
                type="button"
                variant="ghost-destructive"
                onClick={handleClose}
              >
                Cancel
              </Button>

              <Button
                onClick={onSubmit}
                disabled={files.length === 0 || isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="size-4 animate-spin" />
                    Importing...
                  </>
                ) : (
                  "Import Batch"
                )}
              </Button>
            </>
          ) : (
            <Button onClick={handleClose}>Close</Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

const UploadResult = ({ uploadResult }: { uploadResult: UploadResult }) => {
  const { successful, failed, total, errors } = uploadResult;
  return (
    <div className="min-h-40 lg:min-h-48 xl:min-h-56 space-y-4">
      {successful > 0 && (
        <div className="p-4 flex gap-2 items-center text-success border border-success border-dashed bg-success/5 rounded-md">
          <Check className="size-4" />
          <p className="text-sm">
            {successful} out of {total} orders were successfully uploaded!
          </p>
        </div>
      )}
      {failed > 0 && (
        <div className="p-4 border border-destructive border-dashed bg-destructive/5 rounded-md text-destructive">
          <div className="flex gap-2 items-center">
            <AlertTriangle className="size-4 mb-0.5" />
            <p className="text-sm font-semibold">
              {failed} orders failed to upload.
            </p>
          </div>
          <ul className="list-disc list-inside text-sm mt-2 px-2 space-y-1">
            {errors.map((error, index) => (
              <li key={index}>{error}</li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default UploadXlsxModal;
