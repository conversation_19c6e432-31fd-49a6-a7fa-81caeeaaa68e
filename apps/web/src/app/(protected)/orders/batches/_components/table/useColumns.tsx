import Status from "@/components/common/Status";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useConfirm } from "@/hooks/useConfirm";
import { useCancelBatchMutation } from "@/store/api/batchApi";
import { BatchesWithOrderCount } from "@/types/Batch/Batch";
import { BatchStatus } from "@repo/database";
import { ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";
import { Archive, EllipsisVertical, Pencil, X } from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";

export const createBatchColumns = (): ColumnDef<BatchesWithOrderCount>[] => [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={value => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={value => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "createdAt",
    header: "Created Date",
    cell: ({ row }) => (
      <div>{format(row.getValue("createdAt"), "MM/dd/yyyy")}</div>
    ),
  },
  {
    accessorKey: "batchNo",
    header: "Batch No",
    cell: ({ row }) => (
      <Link
        href={`/orders/batches/${row.original.id}`}
        className="hover:text-primary hover:underline underline-offset-2"
      >
        {row.original.batchNo}
      </Link>
    ),
  },
  { accessorKey: "name", header: "Name" },
  {
    accessorKey: "_count",
    header: () => <div className="text-right">Order Count</div>,
    cell: ({ row }) => {
      const count = row.original._count;
      return <div className="text-right">{count.orders}</div>;
    },
  },
  {
    accessorKey: "notes",
    header: "Notes",
    cell: ({ row }) => (
      <div className="min-w-[100px] truncate">{row.original.notes}</div>
    ),
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const { status } = row.original;
      return <Status text={status} />;
    },
  },
];

type CreateBatchActionsProps = {
  handleEditBatch: (id: string) => void;
  handleArchiveBatch: (id: string) => void;
};

export const createBatchActions = ({
  handleEditBatch,
  handleArchiveBatch,
}: CreateBatchActionsProps): ColumnDef<BatchesWithOrderCount>[] => [
  {
    id: "actions",
    cell: ({ row }) => {
      const batch = row.original;
      return (
        <div className="flex justify-end">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon-xs">
                <span className="sr-only">Open menu</span>
                <EllipsisVertical />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => handleEditBatch(batch.id)}>
                <Pencil />
                Edit
              </DropdownMenuItem>
              {batch.status === BatchStatus.open && (
                <CancelBatchDropdownMenu batchId={batch.id} />
              )}
              {batch.status === BatchStatus.processed && (
                <DropdownMenuItem
                  variant="destructive"
                  onClick={() => handleArchiveBatch(batch.id)}
                >
                  <Archive />
                  Archive Batch
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      );
    },
  },
];

export const CancelBatchDropdownMenu = ({ batchId }: { batchId: string }) => {
  const { confirm, ConfirmModal } = useConfirm();
  const [cancelBatch] = useCancelBatchMutation();

  const handleCancelBatch = async (batchId: string) => {
    const confirmed = await confirm({
      title: "Cancel Batch",
      description:
        "Cancelling this batch will remove all orders from the batch. The orders themselves will remain active.",
      confirmText: "Cancel Batch",
      cancelText: "Cancel",
      variant: "destructive",
      icon: <X />,
    });
    if (!confirmed) return;

    try {
      await cancelBatch(batchId).unwrap();
      toast.success("Batch canceled successfully");
    } catch (error) {
      if (process.env.NODE_ENV === "development") console.error(error);
      toast.error("Failed to cancel batch");
    }
  };

  return (
    <>
      <DropdownMenuItem
        variant="destructive"
        onSelect={e => {
          e.preventDefault();
          e.stopPropagation();
        }}
        onClick={() => handleCancelBatch(batchId)}
      >
        <X />
        Cancel Batch
      </DropdownMenuItem>
      <ConfirmModal />
    </>
  );
};
