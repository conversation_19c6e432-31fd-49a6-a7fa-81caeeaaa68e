"use client";

import { cn } from "@/lib/utils";
import { useGetOrderQuery } from "@/store/api/orderApi";
import styles from "@/styles/Dashboard.module.css";
import { OrderStatus } from "@repo/database";
import { notFound, useParams } from "next/navigation";
import OrderForm from "../_components/orderForm";
import { OrderFormProvider } from "../_components/orderForm/OrderFormContext";
import Header from "./_components/Header";
import CancelledOrderView from "./_components/views/CancelledOrderView";
import OrderDetailsView from "./_components/views/OrderDetailsView";
import Loading from "./loading";

const EditOrderPage = () => {
  const params = useParams();
  const id = params.id as string;

  const { data: order, isLoading: orderLoading } = useGetOrderQuery(id);

  if (orderLoading) return <Loading />;

  if (!order) notFound();

  const renderOrderView = () => {
    switch (order.status) {
      case OrderStatus.open:
        return <OrderForm />;
      case OrderStatus.cancelled:
        return <CancelledOrderView order={order} />;
      case OrderStatus.processing:
        return <OrderDetailsView order={order} />;
      case OrderStatus.shipped:
        return <OrderDetailsView order={order} />;
      default:
        return <div>Something went wrong...</div>;
    }
  };

  return (
    <div className={cn(styles.pagePadding, "relative")}>
      <Header order={order} />
      <OrderFormProvider order={order}>{renderOrderView()}</OrderFormProvider>
    </div>
  );
};

export default EditOrderPage;
