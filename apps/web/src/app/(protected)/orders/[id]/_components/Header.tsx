import SummaryCard from "@/components/common/cards/SummaryCard";
import { Button } from "@/components/ui/button";
import { Order } from "@/types/Order/Order";
import { ChevronLeft } from "lucide-react";
import Link from "next/link";

type HeaderProps = {
  order: Order;
};

const Header = ({ order }: HeaderProps) => {
  const { isSplit, parentOrderId } = order;

  return (
    <div className="flex flex-col md:flex-row gap-2 justify-between items-start">
      {/* Left */}
      <div className="flex items-center gap-1">
        <Link href="/orders">
          <Button size="icon-sm" variant="ghost">
            <ChevronLeft />
          </Button>
        </Link>
        <p className="font-semibold text-lg">Order - {order.orderNo}</p>
      </div>
      {/* Right  */}
      <SummaryCard
        data={[
          { label: "status", value: order.status },
          { label: "Order No", value: order.orderNo },
          ...(isSplit
            ? [{ label: "Type", value: "Split Order (parent)" }]
            : []),
          ...(parentOrderId
            ? [{ label: "Type", value: "Split Order (child)" }]
            : []),
        ]}
      />
    </div>
  );
};

export default Header;
