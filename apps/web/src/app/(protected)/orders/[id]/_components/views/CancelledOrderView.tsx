import OrderDetailsCard from "@/components/common/cards/OrderDetailsCard";
import ShipmentDetailsCard from "@/components/common/cards/ShipmentDetailsCard";
import { cn } from "@/lib/utils";
import { GetOrderResponse } from "@/store/types/orders";
import styles from "@/styles/Dashboard.module.css";

type CancelledOrderViewProps = {
  order: GetOrderResponse;
};

const CancelledOrderView = ({ order }: CancelledOrderViewProps) => {
  return (
    <section
      className={cn(
        styles.gridGap,
        "mt-4 lg:mt-6 grid lg:grid-cols-[3fr_2fr] xl:grid-cols-[2fr_1fr] items-start"
      )}
    >
      {/* Left Panels */}
      <div className={cn(styles.gridGap, "flex flex-col")}>
        <OrderDetailsCard
          toAddress={order.toAddress}
          orderDate={order.orderDate}
          orderNo={order.orderNo}
          orderItems={order.orderItems}
          notes={order.notes ?? ""}
        />
        <ShipmentDetailsCard
          fromAddress={order.fromAddress}
          toAddress={order.toAddress}
          parcel={order.parcel}
          shipDate={null}
        />
      </div>

      {/* Right Panel */}
    </section>
  );
};

export default CancelledOrderView;
