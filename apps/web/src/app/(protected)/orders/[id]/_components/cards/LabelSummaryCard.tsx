import CarrierServiceCard from "@/components/common/cards/CarrierServiceCard";
import CopyTextWrapper from "@/components/common/CopyTextWrapper";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";
import { Shipment } from "@/types/Shipment/Shipment";
import { ShipmentStatus } from "@repo/database";
import { Copy } from "lucide-react";

type LabelSummaryProps = {
  shipments: Shipment[];
};

const LabelSummary = ({ shipments }: LabelSummaryProps) => {
  const sortedShipments = Array.from(shipments).sort(
    (a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
  );

  const newestShipment = sortedShipments[shipments.length - 1];

  return (
    <div className="border rounded-lg p-2 space-y-6 overflow-x-hidden">
      <Accordion
        type="single"
        className="space-y-2"
        defaultValue={newestShipment.id}
        collapsible
      >
        {sortedShipments.map(shipment => (
          <AccordionItem
            key={shipment.id}
            value={shipment.id}
            className="border-none"
          >
            <AccordionTrigger className="p-0 [&>svg]:hidden hover:no-underline hover:opacity-80">
              <CarrierServiceCard
                key={shipment.id}
                carrierId={shipment.carrier}
                serviceId={shipment.service}
                rate={shipment.rate}
                isVoided={shipment.status === ShipmentStatus.voided}
              />
            </AccordionTrigger>
            <AccordionContent>
              <div className="px-4 pt-4 pb-0 text-sm space-y-4">
                <div>
                  <div className="text-xs text-muted-foreground font-semibold">
                    Tracking Code
                  </div>
                  <div className="flex justify-between items-center">
                    {shipment.trackingCode}
                    <div className="relative">
                      <CopyTextWrapper
                        text={shipment.trackingCode}
                        isTable={false}
                      >
                        <Button variant="muted" size="xs">
                          <Copy />
                        </Button>
                      </CopyTextWrapper>
                    </div>
                  </div>
                </div>

                <div>
                  <div className="text-xs text-muted-foreground font-semibold">
                    Insurance
                  </div>
                  <div className="text-accent">
                    {String(shipment.insurance ?? "N/A")}
                  </div>
                </div>
                <div>
                  <div className="text-xs text-muted-foreground font-semibold">
                    Label
                  </div>
                  <div className="flex justify-between items-center">
                    <div className="text-accent">
                      {shipment?.labelPrinted ? "Printed" : "Not Printed"}
                    </div>
                  </div>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    </div>
  );
};

export default LabelSummary;
