import CopyTextWrapper from "@/components/common/CopyTextWrapper";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Address } from "@repo/database";
import { ColumnDef } from "@tanstack/react-table";
import { Check, EllipsisVertical, Pencil, Trash, X } from "lucide-react";

export const createShipFromAddressColumns = (): ColumnDef<Address>[] => [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={value => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={value => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  // { accessorKey: "id", header: "ID" },
  { accessorKey: "name", header: "Name" },
  { accessorKey: "company", header: "Company" },
  { accessorKey: "email", header: "Email" },
  { accessorKey: "phone", header: "Phone" },
  {
    id: "address",
    header: "Address",
    cell: ({ row }) => {
      const { street1, street2, city, state, zip, country } = row.original;
      return (
        <div className="text-sm min-w-60 max-w-96 whitespace-pre-line">
          {`${street1} ${street2} ${city} ${state} ${zip} ${country}`}
        </div>
      );
    },
  },
  {
    accessorKey: "verified",
    header: "Verified",
    cell: ({ row }) =>
      row.original.verified ? (
        <Check size={16} className="text-success mx-auto" />
      ) : (
        <X size={16} className="text-gray-400 mx-auto" />
      ),
  },
];

type createShipFromAddressActionsProps = {
  handleEditButton: (address: Address) => void;
  handleDeleteButton: (id: string) => void;
};

export const createShipFromAddressActionsColumns = ({
  handleEditButton,
  handleDeleteButton,
}: createShipFromAddressActionsProps): ColumnDef<Address>[] => [
  {
    id: "actions",
    cell: ({ row }) => {
      const address = row.original;

      return (
        <div className="flex justify-center">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button size="icon-xs" variant="ghost">
                <span className="sr-only">Open menu</span>
                <EllipsisVertical />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuGroup>
                <CopyTextWrapper text={address.epAddressId} isTable={true}>
                  <DropdownMenuItem>Copy Address ID</DropdownMenuItem>
                </CopyTextWrapper>
              </DropdownMenuGroup>
              <DropdownMenuSeparator />
              <DropdownMenuGroup>
                <DropdownMenuItem onClick={() => handleEditButton(address)}>
                  <Pencil />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem
                  variant="destructive"
                  onClick={() => handleDeleteButton(address.id)}
                >
                  <Trash />
                  Delete
                </DropdownMenuItem>{" "}
              </DropdownMenuGroup>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      );
    },
  },
];
