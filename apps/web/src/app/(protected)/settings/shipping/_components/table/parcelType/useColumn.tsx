import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { UNIT_SYSTEMS } from "@/config/unitSystem";
import { ColumnDef } from "@tanstack/react-table";
import { Parcel } from "@repo/database";
import { EllipsisVertical, Pencil, Trash } from "lucide-react";
import { parseWeight } from "@/lib/utils/parcel";

export const createParcelTypeColumns = (): ColumnDef<Parcel>[] => [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={value => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={value => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  { accessorKey: "id", header: "ID" },
  { accessorKey: "name", header: "Name" },
  { accessorKey: "predefined_package", header: "Predefined Package" },
  {
    accessorKey: "length",
    header: () => (
      <div className="text-right">
        Length ({UNIT_SYSTEMS.imperial.dimension.shortLabel})
      </div>
    ),
    cell: ({ row }) => (
      <div className="text-right">{row.getValue("length")}</div>
    ),
  },
  {
    accessorKey: "width",
    header: () => (
      <div className="text-right">
        Width ({UNIT_SYSTEMS.imperial.dimension.shortLabel})
      </div>
    ),
    cell: ({ row }) => (
      <div className="text-right">{row.getValue("width")}</div>
    ),
  },
  {
    accessorKey: "height",
    header: () => (
      <div className="text-right">
        Height ({UNIT_SYSTEMS.imperial.dimension.shortLabel})
      </div>
    ),
    cell: ({ row }) => (
      <div className="text-right">{row.getValue("height")}</div>
    ),
  },
  {
    accessorKey: "weight",
    header: () => (
      <div className="text-right">
        Weight ({UNIT_SYSTEMS.imperial.weight.shortLabel})
      </div>
    ),
    cell: ({ row }) => {
      const { lb, oz } = parseWeight(row.getValue("weight"));
      return (
        <div className="text-right">
          {lb} lb, {oz} oz
        </div>
      );
    },
  },
];

type createParcelTypeActionsProps = {
  handleEditButton: (parcel: Parcel) => void;
  handleDeleteButton: (id: string) => void;
};

export const createParcelTypeActionsColumns = ({
  handleEditButton,
  handleDeleteButton,
}: createParcelTypeActionsProps): ColumnDef<Parcel>[] => [
  {
    id: "actions",
    cell: ({ row }) => {
      const parcel = row.original;
      return (
        <div className="flex justify-center">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button size="icon-xs" variant="ghost">
                <span className="sr-only">Open menu</span>
                <EllipsisVertical />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => handleEditButton(parcel)}>
                <Pencil className="size-3" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem
                variant="destructive"
                onClick={() => handleDeleteButton(parcel.id)}
              >
                <Trash className="size-3" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      );
    },
  },
];
