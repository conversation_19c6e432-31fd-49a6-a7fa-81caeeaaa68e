"use client";

import ParcelFormModal from "@/components/common/forms/ParcelFormModal";
import DataTable from "@/components/common/table/DataTable";
import { Button } from "@/components/ui/button";
import { useConfirm } from "@/hooks/useConfirm";
import ParcelSchema from "@/schemas/settings/ParcelSchema";
import {
  useCreateParcelMutation,
  useDeleteParcelMutation,
  useDeleteParcelsMutation,
  useUpdateParcelMutation,
} from "@/store/api/parcelApi";
import { Parcel } from "@repo/database";
import {
  ColumnDef,
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { PlusCircle } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { z } from "zod";
import {
  createParcelTypeActionsColumns,
  createParcelTypeColumns,
} from "./table/parcelType/useColumn";
import Toolbar from "./table/Toolbar";

type ParcelTypeSectionProps = {
  data: Parcel[];
};

const PAGE_SIZE_OPTIONS = [5, 10, 15];
const DEFAULT_PAGE_SIZE = PAGE_SIZE_OPTIONS[0];

const ParcelTypeSection = ({ data }: ParcelTypeSectionProps) => {
  // Create/update parcel modal states
  const [openParcelModal, setOpenParcelModal] = useState(false);
  const [selectedParcel, setSelectedParcel] = useState<Parcel | null>(null);

  const { confirm, ConfirmModal } = useConfirm();

  const [createParcel, { isLoading: isCreating }] = useCreateParcelMutation();
  const [updateParcel, { isLoading: isUpdating }] = useUpdateParcelMutation();
  const [deleteParcel] = useDeleteParcelMutation();

  const handleEditButton = (parcel: Parcel) => {
    setSelectedParcel(parcel);
    setOpenParcelModal(true);
  };

  const handleDeleteButton = async (id: string) => {
    try {
      const confirmed = await confirm({
        title: "Delete",
        description: "Are you sure you want to delete this parcel?",
        confirmText: "Delete",
        variant: "destructive",
      });

      if (!confirmed) return;

      await deleteParcel(id).unwrap();
      toast.success("Successfully deleted parcel");
    } catch (error) {
      console.error("Failed to delete parcel", error);
      toast.error("Failed to delete parcel");
    }
  };

  const handleCreateParcel = async (values: z.infer<typeof ParcelSchema>) => {
    try {
      await createParcel(values).unwrap();
      toast.success("Parcel added successfully");
      setOpenParcelModal(false);
    } catch (error) {
      console.error("Failed to add parcel", error);
      toast.error("Failed to add parcel");
    }
  };

  const handleUpdateParcel = async (values: z.infer<typeof ParcelSchema>) => {
    if (!selectedParcel) return;

    try {
      await updateParcel({
        id: selectedParcel.id,
        updatedParcel: values,
      }).unwrap();
      toast.success("Parcel updated successfully");
      setOpenParcelModal(false);
    } catch (error) {
      console.error("Failed to update parcel", error);
      toast.error("Failed to update parcel");
    }
  };

  const columns = [
    ...createParcelTypeColumns(),
    ...createParcelTypeActionsColumns({
      handleEditButton,
      handleDeleteButton,
    }),
  ];

  const [mounted, setMounted] = useState(false);
  useEffect(() => {
    setMounted(true);
  }, []);

  if (mounted)
    return (
      <>
        <div className="w-full max-w-full flex flex-col">
          <Table
            data={data}
            columns={columns}
            setOpenParcelModal={setOpenParcelModal}
          />
        </div>
        {/* Modals */}
        <ConfirmModal />
        {openParcelModal && (
          <ParcelFormModal
            open={openParcelModal}
            selectedParcel={selectedParcel}
            isSubmitting={isCreating || isUpdating}
            submitButtonText={selectedParcel ? "Update Parcel" : "Add Parcel"}
            setOpen={setOpenParcelModal}
            onClose={() => {
              setOpenParcelModal(false);
              setSelectedParcel(null);
            }}
            onSubmit={selectedParcel ? handleUpdateParcel : handleCreateParcel}
          />
        )}
      </>
    );
};

type TableProps = {
  data: Parcel[];
  columns: ColumnDef<Parcel>[];
  setOpenParcelModal: (open: boolean) => void;
};

const Table = ({ data, columns, setOpenParcelModal }: TableProps) => {
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: DEFAULT_PAGE_SIZE,
  });

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onPaginationChange: setPagination,
    state: { pagination },
  });

  const [deleteParcels] = useDeleteParcelsMutation();

  return (
    <>
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-sm lg:text-base xl:text-lg font-semibold">
            Parcel Type:
          </h2>
          <div className="flex items-center gap-3">
            <Toolbar table={table} deleteMany={deleteParcels} />
            <Button
              size="xs"
              variant="secondary"
              onClick={() => setOpenParcelModal(true)}
            >
              <PlusCircle /> New Parcel Type
            </Button>
          </div>
        </div>
        <DataTable
          table={table}
          variant="dark"
          pageSizeOptions={PAGE_SIZE_OPTIONS}
        />
      </div>
    </>
  );
};

export default ParcelTypeSection;
