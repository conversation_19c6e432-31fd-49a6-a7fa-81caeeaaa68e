import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { useDeleteAddressesMutation } from "@/store/api/addressApi";
import { useDeleteParcelsMutation } from "@/store/api/parcelApi";
import { Table } from "@tanstack/react-table";
import { Trash } from "lucide-react";

type DeleteAddressTrigger = ReturnType<typeof useDeleteAddressesMutation>[0];
type DeleteParcelTrigger = ReturnType<typeof useDeleteParcelsMutation>[0];
type DeleteTrigger = DeleteAddressTrigger | DeleteParcelTrigger;

type TableToolbarProps<TData extends { id: string }> = {
  table: Table<TData>;
  deleteMany: DeleteTrigger;
  isDeleting?: boolean;
};

const TableToolbar = <TData extends { id: string }>({
  table,
  deleteMany,
  isDeleting = false,
}: TableToolbarProps<TData>) => {
  const noOfSelectedRows = table.getFilteredSelectedRowModel().rows.length;

  const selectedRows = table
    .getFilteredSelectedRowModel()
    .rows.map(row => row.original.id);

  const handleDeleteMany = async () => {
    try {
      await deleteMany(selectedRows).unwrap();
      table.setRowSelection({});
    } catch (error) {
      console.error("Error deleting:", error);
    }
  };

  if (noOfSelectedRows > 0)
    return (
      <div className="flex items-center gap-2 h-6">
        <span className="text-sm font-semibold">
          {noOfSelectedRows} selected
        </span>
        <Separator orientation="vertical" className="mx-1" />
        <Button
          size="xs"
          variant="ghost-destructive"
          onClick={handleDeleteMany}
          disabled={isDeleting}
        >
          <Trash /> Delete
        </Button>
      </div>
    );
};

export default TableToolbar;
