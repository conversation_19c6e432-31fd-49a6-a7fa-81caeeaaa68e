import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ColumnDef } from "@tanstack/react-table";
import { EllipsisVertical, Pencil, Trash } from "lucide-react";
import { Preset } from "../../PresetSection";

// TODO: Add type for Preset
export const createPresetColumns = (): ColumnDef<Preset>[] => [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={value => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={value => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  { accessorKey: "id", header: "ID" },
  { accessorKey: "name", header: "Name" },
  { accessorKey: "shipFrom", header: "Ship From" },
  { accessorKey: "service", header: "Service" },
  { accessorKey: "packageType", header: "Package Type" },
  {
    id: "dimensions",
    header: "Dimensions",
    cell: ({ row }) => {
      const { height, length, weight, width } = row.original;
      return (
        <div className="text-sm min-w-60 max-w-96 whitespace-pre-line">
          {`${height}cm x ${length}cm x ${width}cm x ${weight}oz`}
        </div>
      );
    },
  },
];

type createPresetActionsProps = {
  handleEditButton: (preset: Preset) => void;
  handleDeleteButton: (id: string) => void;
};

export const createPresetActionsColumns = ({
  handleEditButton,
  handleDeleteButton,
}: createPresetActionsProps): ColumnDef<Preset>[] => [
  {
    id: "actions",
    cell: ({ row }) => {
      const preset = row.original;
      return (
        <div className="flex justify-center">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button size="icon-xs" variant="ghost">
                <span className="sr-only">Open menu</span>
                <EllipsisVertical />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem
                onClick={() =>
                  handleEditButton({
                    ...preset,
                    height: 0,
                    length: 0,
                    weight: 0,
                    width: 0,
                  })
                }
              >
                <Pencil />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem
                variant="destructive"
                onClick={() => handleDeleteButton(preset.id)}
              >
                <Trash />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      );
    },
  },
];
