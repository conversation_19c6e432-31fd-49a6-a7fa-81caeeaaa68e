import { But<PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import { PlusCircle } from "lucide-react";

type SkeletonTableProps = {
  className?: string;
  title?: string;
  titleButton?: string;
};

const SkeletonTable = ({
  className,
  title,
  titleButton,
}: SkeletonTableProps) => {
  return (
    <div className={cn(className)}>
      <div className="flex justify-between items-center">
        {title && (
          <h2 className="text-sm lg:text-base xl:text-lg font-semibold">
            {title}
          </h2>
        )}
        {titleButton && (
          <Button size="xs" variant="secondary">
            <PlusCircle /> {titleButton}
          </Button>
        )}
      </div>
      <Skeleton className="h-56 w-full mt-4 " />
    </div>
  );
};

export default SkeletonTable;
