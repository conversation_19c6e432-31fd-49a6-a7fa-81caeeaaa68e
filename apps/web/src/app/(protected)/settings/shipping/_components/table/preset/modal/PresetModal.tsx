"use client";

import Header from "@/components/layout/Header";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { UNIT_SYSTEMS } from "@/config/unitSystem";
import { cn } from "@/lib/utils";
import PresetSchema, { defaultValues } from "@/schemas/settings/PresetSchema";
import customStyles from "@/styles/Custom.module.css";
import { AdjustmentsHorizontalIcon } from "@heroicons/react/24/outline";
import { zodResolver } from "@hookform/resolvers/zod";
import { LoaderCircle } from "lucide-react";
import { Dispatch, SetStateAction } from "react";
import { FormProvider, useForm, useFormContext } from "react-hook-form";
import { z } from "zod";
import { Preset } from "../../../PresetSection";

type PresetModalProps = {
  open: boolean;
  selectedPreset?: Preset | null;
  submitButtonText?: string;
  isSubmitting: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  onClose: () => void;
  onSubmit: (values: z.infer<typeof PresetSchema>) => Promise<void>;
};

const PresetModal = ({
  open,
  selectedPreset,
  submitButtonText = "Save changes",
  isSubmitting,
  setOpen,
  onClose,
  onSubmit,
}: PresetModalProps) => {
  const form = useForm<z.infer<typeof PresetSchema>>({
    resolver: zodResolver(PresetSchema),
    defaultValues: selectedPreset
      ? {
          name: selectedPreset.name,
          shipFrom: selectedPreset.shipFrom,
          service: selectedPreset.service,
          packageType: selectedPreset.packageType,
          height: selectedPreset.height,
          length: selectedPreset.length,
          weight: selectedPreset.weight,
          width: selectedPreset.width,
        }
      : defaultValues,
  });

  const handleSubmit = async (values: z.infer<typeof PresetSchema>) => {
    await onSubmit(values);
    form.reset();
  };

  return (
    <Dialog
      open={open}
      onOpenChange={() =>
        setOpen(prev => {
          if (prev) {
            form.reset();
            onClose();
          }
          return !prev;
        })
      }
    >
      <DialogContent className="md:min-w-2xl xl:gap-y-5">
        <DialogTitle className="hidden">Presets</DialogTitle>
        <DialogDescription className="hidden">
          Form to add preset.
        </DialogDescription>
        <DialogHeader>
          <Header as="h3" icon={AdjustmentsHorizontalIcon}>
            Presets
          </Header>
        </DialogHeader>
        <Separator />
        <FormProvider {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-4 xl:space-y-5"
          >
            <PresetForm />
            <Separator />
            <ParcelDetailForm />
            <Separator />
            <DialogFooter>
              <DialogClose asChild>
                <Button type="button" variant="ghost-destructive">
                  Cancel
                </Button>
              </DialogClose>

              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting && (
                  <LoaderCircle className="size-4 animate-spin" />
                )}
                {submitButtonText}
              </Button>
            </DialogFooter>
          </form>
        </FormProvider>
      </DialogContent>
    </Dialog>
  );
};

const PresetForm = () => {
  const { control } = useFormContext();
  return (
    <div className="space-y-4">
      <h2 className="font-semibold">Details</h2>
      <div className="space-y-4">
        <FormField
          control={control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Preset Name</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="shipFrom"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Ship From</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger className="w-full">
                    <SelectValue />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="A">Address A</SelectItem>
                  <SelectItem value="B">Address B</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="service"
          render={({ field }) => (
            <FormItem className="w-1/2">
              <FormLabel>Shipping Service</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger className="w-full">
                    <SelectValue />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="USPS">USPS</SelectItem>
                  <SelectItem value="B">Address B</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
};

const ParcelDetailForm = () => {
  const { control } = useFormContext();

  const handleNumberChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    onChange: (value: number) => void
  ) => {
    const value = e.target.value;
    // Only allow one decimal point and one digit after it
    if (
      /^\d*\.?\d{0,1}$/.test(value) &&
      (value.match(/\./g) || []).length <= 1
    ) {
      onChange(Number(value));
    }
  };

  return (
    <div className="space-y-4">
      <h2 className="font-semibold">Parcel Details</h2>
      <div className="space-y-4">
        <FormField
          control={control}
          name="packageType"
          render={({ field }) => (
            <FormItem className="w-1/2">
              <FormLabel>Parcel Type</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger className="w-full">
                    <SelectValue />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="Small Box">Small Box</SelectItem>
                  <SelectItem value="Medium Box">Medium Box</SelectItem>
                  <SelectItem value="Large Box">Large Box</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid sm:grid-cols-3 gap-4">
          <FormField
            control={control}
            name="length"
            render={({ field }) => (
              <FormItem className="relative">
                <FormLabel>Length</FormLabel>
                <div className="relative">
                  <FormControl>
                    <Input
                      type="number"
                      step="0.1"
                      min={0}
                      className={cn(
                        customStyles.inputNoArrows,
                        "text-right pr-8"
                      )}
                      {...field}
                      onChange={e => handleNumberChange(e, field.onChange)}
                    />
                  </FormControl>
                  <span className="text-muted-foreground text-xs absolute right-3.5 top-1/2 -translate-y-1/2">
                    {UNIT_SYSTEMS.imperial.dimension.shortLabel}
                  </span>
                </div>
              </FormItem>
            )}
          />
          <FormField
            control={control}
            name="width"
            render={({ field }) => (
              <FormItem className="relative">
                <FormLabel>Width</FormLabel>
                <div className="relative">
                  <FormControl>
                    <Input
                      type="number"
                      step="0.1"
                      min={0}
                      className={cn(
                        customStyles.inputNoArrows,
                        "text-right pr-8"
                      )}
                      {...field}
                      onChange={e => handleNumberChange(e, field.onChange)}
                    />
                  </FormControl>
                  <span className="text-muted-foreground text-xs absolute right-3.5 top-1/2 -translate-y-1/2">
                    {UNIT_SYSTEMS.imperial.dimension.shortLabel}
                  </span>
                </div>
              </FormItem>
            )}
          />
          <FormField
            control={control}
            name="height"
            render={({ field }) => (
              <FormItem className="relative">
                <FormLabel>Height</FormLabel>
                <div className="relative">
                  <FormControl>
                    <Input
                      type="number"
                      step="0.1"
                      min={0}
                      className={cn(
                        customStyles.inputNoArrows,
                        "text-right pr-8"
                      )}
                      {...field}
                      onChange={e => handleNumberChange(e, field.onChange)}
                    />
                  </FormControl>
                  <span className="text-muted-foreground text-xs absolute right-3.5 top-1/2 -translate-y-1/2">
                    {UNIT_SYSTEMS.imperial.dimension.shortLabel}
                  </span>
                </div>
              </FormItem>
            )}
          />
        </div>
        <div className="grid sm:grid-cols-2 gap-4 items-end">
          <FormField
            control={control}
            name="weight"
            render={({ field }) => (
              <FormItem className="relative">
                <FormLabel>Weight</FormLabel>
                <div className="relative">
                  <FormControl>
                    <Input
                      type="number"
                      step="0.1"
                      min={0}
                      className={cn(
                        customStyles.inputNoArrows,
                        "text-right pr-8"
                      )}
                      {...field}
                      onChange={e => handleNumberChange(e, field.onChange)}
                    />
                  </FormControl>
                  <span className="text-muted-foreground text-xs absolute right-3.5 top-1/2 -translate-y-1/2">
                    {UNIT_SYSTEMS.imperial.weight.shortLabel}
                  </span>
                </div>
              </FormItem>
            )}
          />
          <FormField
            control={control}
            name="weight"
            render={({ field }) => (
              <FormItem className="relative">
                <div className="relative">
                  <FormControl>
                    <Input
                      type="number"
                      step="0.1"
                      min={0}
                      className={cn(
                        customStyles.inputNoArrows,
                        "text-right pr-8"
                      )}
                      {...field}
                      onChange={e => handleNumberChange(e, field.onChange)}
                    />
                  </FormControl>
                  <span className="text-muted-foreground text-xs absolute right-3.5 top-1/2 -translate-y-1/2">
                    {UNIT_SYSTEMS.imperial.weight.shortLabel}
                  </span>
                </div>
              </FormItem>
            )}
          />
        </div>
      </div>
    </div>
  );
};

export default PresetModal;
