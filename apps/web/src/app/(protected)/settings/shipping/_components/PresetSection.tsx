"use client";

import DataTable from "@/components/common/table/DataTable";
import { Button } from "@/components/ui/button";
import { useConfirm } from "@/hooks/useConfirm";
import {
  ColumnDef,
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { PlusCircle } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import PresetModal from "./table/preset/modal/PresetModal";
import {
  createPresetActionsColumns,
  createPresetColumns,
} from "./table/preset/useColumn";
import Toolbar from "./table/Toolbar";

// TODO: Add preset to database
export type Preset = {
  id: string;
  name: string;
  shipFrom: string;
  service: string;
  packageType: string;
  height: number;
  length: number;
  weight: number;
  width: number;
};

type PresetSectionProps = {
  data: Preset[];
};

const PAGE_SIZE_OPTIONS = [5, 10, 15];
const DEFAULT_PAGE_SIZE = PAGE_SIZE_OPTIONS[0];

const PresetSection = ({ data }: PresetSectionProps) => {
  // Create/update preset modal states
  const [openPresetModal, setOpenPresetModal] = useState(false);
  const [selectedPreset, setSelectedPreset] = useState<Preset | null>(null);

  const { confirm, ConfirmModal } = useConfirm();

  const handleEditButton = (preset: Preset) => {
    setSelectedPreset(preset);
    setOpenPresetModal(true);
  };

  const handleDeleteButton = async (id: string) => {
    try {
      const confirmed = await confirm({
        title: "Delete",
        description: "Are you sure you want to delete this preset?",
        confirmText: "Delete",
        variant: "destructive",
      });

      if (!confirmed) return;

      console.log("Deleting preset with id ", id);
    } catch (error) {
      console.error("Failed to delete preset", error);
      toast.error("Failed to delete preset");
    }
  };

  const columns = [
    ...createPresetColumns(),
    ...createPresetActionsColumns({
      handleEditButton,
      handleDeleteButton,
    }),
  ];

  const [mounted, setMounted] = useState(false);
  useEffect(() => {
    setMounted(true);
  }, []);
  if (mounted)
    return (
      <div className="w-full max-w-full flex flex-col">
        <Table
          data={data}
          columns={columns}
          setOpenPresetModal={setOpenPresetModal}
        />

        {/* Modals */}
        <ConfirmModal />
        {openPresetModal && (
          <PresetModal
            open={openPresetModal}
            selectedPreset={selectedPreset}
            isSubmitting={false}
            submitButtonText={selectedPreset ? "Update Preset" : "Add Preset"}
            setOpen={setOpenPresetModal}
            onClose={() => {
              setOpenPresetModal(false);
              setSelectedPreset(null);
            }}
            onSubmit={async values => {
              console.log("PresetModal values =>", values);
            }}
          />
        )}
      </div>
    );
};

type TableProps = {
  data: Preset[];
  columns: ColumnDef<Preset>[];
  setOpenPresetModal: (open: boolean) => void;
};

const Table = ({ data, columns, setOpenPresetModal }: TableProps) => {
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: DEFAULT_PAGE_SIZE,
  });

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onPaginationChange: setPagination,
    state: { pagination },
  });

  return (
    <>
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-sm lg:text-base xl:text-lg font-semibold">
            Presets:
          </h2>
          <div className="flex items-center gap-3">
            <Toolbar table={table} deleteMany={null} />
            <Button
              size="xs"
              variant="secondary"
              onClick={() => setOpenPresetModal(true)}
            >
              <PlusCircle />
              Add New Preset
            </Button>
          </div>
        </div>
        <DataTable
          table={table}
          variant="dark"
          pageSizeOptions={PAGE_SIZE_OPTIONS}
        />
      </div>
    </>
  );
};

export default PresetSection;
