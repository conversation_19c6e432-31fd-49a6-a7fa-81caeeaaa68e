"use client";

import AddressFormModal from "@/components/common/forms/AddressFormModal";
import DataTable from "@/components/common/table/DataTable";
import { Button } from "@/components/ui/button";
import { useConfirm } from "@/hooks/useConfirm";
import { addressToAddressForm } from "@/lib/utils/address";
import AddressSchema from "@/schemas/common/AddressSchema";
import {
  useCreateAddressMutation,
  useDeleteAddressesMutation,
  useDeleteAddressMutation,
  useUpdateAddressMutation,
} from "@/store/api/addressApi";
import { Address } from "@repo/database";
import {
  ColumnDef,
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { PlusCircle } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { z } from "zod";
import {
  createShipFromAddressActionsColumns,
  createShipFromAddressColumns,
} from "./table/shipFromAddress/useColumn";
import Toolbar from "./table/Toolbar";

type ShipFromAddressSectionProps = {
  data: Address[];
};

const PAGE_SIZE_OPTIONS = [5, 10, 15];
const DEFAULT_PAGE_SIZE = PAGE_SIZE_OPTIONS[0];

const ShipFromAddressSection = ({ data }: ShipFromAddressSectionProps) => {
  const [openAddressModal, setOpenAddressModal] = useState(false);
  const [selectedAddress, setSelectedAddress] = useState<Address | null>(null);

  useEffect(() => {
    if (!openAddressModal) setSelectedAddress(null);
  }, [openAddressModal]);

  const { confirm, ConfirmModal } = useConfirm();

  const [addNewAddress, { isLoading: isCreating }] = useCreateAddressMutation();
  const [updateAddress, { isLoading: isUpdating }] = useUpdateAddressMutation();
  const [deleteAddress] = useDeleteAddressMutation();

  const handleEditButton = (address: Address) => {
    setSelectedAddress(address);
    setOpenAddressModal(true);
  };

  const handleDeleteButton = async (id: string) => {
    try {
      const confirmed = await confirm({
        title: "Delete",
        description: "Are you sure you want to delete this address?",
        confirmText: "Delete",
        variant: "destructive",
      });

      if (!confirmed) return;

      await deleteAddress(id).unwrap();
      toast.success("Successfully deleted address");
    } catch (error) {
      console.error("Failed to delete address", error);
      toast.error("Failed to delete address");
    }
  };

  const onAddressFormSubmit = async (values: z.infer<typeof AddressSchema>) => {
    const action = selectedAddress ? "update" : "add";

    const response = selectedAddress
      ? await updateAddress({ id: selectedAddress.id, updatedAddress: values })
      : await addNewAddress(values);

    if ("error" in response) {
      toast.error(`Failed to ${action} address`);
      return;
    }

    toast.success(
      `Address ${action}${action === "add" ? "ed" : "d"} successfully`
    );
    setOpenAddressModal(false);
  };

  const columns = [
    ...createShipFromAddressColumns(),
    ...createShipFromAddressActionsColumns({
      handleEditButton,
      handleDeleteButton,
    }),
  ];

  const [mounted, setMounted] = useState(false);
  useEffect(() => {
    setMounted(true);
  }, []);

  if (mounted)
    return (
      <>
        <div className="w-full max-w-full flex flex-col">
          <Table
            data={data}
            columns={columns}
            setOpenAddressModal={setOpenAddressModal}
          />
        </div>
        {/* Modal */}
        <ConfirmModal />
        {openAddressModal && (
          <AddressFormModal
            values={
              selectedAddress
                ? addressToAddressForm(selectedAddress)
                : undefined
            }
            isOpen={openAddressModal}
            setOpen={setOpenAddressModal}
            onSubmit={onAddressFormSubmit}
            isLoading={isCreating || isUpdating}
            header="Ship From Address"
          />
        )}
      </>
    );
};

type TableProps = {
  data: Address[];
  columns: ColumnDef<Address>[];
  setOpenAddressModal: (open: boolean) => void;
};

const Table = ({ data, columns, setOpenAddressModal }: TableProps) => {
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: DEFAULT_PAGE_SIZE,
  });

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onPaginationChange: setPagination,
    state: { pagination },
  });

  const [deleteAddresses] = useDeleteAddressesMutation();

  return (
    <>
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-sm lg:text-base xl:text-lg font-semibold">
            Ship From Addresses:
          </h2>
          <div className="flex items-center gap-3">
            <Toolbar table={table} deleteMany={deleteAddresses} />
            <Button
              size="xs"
              variant="secondary"
              onClick={() => setOpenAddressModal(true)}
            >
              <PlusCircle /> New Address
            </Button>
          </div>
        </div>

        <DataTable
          table={table}
          variant="dark"
          pageSizeOptions={PAGE_SIZE_OPTIONS}
        />
      </div>
    </>
  );
};

export default ShipFromAddressSection;
