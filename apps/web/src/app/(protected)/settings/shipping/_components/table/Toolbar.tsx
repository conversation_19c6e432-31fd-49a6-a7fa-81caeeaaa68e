import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { useDeleteAddressesMutation } from "@/store/api/addressApi";
import { useDeleteParcelsMutation } from "@/store/api/parcelApi";
import { Table } from "@tanstack/react-table";
import { Loader2, Trash } from "lucide-react";
import { useTransition } from "react";
import { toast } from "sonner";

type DeleteManyType =
  | ReturnType<typeof useDeleteAddressesMutation>[0]
  | ReturnType<typeof useDeleteParcelsMutation>[0]
  | null;
// Add preset mutation here

type ToolbarProps<TData extends { id: string }> = {
  table: Table<TData>;
  deleteMany: DeleteManyType;
};

const Toolbar = <TData extends { id: string }>({
  table,
  deleteMany,
}: ToolbarProps<TData>) => {
  const noOfSelectedRows = table.getFilteredSelectedRowModel().rows.length;

  const [isPending, startTransition] = useTransition();

  const handleDeleteMany = async () => {
    if (!deleteMany) return; // Temporary for now until preset is done

    startTransition(async () => {
      const selectedRowsIds = table
        .getFilteredSelectedRowModel()
        .rows.map(row => row.original.id);
      try {
        if (selectedRowsIds.length === 0) return;
        const { count } = await deleteMany(selectedRowsIds).unwrap();
        toast.success(`Successfully deleted ${count} addresses`);
        table.setRowSelection({});
      } catch (error) {
        console.error("Failed to delete items", error);
        toast.error(`Failed to delete items`);
      }
    });
  };

  if (noOfSelectedRows > 0)
    return (
      <div className="flex items-center gap-2 h-6">
        <span className="text-sm font-semibold">
          {noOfSelectedRows} selected
        </span>
        <Separator orientation="vertical" className="mx-1" />
        <Button
          size="xs"
          variant="ghost-destructive"
          onClick={handleDeleteMany}
          disabled={isPending}
        >
          {isPending ? <Loader2 className="animate-spin" /> : <Trash />}
          Delete {isPending ? "..." : ""}
        </Button>
      </div>
    );
};

export default Toolbar;
