"use client";

import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";
import styles from "@/styles/Dashboard.module.css";
import ParcelTypeSection from "./_components/ParcelTypeSection";
import ShipFromAddressSection from "./_components/ShipFromAddressSection";
import SkeletonTable from "./_components/table/SkeletonTable";
import { useGetAllAddressQuery } from "@/store/api/addressApi";
import { useGetParcelsQuery } from "@/store/api/parcelApi";
// import PresetSection from "./_components/PresetSection";
// import { useEffect, useState } from "react";

// const mockPresets = [
//   {
//     id: "1",
//     name: "Shop A Preset 1",
//     shipFrom: "Package 1",
//     service: "USPS",
//     packageType: "Box - MD",
//     height: 5,
//     length: 10,
//     weight: 10,
//     width: 10,
//   },
//   {
//     id: "2",
//     name: "Shop A Preset 2",
//     shipFrom: "Package 2",
//     service: "USPS",
//     packageType: "Box - MD",
//     height: 5,
//     length: 10,
//     weight: 10,
//     width: 10,
//   },
// ];

const ShippingSettingsPage = () => {
  const { data: addresses, isLoading: isLoadingAddresses } =
    useGetAllAddressQuery();
  const { data: parcels, isLoading: isLoadingParcels } = useGetParcelsQuery();
  // const { data: presets, isLoading: isLoadingPresets } = useGetPresetsQuery();
  // const [isLoadingPresets, setIsLoadingPresets] = useState(true);
  // const presets = mockPresets;

  // useEffect(() => {
  //   setTimeout(() => {
  //     setIsLoadingPresets(false);
  //   }, 1000);
  // }, [presets]);

  return (
    <>
      <div
        className={cn(styles.panelContainer, styles.panelYSpacing, "border")}
      >
        {isLoadingAddresses || isLoadingParcels ? (
          <>
            <SkeletonTable
              title="Ship From Addresses:"
              titleButton="New Address"
            />
            <Separator />
            <SkeletonTable title="Parcel Type:" titleButton="New Parcel Type" />
            <Separator />
            <SkeletonTable title="Presets:" titleButton="Add New Preset" />{" "}
          </>
        ) : (
          <>
            <ShipFromAddressSection data={addresses || []} />
            <Separator />
            <ParcelTypeSection data={parcels || []} />
            {/* <Separator /> */}
            {/* <PresetSection data={mockPresets} /> */}
          </>
        )}
      </div>
    </>
  );
};

export default ShippingSettingsPage;
