import LogoutWrapper from "@/components/auth/LogoutWrapper";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";
import styles from "@/styles/Dashboard.module.css";
import { LogOut } from "lucide-react";
import BillingSection from "./_components/BillingSection";
import DefaultUnitsFormatSection from "./_components/DefaultUnitsFormatSection";
import NotificationsSection from "./_components/NotificationsSection";
import ProfileSection from "./_components/ProfileSection";

export default async function SettingsPage() {
  return (
    <div className={cn(styles.panelContainer, styles.panelYSpacing, "border")}>
      <ProfileSection />
      <Separator />
      <DefaultUnitsFormatSection />
      <Separator />
      <BillingSection />
      <Separator />
      <NotificationsSection />
      <Separator />
      <LogoutWrapper>
        <Button variant="muted-destructive">
          <LogOut />
          Logout
        </Button>
      </LogoutWrapper>
    </div>
  );
}
