import { Skeleton } from "@/components/ui/skeleton";

type DataFieldProps = {
  label: string;
  value?: string | React.ReactNode;
  isLoading?: boolean;
};

const DataField = ({ label, value, isLoading }: DataFieldProps) => {
  return (
    <div className="flex items-center justify-between">
      <div className="text-foreground font-medium text-sm">{label}</div>
      <div className="text-foreground text-sm">
        {isLoading ? <Skeleton className="w-20 h-4" /> : value}
      </div>
    </div>
  );
};

export default DataField;
