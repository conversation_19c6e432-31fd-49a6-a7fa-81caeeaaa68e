"use client";

import { cn } from "@/lib/utils";
import { useGetBillingAddressQuery } from "@/store/api/accountApi";
import DataField from "../DataField";
import SectionHeader from "../SectionHeader";
import BillingModal from "./BillingModal";

const BillingSection = () => {
  const { data: billingAddress } = useGetBillingAddressQuery();

  return (
    <div className={cn("space-y-4")}>
      <SectionHeader
        title="Billing"
        modal={<BillingModal defaultValues={billingAddress} />}
      />
      <div className="grid sm:grid-cols-2 gap-x-8 gap-y-4">
        <DataField
          label="Name"
          value={billingAddress?.name}
          isLoading={!billingAddress}
        />
        <DataField
          label="Company"
          value={billingAddress?.company}
          isLoading={!billingAddress}
        />
        <DataField
          label="Street 1"
          value={billingAddress?.street1}
          isLoading={!billingAddress}
        />
        <DataField
          label="Street 2"
          value={billingAddress?.street2}
          isLoading={!billingAddress}
        />
        <DataField
          label="City"
          value={billingAddress?.city}
          isLoading={!billingAddress}
        />
        <DataField
          label="State"
          value={billingAddress?.state}
          isLoading={!billingAddress}
        />
        <DataField
          label="Country"
          value={billingAddress?.country}
          isLoading={!billingAddress}
        />
        <DataField
          label="Zip"
          value={billingAddress?.zip}
          isLoading={!billingAddress}
        />
        <DataField
          label="Phone"
          value={billingAddress?.phone}
          isLoading={!billingAddress}
        />
        <DataField
          label="Email"
          value={billingAddress?.email}
          isLoading={!billingAddress}
        />
      </div>
    </div>
  );
};

export default BillingSection;
