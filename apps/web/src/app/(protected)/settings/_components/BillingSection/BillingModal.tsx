"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>it<PERSON> } from "@/components/ui/alert";
import Header from "@/components/layout/Header";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";
import { billingAddressSchema } from "@/schemas/settings/billingAddressSchema";
import { useUpdateBillingAddressMutation } from "@/store/api/accountApi";
import styles from "@/styles/Dashboard.module.css";
import { zodResolver } from "@hookform/resolvers/zod";
import { BillingAddress } from "@repo/database";
import { AlertTriangle, LoaderCircle, Pencil } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

type BillingModalProps = {
  defaultValues?: BillingAddress;
};

const BillingModal = ({ defaultValues }: BillingModalProps) => {
  const [open, setOpen] = useState<boolean>(false);
  const [updateBillingAddress] = useUpdateBillingAddressMutation();
  const [error, setError] = useState<string | null>(null);

  const form = useForm<z.infer<typeof billingAddressSchema>>({
    resolver: zodResolver(billingAddressSchema),
    values: {
      name: defaultValues?.name ?? "",
      company: defaultValues?.company ?? "",
      street1: defaultValues?.street1 ?? "",
      street2: defaultValues?.street2 ?? "",
      city: defaultValues?.city ?? "",
      state: defaultValues?.state ?? "",
      country: defaultValues?.country ?? "",
      zip: defaultValues?.zip ?? "",
      phone: defaultValues?.phone ?? "",
      email: defaultValues?.email ?? "",
    },
  });

  const {
    control,
    formState: { isDirty, isSubmitting },
  } = form;

  const handleSubmit = async (values: z.infer<typeof billingAddressSchema>) => {
    setError(null);

    if (!isDirty) {
      setOpen(false);
      return;
    }

    try {
      await updateBillingAddress({
        data: values,
      }).unwrap();
      setOpen(false);
    } catch (error) {
      if (process.env.NODE_ENV === "development") console.error(error);
      setError("Billing address update failed. Please try again later");
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost-primary" size="xs" className="gap-1">
          <Pencil className="size-3" />
          <div className="text-sm font-medium">Edit</div>
        </Button>
      </DialogTrigger>
      <DialogContent className="md:max-w-xl xl:max-w-2xl">
        <DialogTitle className="sr-only">Billing Address</DialogTitle>
        <DialogDescription className="sr-only" />
        <DialogHeader>
          <Header as="h3">Billing Address</Header>
        </DialogHeader>
        <Separator />
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className={cn(styles.panelYSpacing)}
          >
            <div className={cn(styles.formGrid)}>
              <FormField
                control={control}
                name="name"
                render={({ field }) => (
                  <FormItem className={styles.colSpan2}>
                    <FormLabel>
                      Name
                      <span className="text-muted-foreground text-xs font-normal">
                        (optional)
                      </span>
                    </FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={control}
                name="company"
                render={({ field }) => (
                  <FormItem className={styles.colSpan2}>
                    <FormLabel>
                      Company
                      <span className="text-muted-foreground text-xs font-normal">
                        (optional)
                      </span>
                    </FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={control}
                name="street1"
                render={({ field }) => (
                  <FormItem className={styles.colSpan2}>
                    <FormLabel>Address 1</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={control}
                name="street2"
                render={({ field }) => (
                  <FormItem className={styles.colSpan2}>
                    <FormLabel>
                      Address 2{" "}
                      <span className="text-muted-foreground text-xs font-normal">
                        (optional)
                      </span>
                    </FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={control}
                name="city"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>City</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={control}
                name="state"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      State{" "}
                      <span className="text-muted-foreground text-xs font-normal">
                        (optional)
                      </span>
                    </FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={control}
                name="country"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Country</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger className="w-full">
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="US">United States</SelectItem>
                        <SelectItem value="CA">Canada</SelectItem>
                        <SelectItem value="TH">Thailand</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={control}
                name="zip"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Zip Code</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Phone
                      <span className="text-muted-foreground text-xs font-normal">
                        (optional)
                      </span>
                    </FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Email
                      <span className="text-muted-foreground text-xs font-normal">
                        (optional)
                      </span>
                    </FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            {error && (
              <Alert variant="destructive">
                <AlertTriangle />
                <AlertTitle>{error}</AlertTitle>
              </Alert>
            )}
            <Separator />
            <DialogFooter>
              <DialogClose asChild>
                <Button type="button" variant="ghost-destructive">
                  Cancel
                </Button>
              </DialogClose>

              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting && (
                  <LoaderCircle className="size-4 animate-spin" />
                )}
                Save changes
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default BillingModal;
