"use client";

import { updateProfile } from "@/actions/account/updateProfile";
import { <PERSON><PERSON>, AlertTitle } from "@/components/ui/alert";
import Header from "@/components/layout/Header";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";
import { editProfileSchema } from "@/schemas/settings/editProfileSchema";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { updateAccount } from "@/store/slices/accountSlice";
import styles from "@/styles/Dashboard.module.css";
import { zodResolver } from "@hookform/resolvers/zod";
import { AlertTriangle, LoaderCircle, Pencil } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

const ProfileModal = () => {
  const [open, setOpen] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const dispatch = useAppDispatch();

  const { data: account } = useAppSelector(state => state.account);

  const form = useForm<z.infer<typeof editProfileSchema>>({
    resolver: zodResolver(editProfileSchema),
    values: {
      firstName: account?.firstName ?? "",
      lastName: account?.lastName ?? "",
    },
  });

  const {
    control,
    formState: { isSubmitting, isDirty },
  } = form;

  const handleSubmit = async (values: z.infer<typeof editProfileSchema>) => {
    setError(null);

    if (!isDirty) {
      setOpen(false);
      return;
    }

    const response = await updateProfile(values);

    if (!response.success) {
      setError(response.error ?? "Failed to update profile");
      return;
    }

    if (response.data) {
      dispatch(updateAccount(response.data));
      setOpen(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost-primary" size="xs" className="gap-1">
          <Pencil className="size-3" />
          <div className="text-sm font-medium">Edit</div>
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogTitle className="hidden">Profile</DialogTitle>
        <DialogDescription className="hidden" />
        <DialogHeader>
          <Header as="h3">Profile</Header>
        </DialogHeader>
        <Separator />
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-4 xl:space-y-5"
          >
            <div className={cn(styles.formGrid)}>
              <FormField
                control={control}
                name="firstName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>First Name</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={control}
                name="lastName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Last Name</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            {error && (
              <Alert variant="destructive">
                <AlertTriangle />
                <AlertTitle>{error}</AlertTitle>
              </Alert>
            )}
            <Separator />
            <DialogFooter>
              <DialogClose asChild>
                <Button type="button" variant="ghost-destructive">
                  Cancel
                </Button>
              </DialogClose>

              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting && (
                  <LoaderCircle className="size-4 animate-spin" />
                )}
                Save changes
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default ProfileModal;
