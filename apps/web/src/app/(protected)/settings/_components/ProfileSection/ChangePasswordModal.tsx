"use client";

import { <PERSON><PERSON>, <PERSON>ertDescription, AlertTitle } from "@/components/ui/alert";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import {
  checkPasswordsMatch,
  checkPasswordStrength,
} from "@/lib/utils/password";
import ChangePasswordSchema from "@/schemas/auth/ChangePasswordSchema";
import { zodResolver } from "@hookform/resolvers/zod";
import { AlertCircle, Check, Eye, EyeOff, LoaderCircle, X } from "lucide-react";
import Link from "next/link";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import Header from "@/components/layout/Header";

const ChangePasswordModal = () => {
  const [open, setOpen] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [showPasswords, setShowPasswords] = useState({
    oldPassword: false,
    password: false,
    confirmPassword: false,
  });

  const form = useForm<z.infer<typeof ChangePasswordSchema>>({
    resolver: zodResolver(ChangePasswordSchema),
    defaultValues: {
      oldPassword: "",
      password: "",
      confirmPassword: "",
    },
  });

  const {
    formState: { isSubmitting },
  } = form;

  const onSubmit = (values: z.infer<typeof ChangePasswordSchema>) => {
    setError(null);

    try {
      // TODO: change password
      console.log("change password values =>", values);
      form.reset();
      setOpen(false);
    } catch (error) {
      console.log(error);
      // TODO: Toast error
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant={"secondary"} size="xs" className="gap-1">
          <div className="text-xs font-medium text-foreground">
            Change Password
          </div>
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogTitle className="hidden">Change Password</DialogTitle>
        <DialogDescription className="hidden" />
        <DialogHeader>
          <Header as="h3">Change Password</Header>
        </DialogHeader>
        <Separator />
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="space-y-4 xl:space-y-5"
          >
            <FormField
              control={form.control}
              name="oldPassword"
              render={({ field }) => (
                <FormItem className="md:col-span-2">
                  <FormLabel className="flex items-center gap-1.5">
                    Old Password
                  </FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input
                        type={showPasswords.oldPassword ? "text" : "password"}
                        className="pr-10"
                        {...field}
                      />
                      <Button
                        type="button"
                        size="icon"
                        variant="link"
                        className="absolute right-2 top-1/2 -translate-y-1/2"
                        onClick={() =>
                          setShowPasswords(prev => ({
                            ...prev,
                            oldPassword: !prev.oldPassword,
                          }))
                        }
                      >
                        {showPasswords.oldPassword ? <Eye /> : <EyeOff />}
                      </Button>
                    </div>
                  </FormControl>
                  <FormMessage />
                  <Link
                    href="/"
                    className="text-xs text-right text-primary font-medium hover:underline"
                  >
                    I forgot my password
                  </Link>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem className="md:col-span-2">
                  <FormLabel className="flex items-center gap-1.5">
                    New Password
                  </FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input
                        // variant="ghost"
                        type={showPasswords.password ? "text" : "password"}
                        className="pr-10"
                        {...field}
                      />
                      {field.value && !checkPasswordStrength(field.value) && (
                        <X
                          size={20}
                          className="absolute right-11 top-1/2 -translate-y-1/2 text-destructive/50"
                        />
                      )}
                      {checkPasswordStrength(field.value) && (
                        <Check
                          size={20}
                          className="absolute right-11 top-1/2 -translate-y-1/2 text-emerald-500"
                        />
                      )}
                      <Button
                        type="button"
                        size="icon"
                        variant="link"
                        className="absolute right-2 top-1/2 -translate-y-1/2"
                        onClick={() =>
                          setShowPasswords(prev => ({
                            ...prev,
                            password: !prev.password,
                          }))
                        }
                      >
                        {showPasswords.password ? <Eye /> : <EyeOff />}
                      </Button>
                    </div>
                  </FormControl>
                  <FormDescription className="mt-1 text-xs">
                    At least 8 chars with 1 uppercase, 1 lowercase and 1 number
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="confirmPassword"
              render={({ field }) => (
                <FormItem className="md:col-span-2">
                  <FormLabel>Confirm Password</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input
                        // variant="ghost"
                        type={
                          showPasswords.confirmPassword ? "text" : "password"
                        }
                        className="pr-10"
                        {...field}
                      />
                      {checkPasswordStrength(field.value) &&
                        checkPasswordsMatch(
                          field.value,
                          form.getValues("password")
                        ) && (
                          <Check
                            size={20}
                            className="absolute right-11 top-1/2 -translate-y-1/2 text-emerald-500"
                          />
                        )}
                      <Button
                        type="button"
                        size="icon"
                        variant="link"
                        className="absolute right-2 top-1/2 -translate-y-1/2"
                        onClick={() =>
                          setShowPasswords(prev => ({
                            ...prev,
                            confirmPassword: !prev.confirmPassword,
                          }))
                        }
                      >
                        {showPasswords.confirmPassword ? <Eye /> : <EyeOff />}
                      </Button>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            {error && error !== "" && (
              <Alert variant="destructive" className="md:col-span-2">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Error</AlertTitle>
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}
            <DialogFooter>
              <DialogClose asChild>
                <Button type="button" variant="ghost-destructive">
                  Cancel
                </Button>
              </DialogClose>

              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting && (
                  <LoaderCircle className="size-4 animate-spin" />
                )}
                Update Password
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default ChangePasswordModal;
