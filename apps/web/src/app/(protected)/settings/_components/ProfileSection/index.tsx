import UserProfileCard from "@/app/(protected)/_components/common/user/UserProfileCard";
import { cn } from "@/lib/utils";
import styles from "@/styles/Dashboard.module.css";
import SectionHeader from "../SectionHeader";
import ChangePasswordModal from "./ChangePasswordModal";
import ProfileModal from "./ProfileModal";

const ProfileSection = () => {
  return (
    <div className={cn(styles.panelYSpacing)}>
      <div className="space-y-4">
        <SectionHeader title="Profile" modal={<ProfileModal />} />
        <div className="grid sm:grid-cols-2 gap-x-8 gap-y-4">
          <div className="flex items-center space-x-5">
            <UserProfileCard canEdit={true} />
          </div>

          <div className="flex justify-end items-center">
            <ChangePasswordModal />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileSection;
