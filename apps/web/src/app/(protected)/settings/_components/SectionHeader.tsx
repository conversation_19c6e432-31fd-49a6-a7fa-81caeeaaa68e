"use client";
import { cn } from "@/lib/utils";

interface SectionHeaderProps {
  title: string;
  modal: React.ReactNode;
}

const SectionHeader = ({ title, modal }: SectionHeaderProps) => {
  return (
    <div className={cn("flex items-center justify-between")}>
      <h2 className="text-sm lg:text-base xl:text-lg font-semibold">{title}</h2>
      <div>{modal}</div>
    </div>
  );
};

export default SectionHeader;
