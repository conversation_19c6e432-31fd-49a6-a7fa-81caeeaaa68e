"use client";

import { cn } from "@/lib/utils";
import { useEffect, useRef, useState } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";

const links = [
  { pathname: "/settings", label: "Account" },
  { pathname: "/settings/shipping", label: "Shipping" },
  { pathname: "/settings/label-packing-slips", label: "Label & Packing Slips" },
  // { pathname: "/settings/billing", label: "Billing" },
];

const Navbar = () => {
  const pathname = usePathname();
  const [mounted, setMounted] = useState(false);
  const listRef = useRef<HTMLDivElement>(null);
  const [selectedTabWidth, setSelectedTabWidth] = useState(0);
  const [selectedTabLeft, setSelectedTabLeft] = useState(0);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!mounted || !listRef.current) return;

    const active = listRef.current.querySelector(
      '[data-state="active"]'
    ) as HTMLElement;
    if (active) {
      setSelectedTabWidth(active.clientWidth);
      setSelectedTabLeft(active.offsetLeft);
    }
  }, [pathname, mounted]);

  return (
    <div
      ref={listRef}
      className={cn(
        "relative inline-flex h-9 w-fit items-center justify-center",
        "before:content-[''] before:block before:absolute before:top-full before:left-0 before:h-[3px] before:bg-foreground before:translate-x-0 before:transition-all before:duration-300 before:ease-out",
        `before:w-[var(--before-width)] before:left-[var(--before-left)]`
      )}
      style={
        mounted
          ? ({
              "--before-width": `${selectedTabWidth}px`,
              "--before-left": `${selectedTabLeft}px`,
            } as React.CSSProperties)
          : undefined
      }
    >
      {links.map(link => (
        <Link
          key={link.pathname}
          href={link.pathname}
          data-state={
            mounted && pathname === link.pathname ? "active" : undefined
          }
          className={cn(
            "text-sm text-muted-foreground data-[state=active]:text-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 px-3 sm:px-4 md:px-6 xl:px-8 py-1 font-semibold whitespace-nowrap transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",
            mounted && pathname === link.pathname && "text-foreground"
          )}
        >
          {link.label}
        </Link>
      ))}
    </div>
  );
};

export default Navbar;
