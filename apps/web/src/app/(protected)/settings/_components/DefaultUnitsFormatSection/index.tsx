"use client";

import { cn } from "@/lib/utils";
import DataField from "../DataField";
import SectionHeader from "../SectionHeader";
import UnitFormatModal from "./UnitFormatModal";

const DefaultUnitsFormatSection = () => {
  return (
    <div className={cn("space-y-4")}>
      <SectionHeader
        title="Default Units & Formats"
        modal={<UnitFormatModal />}
      />
      <div className="grid sm:grid-cols-2 gap-x-8 gap-y-4">
        <DataField label="Measurement" value="Imperial" isLoading={false} />
        <DataField label="Currency" value="USD" isLoading={false} />
      </div>
    </div>
  );
};

export default DefaultUnitsFormatSection;
