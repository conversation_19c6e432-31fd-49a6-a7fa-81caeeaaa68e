"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useState } from "react";
import Header from "@/components/layout/Header";
import { cn } from "@/lib/utils";
import { useForm } from "react-hook-form";
import UnitFormatSchema, {
  defaultValues,
} from "@/schemas/settings/UnitFormatSchema";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { LoaderCircle, Pencil } from "lucide-react";
import { Separator } from "@/components/ui/separator";
import styles from "@/styles/Dashboard.module.css";
import { UNIT_SYSTEMS } from "@/config/unitSystem";

const UnitFormatModal = () => {
  const [open, setOpen] = useState(false);
  // const [isLoading, setIsLoading] = useState(false);

  const form = useForm<z.infer<typeof UnitFormatSchema>>({
    resolver: zodResolver(UnitFormatSchema),
    defaultValues: defaultValues,
    // TODO: add default values
  });

  const {
    control,
    formState: { isSubmitting },
  } = form;

  const handleSubmit = async (values: z.infer<typeof UnitFormatSchema>) => {
    try {
      console.log("values =>", values);

      // TODO: update profile

      // TODO: Toast successful update

      form.reset();
      setOpen(false);
    } catch (error) {
      console.log(error);
      // TODO: Toast error
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost-primary" size="xs" className="gap-1">
          <Pencil className="size-3" />
          <div className="text-sm font-medium">Edit</div>
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogTitle className="hidden">Default Units & Formats</DialogTitle>
        <DialogDescription className="hidden" />
        <DialogHeader>
          <Header as="h3">Default Units & Formats</Header>
        </DialogHeader>
        <Separator />
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-4 xl:space-y-5"
          >
            <div className={cn(styles.formGrid)}>
              <FormField
                control={control}
                name="measurement"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Measurement System</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger className="w-full">
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Object.keys(UNIT_SYSTEMS).map(system => (
                          <SelectItem
                            key={system}
                            value={system}
                            className="capitalize"
                          >
                            <div className="capitalize">{system}</div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={control}
                name="currency"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Currency</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger className="w-full">
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="US Dollars (USD)">
                          US Dollars (USD)
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <Separator />
            <DialogFooter>
              <DialogClose asChild>
                <Button type="button" variant="ghost-destructive">
                  Cancel
                </Button>
              </DialogClose>

              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting && (
                  <LoaderCircle className="size-4 animate-spin" />
                )}
                Save changes
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default UnitFormatModal;
