"use client";

import { cn } from "@/lib/utils";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";

const NotificationsSection = () => {
  return (
    <div className={cn("space-y-4")}>
      <h2 className="text-sm lg:text-base xl:text-lg font-semibold">
        Notifications
      </h2>

      <div className="flex items-start justify-between space-x-4">
        <div>
          <div className="text-foreground font-medium text-sm">
            Low Wallet Balance Alert
          </div>
          <div className={cn("text-foreground text-xs")}>
            Dropright will notify users with finance permission through email
            when the wallet amount is below threshold.
          </div>
        </div>
        <div className="flex items-center">
          <Label
            htmlFor="wallet-balance-alert"
            className="text-foreground mr-2"
          >
            Alert
          </Label>
          <Switch id="wallet-balance-alert" />
        </div>
      </div>
    </div>
  );
};

export default NotificationsSection;
