"use client";

import { deleteProfileImage } from "@/actions/account/deleteProfileImage";
import { updateProfileImage } from "@/actions/account/uploadProfileImage";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { getInitials, getPermissions } from "@/lib/utils/user";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { updateAccount } from "@/store/slices/accountSlice";
import { Settings, Trash, Upload } from "lucide-react";
import { useRef } from "react";
import { toast } from "sonner";

type UserProfileCardProps = {
  canEdit?: boolean;
};

export default function UserProfileCard({
  canEdit = false,
}: UserProfileCardProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { data: account } = useAppSelector(state => state.account);
  const dispatch = useAppDispatch();

  const fullName =
    account?.firstName && account?.lastName
      ? `${account?.firstName} ${account?.lastName}`
      : "";

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.readAsDataURL(file);

      const formData = new FormData();
      formData.append("image", file);

      const response = await updateProfileImage(formData);

      if (!response.success) {
        toast.error(response.error);
        return;
      }

      if (response.data) {
        dispatch(updateAccount(response.data));
      }
      toast.success("Profile image updated");
    }
  };

  const handleDelete = async () => {
    if (fileInputRef.current) fileInputRef.current.value = "";

    const response = await deleteProfileImage();
    if (!response.success) {
      toast.error(response.error);
      return;
    }

    if (response.data) {
      dispatch(updateAccount(response.data));
    }
    toast.success("Profile image removed");
  };

  return (
    <>
      <div className="relative">
        <Avatar
          key={account?.image || "fallback"}
          className="flex items-center justify-center size-20"
        >
          {account?.image ? (
            <AvatarImage
              src={account?.image ?? undefined}
              className="object-cover"
            />
          ) : (
            <AvatarFallback>{getInitials(fullName)}</AvatarFallback>
          )}
        </Avatar>

        {canEdit && (
          <>
            <input
              type="file"
              accept="image/*"
              ref={fileInputRef}
              className="hidden"
              onChange={handleFileChange}
            />
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <button
                  type="button"
                  className="size-5 rounded-full bg-secondary absolute bottom-0 right-0 cursor-pointer"
                >
                  <Settings className="h-3.5 w-full flex justify-between items-center" />
                </button>
              </DropdownMenuTrigger>

              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => fileInputRef.current?.click()}>
                  <Upload />
                  Upload
                </DropdownMenuItem>
                {account?.image && (
                  <DropdownMenuItem
                    variant="destructive"
                    onClick={handleDelete}
                  >
                    <Trash />
                    Remove
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </>
        )}
      </div>
      <div>
        <div className="font-semibold text-accent text-sm">{fullName}</div>
        <div className="text-accent text-xs capitalize">
          {getPermissions(account?.permissions ?? [])}
        </div>
        <div className="text-accent text-xs">{account?.email}</div>
      </div>
    </>
  );
}
