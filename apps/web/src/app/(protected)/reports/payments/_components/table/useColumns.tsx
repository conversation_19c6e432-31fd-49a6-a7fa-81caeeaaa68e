import Status from "@/components/common/Status";
import { snakeCaseToText } from "@/lib/utils/strings";
import { GetPaymentsResponse } from "@/store/types/payment";
import { WalletRecentPayment } from "@/store/types/walletDashboard";
import { ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";

export const createPaymentColumns = <
  T extends WalletRecentPayment | GetPaymentsResponse["data"][number],
>(): ColumnDef<T>[] => [
  {
    accessorKey: "initiatedAt",
    header: "Date/Time",
    cell: ({ row }) => (
      <div>{format(row.getValue("initiatedAt"), "MM/dd/yyyy - hh.mm")}</div>
    ),
  },
  {
    accessorKey: "amount",
    header() {
      return <div className="text-right">Amount</div>;
    },
    cell: ({ row }) => {
      const { amount } = row.original;
      return <div className="text-right">${amount}</div>;
    },
  },
  {
    accessorKey: "fee",
    header() {
      return <div className="text-right">Fee</div>;
    },
    cell: ({ row }) => {
      const { fee } = row.original;
      return <div className="text-right">${fee}</div>;
    },
  },
  {
    accessorKey: "type",
    header: "Type",
    cell: ({ row }) => {
      const { type } = row.original;
      return <div>{type ? snakeCaseToText(type) : "-"}</div>;
    },
  },
  {
    accessorKey: "paymentMethod",
    header: "Payment Method",
    cell: ({ row }) => {
      const { paymentMethod } = row.original;

      const text =
        paymentMethod && paymentMethod?.type === "ach" ? (
          <>
            {paymentMethod?.bankAccountName && paymentMethod?.bankName
              ? paymentMethod?.bankAccountName.length > 8
                ? `${paymentMethod?.bankAccountName.slice(0, 8)}** (${paymentMethod?.bankName})`
                : `${snakeCaseToText(paymentMethod?.bankAccountName)} (${snakeCaseToText(paymentMethod?.bankName)})`
              : "-"}
          </>
        ) : (
          <>
            {paymentMethod?.cardBrand && paymentMethod?.cardLast4
              ? `${snakeCaseToText(paymentMethod?.cardBrand)}****${paymentMethod?.cardLast4}`
              : "-"}
          </>
        );

      return <div>{text}</div>;
    },
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const { status } = row.original;
      return (
        <div>
          <Status text={status} />
        </div>
      );
    },
  },
];
