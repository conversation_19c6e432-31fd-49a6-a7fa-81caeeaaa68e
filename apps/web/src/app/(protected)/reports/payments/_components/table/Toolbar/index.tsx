import { DateRange } from "react-day-picker";
// import DateRangeSelect from "./DateRangeSelect";
import DateRangeSelect from "@/components/common/table/DateRangeSelect";
import { GetPaymentsResponse } from "@/store/types/payment";
import { Table } from "@tanstack/react-table";
import StatusSelect from "./StatusSelect";

type ToolbarProps = {
  table: Table<GetPaymentsResponse["data"][number]>;
  dateRange: DateRange;
  onDateFilter: (range: DateRange) => void;
};

const Toolbar = ({ table, dateRange, onDateFilter }: ToolbarProps) => {
  return (
    <div className="flex md:items-end md:justify-between gap-4 md:flex-row flex-col">
      {/* LEFT */}
      <DateRangeSelect initialValue={dateRange} onApply={onDateFilter} />

      {/* RIGHT */}
      <div className="flex items-center gap-2 h-8">
        <StatusSelect table={table} />
      </div>
    </div>
  );
};

export default Toolbar;
