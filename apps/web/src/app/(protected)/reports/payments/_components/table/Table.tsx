"use client";

import DataTable from "@/components/common/table/DataTable";
import { createPaginationHandler } from "@/lib/utils/table/pagination";
import { useGetPaymentsQuery } from "@/store/api/paymentApi";
import { GetPaymentsResponse } from "@/store/types/payment";
import {
  ColumnDef,
  ColumnFilter,
  ColumnFiltersState,
  getCoreRowModel,
  OnChangeFn,
  PaginationState,
  Updater,
  useReactTable,
  VisibilityState,
} from "@tanstack/react-table";
import { startOfMonth } from "date-fns";
import { useCallback, useMemo, useState } from "react";
import { DateRange } from "react-day-picker";
import SkeletonTable from "./SkeletonTable";
import Toolbar from "./Toolbar";
import { createPaymentColumns } from "./useColumns";

const PAGE_SIZE_OPTIONS = [25, 50, 75, 100];
const DEFAULT_PAGE_SIZE = PAGE_SIZE_OPTIONS[0];
const DEFAULT_PAGE_INDEX = 0;

const TableWrapper = () => {
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: DEFAULT_PAGE_INDEX,
    pageSize: DEFAULT_PAGE_SIZE,
  });
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);

  const today = new Date();

  const [dateRange, setDateRange] = useState<DateRange>({
    from: startOfMonth(today),
    to: today,
  });

  const queryStr = useMemo(() => {
    const query = new URLSearchParams();
    // Pagination
    query.append("limit", pagination.pageSize.toString());
    query.append(
      "offset",
      (pagination.pageIndex * pagination.pageSize).toString()
    );
    // Filters
    columnFilters.forEach((filter: ColumnFilter) => {
      if (typeof filter.value === "object" && filter.value !== null) {
        Object.entries(filter.value).forEach(([key, value]) => {
          query.append(`filter[${filter.id}][${key}]`, value);
        });
      } else query.append(`filter[${filter.id}]`, `${filter.value}`);
    });
    // Filter Date
    if (dateRange.from) query.append("fromDate", dateRange.from.toISOString());
    if (dateRange.to) query.append("toDate", dateRange.to.toISOString());

    return query.toString();
  }, [pagination, columnFilters, dateRange]);

  const columns = [
    ...createPaymentColumns<GetPaymentsResponse["data"][number]>(),
  ];

  const { data: payments, isLoading } = useGetPaymentsQuery(queryStr);

  const handleDateFilter = useCallback((range: DateRange) => {
    setDateRange(range);
    setPagination(prev => ({ ...prev, pageIndex: DEFAULT_PAGE_INDEX }));
  }, []);

  return (
    <>
      {!payments || !columns || isLoading ? (
        <SkeletonTable />
      ) : (
        <>
          <Table
            data={payments}
            columns={columns}
            pagination={pagination}
            setPagination={setPagination}
            dateRange={dateRange}
            onDateFilter={handleDateFilter}
            columnFilters={columnFilters}
            setColumnFilters={setColumnFilters}
          />
        </>
      )}
    </>
  );
};

type TableProps = {
  data: GetPaymentsResponse;
  columns: ColumnDef<GetPaymentsResponse["data"][number]>[];
  pagination: PaginationState;
  dateRange: DateRange;
  setPagination: React.Dispatch<React.SetStateAction<PaginationState>>;
  columnFilters: ColumnFiltersState;
  setColumnFilters: React.Dispatch<React.SetStateAction<ColumnFiltersState>>;
  onDateFilter: (range: DateRange) => void;
};

const Table = ({
  data,
  columns,
  pagination,
  dateRange,
  setPagination,
  columnFilters,
  setColumnFilters,
  onDateFilter,
}: TableProps) => {
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});

  const onPaginationChange = useCallback(
    (updater: Updater<PaginationState>) =>
      createPaginationHandler(
        data.pagination,
        pagination,
        setPagination
      )(updater),
    [data.pagination, pagination, setPagination]
  );

  const table = useReactTable({
    data: data.data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    // Pagination
    manualPagination: true,
    rowCount: data.pagination.total,
    onPaginationChange: onPaginationChange as OnChangeFn<PaginationState>,
    // Filtering
    manualFiltering: true,
    onColumnFiltersChange: setColumnFilters as OnChangeFn<ColumnFiltersState>,
    // Column Visibility Settings
    onColumnVisibilityChange: setColumnVisibility,
    state: {
      pagination,
      columnFilters,
      columnVisibility,
    },
  });

  return (
    <div className="flex flex-col gap-4">
      <Toolbar
        table={table}
        dateRange={dateRange}
        onDateFilter={onDateFilter}
      />

      <DataTable
        table={table}
        pageSizeOptions={PAGE_SIZE_OPTIONS}
        height="h-[calc(100dvh-329px)]"
      />
    </div>
  );
};

export default TableWrapper;
