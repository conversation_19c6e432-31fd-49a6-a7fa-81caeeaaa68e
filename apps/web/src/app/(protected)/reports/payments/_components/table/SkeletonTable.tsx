import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";

type SkeletonTableProps = {
  className?: string;
  tableHeight?: string;
};

const SkeletonTable = ({
  className,
  tableHeight = "h-[calc(100dvh-375px)]",
}: SkeletonTableProps) => {
  return (
    <div className={cn(className, "flex flex-col gap-4")}>
      <div className="flex md:justify-between md:items-end space-x-2 md:flex-row flex-col gap-4">
        {/* toolbar left */}
        <div className="flex gap-1.5 items-end w-full md:w-auto">
          <div className="space-y-1 w-full">
            <Skeleton className="w-36 h-4 rounded-full" />
            <Skeleton className="w-full md:w-[230px] h-9 rounded-full" />
          </div>
          <Skeleton className="size-9 rounded-full shrink-0" />
        </div>

        <Skeleton className="h-9 w-[140px] rounded-full" />
      </div>
      <Skeleton className={cn(tableHeight, "grow-1 rounded-lg")} />
    </div>
  );
};

export default SkeletonTable;
