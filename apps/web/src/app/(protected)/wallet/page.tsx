import { cn } from "@/lib/utils";
import styles from "@/styles/Dashboard.module.css";
import { Wallet } from "lucide-react";
import PageHeader from "../../../components/layout/PageHeader";
import Account from "./_components/Account";
import Balance from "./_components/Balance";
import PaymentMethods from "./_components/PaymentMethods";
import DataTables from "./_components/DataTables";

export default function WalletPage() {
  return (
    <div className={cn(styles.pagePadding, "relative")}>
      <PageHeader header="My Wallet" icon={Wallet} />
      <main
        className={cn(
          styles.gridGap,
          "mt-4 lg:mt-6 grid grid-cols-1 lg:grid-cols-[3fr_2fr] xl:grid-cols-[2fr_1fr] items-start"
        )}
      >
        {/* Left Panels */}
        <div className={cn(styles.gridGap, "flex flex-col overflow-x-auto")}>
          <Balance />
          <DataTables />
        </div>

        {/* Right Panels */}
        <div className={cn(styles.gridGap, "flex flex-col")}>
          <PaymentMethods />
          <Account />
        </div>
      </main>
    </div>
  );
}
