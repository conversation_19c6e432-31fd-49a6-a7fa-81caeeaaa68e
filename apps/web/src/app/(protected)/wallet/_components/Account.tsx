"use client";

import Header from "@/components/layout/Header";
import { Skeleton } from "@/components/ui/skeleton";
import { MAX_USERS_PER_ORGANIZATION } from "@/constants/account";
import { cn } from "@/lib/utils";
import { useGetWalletDashboardQuery } from "@/store/api/accountApi";
import styles from "@/styles/Dashboard.module.css";
import Link from "next/link";

const Account = () => {
  const { data, isLoading } = useGetWalletDashboardQuery();

  return (
    <section
      className={cn(styles.panelContainerNoBorder, styles.panelYSpacing)}
    >
      <Header variant="section">Account Details</Header>

      <div className="grid grid-cols-2 text-sm gap-y-1.5 md:gap-y-2">
        <div className="font-medium text-foreground">Shipment Volume</div>
        {isLoading ? (
          <div className="flex justify-end">
            <Skeleton className="w-40" />
          </div>
        ) : (
          <div className="text-right text-foreground">
            {data?.shipmentVolume} / month
          </div>
        )}

        <div className="font-medium text-foreground">Users</div>
        {isLoading ? (
          <div className="flex justify-end">
            <Skeleton className="w-32" />
          </div>
        ) : (
          <div className="text-right text-foreground">
            {data?._count.users} out of {MAX_USERS_PER_ORGANIZATION} users
          </div>
        )}

        <div className="font-medium text-foreground">Monthly Archives</div>
        <Link
          href="/wallet/transactions"
          className="text-primary font-medium text-right hover:underline underline-offset-4"
        >
          View report
        </Link>
      </div>
    </section>
  );
};

export default Account;
