"use client";
import Header from "@/components/layout/Header";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useGetWalletDashboardQuery } from "@/store/api/accountApi";
import styles from "@/styles/Dashboard.module.css";
import Link from "next/link";
import PaymentMethodCard from "./PaymentMethodCard";
import { Skeleton } from "@/components/ui/skeleton";

const PaymentMethods = () => {
  const { data, isLoading } = useGetWalletDashboardQuery();

  return (
    <section
      className={cn(styles.panelContainerNoBorder, styles.panelYSpacing)}
    >
      <div className="flex justify-between items-center">
        <Header variant="section">Payment Methods</Header>
        <Button variant="ghost-primary" size="xs" asChild>
          <Link href="/wallet/payment-methods">Manage</Link>
        </Button>
      </div>

      {!data || isLoading
        ? Array.from({ length: 3 }).map((_, index) => (
            <div key={index} className="flex justify-between">
              <div className="space-x-4 flex">
                <Skeleton className="w-8 h-4 md:w-[72px] md:h-10 rounded-none" />
                <div className="space-y-1">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-4 w-14" />
                </div>
              </div>
              <Skeleton className="h-6 w-14 rounded-lg" />
            </div>
          ))
        : data.paymentMethods.map(paymentMethod => (
            <PaymentMethodCard key={paymentMethod.id} data={paymentMethod} />
          ))}
    </section>
  );
};

export default PaymentMethods;
