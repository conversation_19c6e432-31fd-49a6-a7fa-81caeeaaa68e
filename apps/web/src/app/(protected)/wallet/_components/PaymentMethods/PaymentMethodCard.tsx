import { Button } from "@/components/ui/button";
import { PaymentMethod, PaymentMethodStatus } from "@repo/database";
import PaymentMethodIcon from "../common/PaymentMethodIcon";
import PaymentMethodInfo from "../common/PaymentMethodInfo";
import { useSetDefaultMutation } from "@/store/api/paymentMethodsApi";
import { Loader2 } from "lucide-react";
import { accountApi } from "@/store/api/accountApi";
import { useAppDispatch } from "@/store/hooks";

type PaymentMethodCardProps = {
  data: PaymentMethod;
};

const PaymentMethodCard = ({ data }: PaymentMethodCardProps) => {
  const { id, type, status, isDefault, cardBrand } = data;
  const [setDefault, { isLoading: settingDefault }] = useSetDefaultMutation();
  const dispatch = useAppDispatch();

  const handleSetDefault = async (id: string) => {
    try {
      const response = await setDefault(id).unwrap();
      if (response) {
        dispatch(
          accountApi.util.updateQueryData(
            "getWalletDashboard",
            undefined,
            draft => {
              if (draft?.paymentMethods) {
                draft.paymentMethods.forEach(pm => {
                  pm.isDefault = pm.id === id;
                });
              }
            }
          )
        );
      }
    } catch (error) {
      if (process.env.NODE_ENV === "development")
        console.error("Failed to set default of payment method:", error);
    }
  };

  return (
    <div className="flex justify-between">
      <div className="flex items-center space-x-4">
        <PaymentMethodIcon type={type} cardBrand={cardBrand} />
        <PaymentMethodInfo data={data} />
      </div>

      {isDefault ? (
        <Button variant="accent" size="xs">
          Default
        </Button>
      ) : (
        <Button
          variant="ghost-primary"
          size="xs"
          disabled={status !== PaymentMethodStatus.verified}
          onClick={() => handleSetDefault(id)}
        >
          {settingDefault ? (
            <Loader2 className="size-5 animate-spin" />
          ) : (
            <span>Set as default</span>
          )}
        </Button>
      )}
    </div>
  );
};

export default PaymentMethodCard;
