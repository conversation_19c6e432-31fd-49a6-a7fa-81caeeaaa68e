import { getPaymentTypeIcon } from "@/lib/mappings/paymentTypeIcon";
import { CreditCardBrand, PaymentMethodType } from "@repo/database";
import Image from "next/image";

type PaymentMethodIconType = {
  type: PaymentMethodType;
  cardBrand?: CreditCardBrand | null;
};

const PaymentMethodIcon = ({ type, cardBrand }: PaymentMethodIconType) => {
  const icon = getPaymentTypeIcon(type, cardBrand);

  return (
    <div className="bg-muted h-8 md:h-10 aspect-video grid place-items-center">
      <div className="relative w-full h-full max-w-8 max-h-4 md:max-w-10 md:max-h-5.5">
        <Image
          className="object-contain"
          fill
          src={icon}
          alt={`${type} ${cardBrand ?? ""}`}
        />
      </div>
    </div>
  );
};

export default PaymentMethodIcon;
