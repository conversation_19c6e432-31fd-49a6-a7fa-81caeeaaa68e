import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { MINIMUM_TOP_UP_AMOUNT, TOP_UP_AMOUNTS } from "@/constants/wallet";
import { cn } from "@/lib/utils";
import customStyles from "@/styles/Custom.module.css";
import { useEffect, useRef, useState } from "react";

type TopUpAmountSelectorProps = {
  value: number;
  onChange: (value: number) => void;
};

const TopUpAmountSelector = ({ value, onChange }: TopUpAmountSelectorProps) => {
  const [customAmount, setCustomAmount] = useState(300);
  const [isOther, setIsOther] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    const isCustomAmount = !TOP_UP_AMOUNTS.includes(value);
    if (isCustomAmount) {
      setIsOther(true);
      setCustomAmount(value);
    } else {
      setIsOther(false);
    }
  }, [value]);

  const handleClick = (value: number | string) => {
    if (typeof value === "string" && value === "Other") {
      onChange(customAmount);
      setIsOther(true);
      inputRef.current?.focus();
    } else {
      onChange(value as number);
      setIsOther(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = Number(e.target.value);
    setCustomAmount(value);
    onChange(value);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "." || e.key === ",") {
      e.preventDefault();
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-wrap gap-2">
        {TOP_UP_AMOUNTS.map((amount, index) => (
          <Button
            type="button"
            size="sm"
            key={`${amount}-${index}`}
            variant={
              isOther
                ? amount === "Other"
                  ? "accent"
                  : "muted"
                : amount === value
                  ? "accent"
                  : "muted"
            }
            onClick={() => handleClick(amount)}
          >
            {typeof amount === "number" ? `$${amount}` : amount}
          </Button>
        ))}
      </div>

      <div
        className={cn(
          "transition-all duration-300 overflow-y-hidden overflow-x-visible",
          isOther ? "h-24 sm:h-12 opacity-100" : "h-0 opacity-0"
        )}
      >
        <div
          className={cn(
            "py-1 px-[1px] grid gap-3 grid-cols-1 sm:grid-cols-2 items-center"
          )}
        >
          <Label
            htmlFor="customAmount"
            className="text-sm text-foreground flex-col gap-0 items-start"
          >
            Specify amount:
            <div className="text-xs font-normal text-muted-foreground">
              Minimum amount is $10
            </div>
          </Label>
          <div>
            <div className="relative w-full max-w-58 justify-self-end">
              <div className="text-muted-foreground absolute top-1.5 left-3">
                $
              </div>
              <Input
                id="customAmount"
                ref={inputRef}
                value={customAmount}
                onChange={handleInputChange}
                type="number"
                step="1"
                min={MINIMUM_TOP_UP_AMOUNT}
                className={cn(customStyles.inputNoArrows, "text-right")}
                onKeyDown={handleKeyDown}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TopUpAmountSelector;
