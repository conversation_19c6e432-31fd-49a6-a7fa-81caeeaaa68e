import { cn } from "@/lib/utils";
import {
  PaymentMethod,
  PaymentMethodStatus,
  PaymentMethodType,
} from "@repo/database";

const AchInfo = ({ data }: { data: PaymentMethod }) => {
  const { bankAccountName, bankName, status } = data;
  return (
    <div>
      <div className="text-sm text-foreground font-medium">
        {bankAccountName && bankName
          ? bankAccountName.length > 8
            ? `${bankAccountName.slice(0, 8)}** (${bankName})`
            : `${bankAccountName} (${bankName})`
          : "-"}
      </div>
      <div className="text-xs font-medium space-x-1">
        <span
          className={cn(
            "capitalize",
            status === PaymentMethodStatus.verified
              ? "text-success"
              : "text-muted-foreground"
          )}
        >
          {status}
        </span>
      </div>
    </div>
  );
};

const CreditCardInfo = ({ data }: { data: PaymentMethod }) => {
  const { cardBrand, cardLast4, cardExpMonth, cardExpYear, status } = data;
  return (
    <div>
      <div className="text-sm text-foreground font-medium capitalize">
        {cardBrand && cardLast4 ? `${cardBrand}****${cardLast4}` : "-"}
      </div>
      <div className="text-xs font-medium space-x-1">
        <div
          className={cn(
            "capitalize",
            status === PaymentMethodStatus.expired
              ? "text-destructive"
              : "text-muted-foreground"
          )}
        >
          <span>Expires </span>
          <span>
            {cardExpMonth && cardExpYear
              ? `${String(cardExpMonth).padStart(2, "0")}/${String(cardExpYear).slice(-2)}`
              : "-"}
          </span>
        </div>
      </div>
    </div>
  );
};

const PaymentMethodInfo = ({ data }: { data: PaymentMethod }) => {
  const paymentInfoHandler = {
    [PaymentMethodType.ach]: <AchInfo data={data} />,
    [PaymentMethodType.credit_card]: <CreditCardInfo data={data} />,
  };

  return paymentInfoHandler[data.type];
};

export default PaymentMethodInfo;
