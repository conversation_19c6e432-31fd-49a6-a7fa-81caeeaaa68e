"use client";

import FileUpload from "@/components/common/FileUpload";
import { <PERSON><PERSON>, AlertTitle } from "@/components/ui/alert";
import Header from "@/components/layout/Header";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";
import droprightLogo from "@/public/logos/primary-light-logo-only.svg";
import {
  defaultValues,
  wireTransferSchema,
} from "@/schemas/wallet/wireTransferSchema";
import customStyles from "@/styles/Custom.module.css";
import { zodResolver } from "@hookform/resolvers/zod";
import { format } from "date-fns";
import {
  AlertTriangleIcon,
  CalendarIcon,
  Copy,
  LandmarkIcon,
  Loader2,
} from "lucide-react";
import Image from "next/image";
import { useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { z } from "zod";
import { useCreateWireTransferMutation } from "@/store/api/walletApi";
import { toast } from "sonner";
import CopyTextWrapper from "@/components/common/CopyTextWrapper";
import { useAppDispatch } from "@/store/hooks";
import { accountApi } from "@/store/api/accountApi";

const MAX_FILE_NUM = 10;
const VALID_FILE_TYPES = [".pdf", ".jpg", ".jpeg", ".png", ".webp"];

const WireTransferForm = () => {
  const [open, setOpen] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [createWireTransfer, { isLoading }] = useCreateWireTransferMutation();
  const dispatch = useAppDispatch();

  const form = useForm<z.infer<typeof wireTransferSchema>>({
    resolver: zodResolver(wireTransferSchema),
    defaultValues: defaultValues,
  });

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "." || e.key === ",") {
      e.preventDefault();
    }
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    onChange: (value: string) => void
  ) => {
    const value = e.target.value;
    if (value.includes(".")) {
      e.target.value = value.split(".")[0];
    }
    // Remove leading zeros but keep single "0"
    const trimmedValue = value.replace(/^0+/, "") || "0";
    onChange(trimmedValue);
  };

  const handleClose = () => {
    setOpen(false);
    setError(null);
    form.reset();
  };

  const handleSubmit = async (values: z.infer<typeof wireTransferSchema>) => {
    try {
      const formData = new FormData();
      Object.entries(values).forEach(([key, value]) => {
        if (key === "transferConfirmation" && Array.isArray(value)) {
          value.forEach(file => {
            formData.append("transferConfirmation", file);
          });
        } else if (value instanceof Date) {
          formData.append(key, value.toISOString());
        } else if (typeof value === "number") {
          formData.append(key, String(value));
        } else if (value !== undefined && value !== null) {
          formData.append(key, String(value));
        }
      });
      await createWireTransfer(formData).unwrap();

      // Invalidate
      dispatch(accountApi.util.invalidateTags(["WalletDashboard"]));

      toast.success("Successfully submitted wire transfer. Awaiting approval.");
      form.reset();
      setOpen(false);
    } catch (error) {
      if (process.env.NODE_ENV === "development") console.error(error);
      toast.error("Failed to wire transfer");
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost-primary" size="sm">
          Wire Transfer
        </Button>
      </DialogTrigger>
      <DialogContent className="w-[95%] sm:max-w-none md:max-w-3xl">
        <DialogTitle className="sr-only">Wire Transfer</DialogTitle>
        <DialogDescription className="sr-only">
          Top up wallet via wire transfer.
        </DialogDescription>
        <DialogHeader>
          <Header icon={LandmarkIcon} as="h3">
            Wire Transfer
          </Header>
        </DialogHeader>
        <Separator />

        <div className="space-y-0.5">
          <h3 className="text-accent font-semibold md:text-lg">
            Top up via wire transfer
          </h3>
          <div className="text-xs md:text-sm text-muted-foreground">
            Send money directly to the account below.
          </div>
        </div>

        {/* Beneficiary */}
        <div className="bg-secondary/20 p-4 rounded-md flex flex-col gap-2.5">
          <div className="flex gap-2 sm:gap-3 items-center">
            <div className="w-12 h-12 sm:w-14 sm:h-14 p-2 shrink-0">
              <Image
                src={droprightLogo}
                alt="Dropright Logo"
                width={40}
                height={40}
              />
            </div>

            <div>
              <div className="text-sm md:text-base font-semibold">
                Dropright, Corp
              </div>
              <div className="text-sm text-muted-foreground">
                943 Flynn Road, Camarillo, CA 93012 US
              </div>
            </div>
          </div>

          <div className="border-t grid grid-cols-2 text-sm text-foreground [&>div:nth-child(odd)]:text-muted-foreground [&>div]:py-2 md:[&>div]:py-2.5 [&>div:not(:nth-last-child(-n+2))]:border-b [&>*:nth-child(even)]:font-medium">
            <div>Account Number:</div>
            <div className="flex justify-between items-center">
              ************
              <div className="relative">
                <CopyTextWrapper text={"************"} isTable={false}>
                  <Button variant="muted" size="xs">
                    <Copy />
                  </Button>
                </CopyTextWrapper>
              </div>
            </div>
            <div>Routing Number:</div>
            <div className="flex justify-between items-center">
              *********
              <div className="relative">
                <CopyTextWrapper text={"*********"} isTable={false}>
                  <Button variant="muted" size="xs">
                    <Copy />
                  </Button>
                </CopyTextWrapper>
              </div>
            </div>
            <div>Swift Code:</div>
            <div className="flex justify-between items-center">
              BOFAUS3N
              <div className="relative">
                <CopyTextWrapper text={"BOFAUS3N"} isTable={false}>
                  <Button variant="muted" size="xs">
                    <Copy />
                  </Button>
                </CopyTextWrapper>
              </div>
            </div>
            <div>Bank Name:</div>
            <div>Bank of America, N.A.</div>
            <div>Bank Address:</div>
            <div>222 Broadway New York, NY 10038</div>
          </div>
        </div>

        <p className="text-xs sm:text-sm text-muted-foreground/50 pb-3">
          <span className="font-medium">Note:</span> Please use your email
          address as the wire transfer reference.
        </p>

        <Separator />
        <div className="space-y-1">
          <h3 className="text-accent font-semibold md:text-lg">
            Verify Your Payment
          </h3>
          <div className="text-xs md:text-sm text-muted-foreground">
            After completing your wire transfer, upload your receipt and fill in
            the details below.
          </div>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)}>
            <div className="bg-muted p-4 grid grid-cols-1 sm:grid-cols-2 gap-4 rounded-md items-start">
              <FormField
                control={form.control}
                name="date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={"outline"}
                            className={cn(
                              "w-full pl-3 text-left font-normal text-muted-foreground !border-input",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "dd/MM/yyyy")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto !p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={date =>
                            date > new Date() || date < new Date("1900-01-01")
                          }
                          captionLayout="dropdown"
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage className="text-right" />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="amount"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>Amount (USD)</FormLabel>
                    <div className="relative">
                      <div className="text-muted-foreground absolute top-1.5 left-3 z-20">
                        $
                      </div>
                      <FormControl>
                        <Input
                          id="rechargeThreshold"
                          type="number"
                          step="1"
                          className={cn(
                            customStyles.inputNoArrows,
                            "text-right bg-white"
                          )}
                          onKeyDown={handleKeyDown}
                          onChange={e => handleChange(e, field.onChange)}
                          value={field.value}
                        />
                      </FormControl>
                    </div>
                    <FormMessage className="text-right" />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="bankReference"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>Reference</FormLabel>
                    <FormControl>
                      <Input {...field} className={cn("bg-white")} />
                    </FormControl>
                    <FormMessage className="text-right" />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="bankName"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>Sender bank name</FormLabel>
                    <FormControl>
                      <Input {...field} className={cn("bg-white")} />
                    </FormControl>
                    <FormMessage className="text-right" />
                  </FormItem>
                )}
              />

              <div className="sm:col-span-2 space-y-2">
                <div className="text-accent text-sm font-medium">
                  Transfer Confirmation
                </div>

                {error && (
                  <Alert variant="destructive">
                    <AlertTriangleIcon />
                    <AlertTitle>{error}</AlertTitle>
                  </Alert>
                )}

                <Controller
                  name="transferConfirmation"
                  control={form.control}
                  render={({ field }) => {
                    return (
                      <FileUpload
                        files={field.value}
                        onChange={field.onChange}
                        accept={VALID_FILE_TYPES as []}
                        maxFiles={MAX_FILE_NUM}
                        onError={setError}
                        className="relative min-h-44 md:min-h-40"
                      />
                    );
                  }}
                />
              </div>
            </div>

            <Separator className="my-4" />
            <DialogFooter>
              <Button
                type="button"
                variant="ghost-destructive"
                onClick={handleClose}
              >
                Cancel
              </Button>

              <Button
                type="submit"
                disabled={!form.formState.isValid || isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="animate-spin" /> Submitting
                  </>
                ) : (
                  "Submit"
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default WireTransferForm;
