"use client";

import Header from "@/components/layout/Header";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  Di<PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { CREDIT_CARD_FEE } from "@/constants/wallet";
import { cn } from "@/lib/utils";
import { accountApi, useGetWalletDashboardQuery } from "@/store/api/accountApi";
import { useGetPaymentMethodsQuery } from "@/store/api/paymentMethodsApi";
import { useCreateTopUpMutation } from "@/store/api/walletApi";
import { useAppDispatch } from "@/store/hooks";
import { ArrowUpRightIcon } from "@heroicons/react/24/outline";
import {
  PaymentMethod,
  PaymentMethodStatus,
  PaymentMethodType,
} from "@repo/database";
import { ChevronLeft } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import PaymentMethodIcon from "../common/PaymentMethodIcon";
import PaymentMethodInfo from "../common/PaymentMethodInfo";
import Link from "next/link";
import {
  updateAccount,
  updateWalletBalance,
} from "@/store/slices/accountSlice";

const TopUpModal = ({ amount }: { amount: number }) => {
  const [open, setOpen] = useState(false);
  const [dialogType, setDialogType] = useState<"topUp" | "paymentMethod">(
    "topUp"
  );
  const [selectedPayment, setSelectedPayment] = useState<PaymentMethod>();

  const { data: payments, isLoading } = useGetPaymentMethodsQuery();
  const { data: walletDashboard } = useGetWalletDashboardQuery();
  const dispatch = useAppDispatch();
  const [createTopUp, { isLoading: creatingTopUp }] = useCreateTopUpMutation();

  const creditCardFee =
    selectedPayment?.type === PaymentMethodType.credit_card
      ? amount * CREDIT_CARD_FEE
      : 0;

  useEffect(() => {
    if (payments && payments.length > 0) {
      setSelectedPayment(payments.find(pm => pm.isDefault));
    }
  }, [payments, open]);

  const handleSubmit = async () => {
    if (!selectedPayment) {
      toast.error("Please select a payment method.");
      return;
    }

    try {
      const payload = {
        paymentType: selectedPayment.type,
        paymentMethodId: selectedPayment.id,
        amount: amount,
        fee: CREDIT_CARD_FEE * 100,
      };

      const response = await createTopUp(payload).unwrap();

      // Invalidate if wallet transaction as been made which returns a Wallet
      dispatch(accountApi.util.invalidateTags(["WalletDashboard"]));
      const wallet = response.wallet;
      if (wallet) dispatch(updateWalletBalance(amount));

      toast.success(response.message ?? "Top-up requested");
      setOpen(false);
    } catch (error) {
      if (process.env.NODE_ENV === "development") console.error(error);
      toast.error("Failed to top up");
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="accent">
          <ArrowUpRightIcon />
          Top Up
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogTitle className="sr-only">Wallet</DialogTitle>
        <DialogDescription className="sr-only">
          Form to add wallet.
        </DialogDescription>
        <DialogHeader>
          <Header icon={ArrowUpRightIcon} as="h3">
            Top Up
          </Header>
        </DialogHeader>
        <Separator />

        <div
          className={cn(
            "relative w-full overflow-x-hidden",
            selectedPayment?.type === PaymentMethodType.credit_card
              ? "min-h-[380px]"
              : selectedPayment?.type === PaymentMethodType.ach
                ? "min-h-[350px]"
                : "min-h-[360px]"
          )}
        >
          {/* TopUp View */}
          <div
            className={`absolute inset-0 transition-all duration-500 transform ${
              dialogType === "topUp"
                ? "translate-x-0 opacity-100"
                : "-translate-x-full opacity-0"
            }`}
          >
            <div className="space-y-4">
              <div>
                <div className="text-sm font-semibold text-foreground">
                  Summary
                </div>
                <div className="grid grid-cols-2 text-sm font-medium mt-3">
                  <div className="text-foreground">Current Balance</div>
                  <div className="text-right text-foreground">
                    ${walletDashboard?.wallet.availableBalance}
                  </div>

                  <div className="text-foreground mt-2">Top Up Amount</div>
                  <div className="text-right text-success mt-2">
                    +${amount.toFixed(2)}
                  </div>
                </div>
              </div>

              <div className="bg-muted rounded-lg grid grid-cols-2 text-sm font-medium p-3">
                {selectedPayment?.type === PaymentMethodType.credit_card && (
                  <>
                    <div>Credit Card Fee (3%)</div>
                    <div className="text-right">
                      $
                      {creditCardFee.toLocaleString(undefined, {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                      })}
                    </div>
                  </>
                )}

                <div
                  className={cn(
                    selectedPayment?.type === PaymentMethodType.credit_card &&
                      "mt-2"
                  )}
                >
                  Total Charge
                </div>
                <div
                  className={cn(
                    "text-right",
                    selectedPayment?.type === PaymentMethodType.credit_card &&
                      "mt-2"
                  )}
                >
                  $
                  {(amount + creditCardFee).toLocaleString(undefined, {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                  })}
                </div>
              </div>
              <div className="bg-secondary/50 rounded-lg flex justify-between items-center font-semibold p-3">
                <div>Updated Balance</div>
                {walletDashboard ? (
                  <div>
                    $
                    {(
                      parseFloat(walletDashboard.wallet.availableBalance) +
                      amount
                    ).toFixed(2)}
                  </div>
                ) : (
                  <Skeleton className="w-16 h-4 rounded-sm bg-secondary" />
                )}
              </div>
              <Separator />
              <div className="flex justify-between">
                {selectedPayment ? (
                  <>
                    <div className="flex items-center space-x-4">
                      <PaymentMethodIcon
                        type={selectedPayment.type}
                        cardBrand={selectedPayment.cardBrand}
                      />
                      <PaymentMethodInfo data={selectedPayment} />
                    </div>
                    <Button
                      variant="link"
                      className="text-primary text-xs font-medium"
                      onClick={() => setDialogType("paymentMethod")}
                    >
                      Change
                    </Button>
                  </>
                ) : (
                  <div className="bg-muted flex justify-between items-center w-full px-3 py-4 rounded-lg">
                    <div className="text-xs text-muted-foreground">
                      No payment method added
                    </div>
                    <Link
                      href="/wallet/payment-methods"
                      className="text-xs text-primary hover:underline font-medium"
                    >
                      Add Payment Method
                    </Link>
                  </div>
                )}
              </div>
              <Separator />
              <DialogFooter className="md:items-center">
                <div className="flex justify-end">
                  <DialogClose asChild>
                    <Button type="button" variant="ghost-destructive">
                      Cancel
                    </Button>
                  </DialogClose>

                  <Button
                    onClick={handleSubmit}
                    type="button"
                    size="lg"
                    disabled={creatingTopUp}
                  >
                    {creatingTopUp ? "Processing..." : "Confirm"}
                  </Button>
                </div>
              </DialogFooter>
            </div>
          </div>

          {/* PaymentMethod View */}
          <div
            className={`absolute inset-0 transition-all duration-500 transform overflow-x-hidden bg-red ${
              dialogType === "paymentMethod"
                ? "translate-x-0 opacity-100"
                : "translate-x-full opacity-0"
            }`}
          >
            <Button variant="link" onClick={() => setDialogType("topUp")}>
              <ChevronLeft className="size-4" />
              Back
            </Button>
            {!payments || isLoading
              ? Array.from({ length: 3 }).map((_, index) => (
                  <div
                    key={index}
                    className="flex justify-between items-center cursor-pointer p-3 rounded"
                  >
                    <div className="space-x-4 flex">
                      <Skeleton className="w-8 h-4 md:w-[72px] md:h-10 rounded-none" />
                      <div className="space-y-1">
                        <Skeleton className="h-4 w-32" />
                        <Skeleton className="h-4 w-14" />
                      </div>
                    </div>
                    <Skeleton className="size-4 rounded-full" />
                  </div>
                ))
              : payments
                  ?.filter(pm => pm.status === PaymentMethodStatus.verified)
                  ?.map(paymentMethod => (
                    <div
                      key={paymentMethod.id}
                      className="flex justify-between items-center cursor-pointer p-3 rounded hover:border-muted-foreground/50 border border-white"
                      onClick={() => {
                        setSelectedPayment(paymentMethod);
                        setDialogType("topUp");
                      }}
                    >
                      <div className="flex items-center space-x-4">
                        <PaymentMethodIcon
                          type={paymentMethod.type}
                          cardBrand={paymentMethod.cardBrand}
                        />
                        <PaymentMethodInfo data={paymentMethod} />
                      </div>

                      <RadioGroup value={selectedPayment?.id}>
                        <RadioGroupItem
                          value={paymentMethod.id}
                          id={`pm-${paymentMethod.id}`}
                          className="ml-4 pointer-events-none"
                        />
                      </RadioGroup>
                    </div>
                  ))}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default TopUpModal;
