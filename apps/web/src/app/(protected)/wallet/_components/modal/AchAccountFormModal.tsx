"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import Header from "@/components/layout/Header";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";
import { achFormSchema, defaultValues } from "@/schemas/wallet/achFormSchema";
import customStyles from "@/styles/Custom.module.css";
import styles from "@/styles/Dashboard.module.css";
import { zodResolver } from "@hookform/resolvers/zod";
import { format } from "date-fns";
import { Eraser, LoaderCircle, Wallet } from "lucide-react";
import Image from "next/image";
import { useRef, useState } from "react";
import {
  FieldError,
  FormProvider,
  useForm,
  useFormContext,
  useWatch,
} from "react-hook-form";
import SignatureCanvas from "react-signature-canvas";
import { z } from "zod";
import PaymentMethodButton from "../../payment-methods/_components/PaymentMethodButton";
import { useCreateAchPaymentMutation } from "@/store/api/paymentMethodsApi";
import { base64ToFile } from "@/lib/utils/fileUtils";
import { toast } from "sonner";
import { ACH_AUTHORIZATION_TEXT } from "@/constants/legal-text";
import { accountApi } from "@/store/api/accountApi";
import { useAppDispatch } from "@/store/hooks";

const AchAccountFormModal = () => {
  const [open, setOpen] = useState(false);
  const form = useForm<z.infer<typeof achFormSchema>>({
    resolver: zodResolver(achFormSchema),
    defaultValues,
  });
  const { isSubmitting } = form.formState;
  const [createAchPayment] = useCreateAchPaymentMutation();
  const dispatch = useAppDispatch();

  const onSubmit = async (values: z.infer<typeof achFormSchema>) => {
    try {
      const formData = new FormData();
      Object.entries(values).forEach(([key, value]) => {
        if (typeof value === "string" && value.startsWith("data:")) {
          const file = base64ToFile(value, key);
          formData.append(key, file);
        } else {
          formData.append(
            key,
            typeof value === "number" ? String(value) : String(value ?? "")
          );
        }
      });

      formData.append("authorizedAt", new Date().toISOString());

      const response = await createAchPayment(formData).unwrap();
      if (response.success) {
        toast.success(response.message);
        dispatch(accountApi.util.invalidateTags(["WalletDashboard"]));
        setOpen(false);
        form.reset();
      }
    } catch (error) {
      if (process.env.NODE_ENV === "development") console.error(error);
      toast.error(
        "Failed to submit your ACH account. Please try again later or contact support"
      );
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <PaymentMethodButton
          textColor="text-secondary-foreground"
          bgColor="bg-secondary hover:bg-secondary/90"
        >
          <Image
            src="/payment/ach.svg"
            alt="ACH"
            width={40}
            height={40}
            className="w-auto h-auto object-contain"
          />
          ACH
        </PaymentMethodButton>
      </DialogTrigger>
      <DialogContent className="w-[95%] sm:max-w-none lg:max-w-3xl xl:max-w-4xl xl:gap-y-5">
        <DialogTitle className="sr-only">Add ACH Payment Method</DialogTitle>
        <DialogDescription className="sr-only">
          Form to add ACH payment method.
        </DialogDescription>
        <DialogHeader>
          <Header icon={Wallet} as="h3">
            ACH Account Form
          </Header>
        </DialogHeader>
        <Separator />
        <FormProvider {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="space-y-4 xl:space-y-5"
          >
            <CustomerInformation />
            <Separator />
            <BankInformation />
            <Separator />
            <ConfirmationSignature />
            <Separator />
            <DialogFooter>
              <Button
                type="button"
                variant="ghost-destructive"
                onClick={() => {
                  form.reset();
                  setOpen(false);
                }}
              >
                Cancel
              </Button>

              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <LoaderCircle className="size-4 animate-spin" /> Submitting
                  </>
                ) : (
                  "Submit"
                )}
              </Button>
            </DialogFooter>
          </form>
        </FormProvider>
      </DialogContent>
    </Dialog>
  );
};

const CustomerInformation = () => {
  const { control } = useFormContext();
  return (
    <div className="space-y-4">
      <h2 className="font-semibold">Customer Information</h2>
      <div className={cn(styles.formGrid)}>
        <FormField
          control={control}
          name="customerName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Name</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={control}
          name="company"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Company</FormLabel>
              <FormControl>
                <Input {...field} placeholder="Optional" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={control}
          name="customerEmail"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={control}
          name="customerPhone"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Phone No.</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={control}
          name="customerAddress"
          render={({ field }) => (
            <FormItem className={styles.colSpan2}>
              <FormLabel>Address</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
};

const BankInformation = () => {
  const { control } = useFormContext();

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "." || e.key === ",") {
      e.preventDefault();
    }
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    onChange: (value: string) => void
  ) => {
    const value = e.target.value;
    if (value.includes(".")) {
      e.target.value = value.split(".")[0];
    }
    // Remove leading zeros but keep single "0"
    const trimmedValue = value.replace(/^0+/, "") || "0";
    onChange(trimmedValue);
  };
  return (
    <div className="space-y-4">
      <h2 className="font-semibold">Bank Information</h2>
      <div className={cn(styles.formGrid)}>
        <FormField
          control={control}
          name="bankName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Bank Name</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="bankAccountName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Name on Bank Account</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="bankAccountNo"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Bank Account No.</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="bankRoutingNo"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Bank Routing No.</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="col-span-1 md:col-span-2">
          <FormField
            control={control}
            name="bankBillingAddress"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Billing Address</FormLabel>
                <FormControl>
                  <Input {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={control}
          name="bankAccountType"
          render={({ field }) => (
            <FormItem className="py-2 space-y-2">
              <FormLabel>Bank Account Type</FormLabel>
              <FormControl className="max-w-sm">
                <RadioGroup
                  onValueChange={(value: string) => field.onChange(value)}
                  defaultValue={String(field.value)}
                  className="flex space-x-4"
                >
                  <FormItem className="flex items-center space-x-1 space-y-0">
                    <FormControl>
                      <RadioGroupItem value="checking" />
                    </FormControl>
                    <FormLabel className="font-normal">Checking</FormLabel>
                  </FormItem>
                  <FormItem className="flex items-center space-x-1 space-y-0">
                    <FormControl>
                      <RadioGroupItem value="saving" />
                    </FormControl>
                    <FormLabel className="font-normal">Saving</FormLabel>
                  </FormItem>
                </RadioGroup>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="bankHolderType"
          render={({ field }) => (
            <FormItem className="py-2 space-y-2">
              <FormLabel>Bank Holder Type</FormLabel>
              <FormControl className="max-w-sm">
                <RadioGroup
                  onValueChange={(value: string) => field.onChange(value)}
                  defaultValue={String(field.value)}
                  className="flex space-x-4"
                >
                  <FormItem className="flex items-center space-x-1 space-y-0">
                    <FormControl>
                      <RadioGroupItem value="personal" />
                    </FormControl>
                    <FormLabel className="font-normal">Personal</FormLabel>
                  </FormItem>
                  <FormItem className="flex items-center space-x-1 space-y-0">
                    <FormControl>
                      <RadioGroupItem value="business" />
                    </FormControl>
                    <FormLabel className="font-normal">Business</FormLabel>
                  </FormItem>
                </RadioGroup>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className={cn(styles.formGrid, "sm:col-span-2 pb-4")}>
          <div className="space-y-1">
            <FormLabel htmlFor="initialFunding">
              Initial Funding Amount
            </FormLabel>
            <FormDescription className="text-xs">
              This is the initial amount loaded into your wallet.
            </FormDescription>
          </div>
          <FormField
            control={control}
            name="initialFunding"
            render={({ field }) => (
              <FormItem className="justify-self-end">
                <div className="relative">
                  <div className="text-muted-foreground absolute top-1.5 left-3">
                    $
                  </div>
                  <FormControl>
                    <Input
                      id="initialFunding"
                      type="number"
                      step="1"
                      className={cn(
                        customStyles.inputNoArrows,
                        "text-right w-58"
                      )}
                      onKeyDown={handleKeyDown}
                      onChange={e => handleChange(e, field.onChange)}
                      value={field.value}
                    />
                  </FormControl>
                </div>
                <FormMessage className="text-right" />
              </FormItem>
            )}
          />
        </div>
      </div>
    </div>
  );
};

// removed unused _FundingRechargingMeter component

const ConfirmationSignature = () => {
  const PAD_WIDTH = 400;

  const {
    setValue,
    formState: { errors },
  } = useFormContext();
  const sigCanvas = useRef<SignatureCanvas | null>(null);
  const name = useWatch({ name: "customerName" });

  const signatureError = errors.signatureFile as FieldError;

  const handleClear = () => {
    sigCanvas.current?.clear();
    setValue("signatureFile", "", { shouldValidate: true });
  };

  const handleEnd = () => {
    const trimmed = sigCanvas.current
      ?.getTrimmedCanvas()
      .toDataURL("image/png");
    if (trimmed) {
      setValue("signatureFile", trimmed, { shouldValidate: true });
    }
  };

  return (
    <div className="space-y-4">
      <h2 className="font-semibold">Online Payment Authorization</h2>
      {ACH_AUTHORIZATION_TEXT.map((item, index) => {
        return (
          <p key={index} className="text-sm text-muted-foreground">
            {item}
          </p>
        );
      })}

      <div
        className="ml-auto flex flex-col items-center gap-2"
        style={{ width: PAD_WIDTH }}
      >
        <div className="w-full flex items-center justify-between gap-1">
          <FormLabel className="text-xs">Signature:</FormLabel>
          {signatureError && (
            <div className="text-destructive text-xs">
              {signatureError.message}
            </div>
          )}
        </div>
        <SignatureCanvas
          ref={sigCanvas}
          penColor="blue"
          canvasProps={{
            width: PAD_WIDTH,
            height: 140,
            className: cn("rounded-md border border-transparent", {
              "border-destructive": !!signatureError,
            }),
          }}
          onEnd={handleEnd}
          backgroundColor="#F6F8FB"
        />
        <Button
          type="button"
          variant="ghost-destructive"
          size="xs"
          className="ml-auto"
          onClick={handleClear}
        >
          <Eraser /> Clear
        </Button>
        <div className="flex flex-col justify-center items-center gap-2">
          <div className="text-sm font-medium">
            {name || <span className="opacity-30">Your name here</span>}
          </div>
          <div className="text-xs">{format(new Date(), "MM/dd/yyyy")}</div>
        </div>
      </div>
    </div>
  );
};

export default AchAccountFormModal;
