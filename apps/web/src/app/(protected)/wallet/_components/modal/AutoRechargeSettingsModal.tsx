"use client";

import Header from "@/components/layout/Header";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";
import { cn } from "@/lib/utils";
import {
  autoRechargeSchema,
  defaultValues,
} from "@/schemas/wallet/autoRechargeSetting";
import { accountApi } from "@/store/api/accountApi";
import {
  useGetRechargePlanQuery,
  useSetRechargePlaneMutation,
} from "@/store/api/walletApi";
import { useAppDispatch } from "@/store/hooks";
import customStyles from "@/styles/Custom.module.css";
import styles from "@/styles/Dashboard.module.css";
import { ArrowUpRightIcon } from "@heroicons/react/24/outline";
import { zodResolver } from "@hookform/resolvers/zod";
import { Loader2, TriangleAlert } from "lucide-react";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";
import TopUpAmountSelector from "../common/TopUpAmountSelector";

const AutoRechargeSettingsModal = () => {
  const [open, setOpen] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [setRechargePlane] = useSetRechargePlaneMutation();
  const { data } = useGetRechargePlanQuery();

  const dispatch = useAppDispatch();

  const form = useForm<z.infer<typeof autoRechargeSchema>>({
    resolver: zodResolver(autoRechargeSchema),
    defaultValues: defaultValues,
  });

  const { isDirty, isSubmitting } = form.formState;

  useEffect(() => {
    if (data) {
      form.reset({
        enabled: data.enabled ?? false,
        amount: Number(data.amount),
        threshold: Number(data.threshold),
        maxPerDay: data.maxPerDay ?? 2,
      });
    }
  }, [data, form]);

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "." || e.key === ",") {
      e.preventDefault();
    }
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    onChange: (value: string) => void
  ) => {
    const value = e.target.value;
    if (value.includes(".")) {
      e.target.value = value.split(".")[0];
    }
    const trimmedValue = value.replace(/^0+/, "") || "0";
    onChange(trimmedValue);
  };

  const handleSubmit = async (values: z.infer<typeof autoRechargeSchema>) => {
    if (!isDirty) {
      setOpen(false);
      return;
    }

    try {
      const response = await setRechargePlane(values).unwrap();
      if (response) {
        toast.success("Set a recharge plan successfully");
        setOpen(false);
        form.reset();
        dispatch(
          accountApi.util.updateQueryData(
            "getWalletDashboard",
            undefined,
            draft => {
              if (draft?.rechargePlan) {
                Object.assign(draft.rechargePlan, {
                  ...values,
                  threshold: String(values.threshold),
                });
              }
            }
          )
        );
      }
    } catch (error) {
      if (process.env.NODE_ENV === "development") console.error(error);
      setError(
        error instanceof Error ? error.message : "Failed to set a recharge plan"
      );
      toast.error("Failed to set a recharge plan");
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost-primary" size="xs">
          Settings
        </Button>
      </DialogTrigger>
      <DialogContent className="md:min-w-xl">
        <DialogTitle className="sr-only">Auto Recharge Settings</DialogTitle>
        <DialogDescription className="sr-only">
          Form to configure auto recharge.
        </DialogDescription>
        <DialogHeader>
          <Header icon={ArrowUpRightIcon} as="h3">
            Auto Recharge Settings
          </Header>
        </DialogHeader>
        <Separator />

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-4 xl:space-y-5"
          >
            <FormField
              control={form.control}
              name="enabled"
              render={({ field }) => (
                <FormItem className="flex justify-between space-x-3 w-full">
                  <FormControl>
                    <Switch
                      checked={field.value as boolean}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1">
                    <FormLabel className="text-foreground font-semibold">
                      Enable Auto Recharge
                    </FormLabel>
                    <FormDescription className="text-foreground text-xs">
                      Occurs when label purchasing causes your wallet to fall
                      below your{" "}
                      <span className="font-semibold">recharge threshold.</span>
                    </FormDescription>
                  </div>
                </FormItem>
              )}
            />

            <div>
              <div className="space-y-1">
                <div className="text-foreground font-semibold text-sm">
                  Recharge Amount
                </div>
                <p className="text-xs text-muted-foreground">
                  The amount that will be loaded into your wallet whenever your
                  wallet falls below the recharge threshold.
                </p>
              </div>

              <FormField
                control={form.control}
                name="amount"
                render={({ field }) => (
                  <div className="py-3">
                    <TopUpAmountSelector
                      value={field.value}
                      onChange={value => field.onChange(value)}
                    />
                    <FormMessage className="text-right" />
                  </div>
                )}
              />

              <div className={cn(styles.formGrid, "mt-3 px-[1px]")}>
                <div className="space-y-1">
                  <FormLabel htmlFor="threshold" className="text-foreground">
                    Recharge Threshold
                  </FormLabel>
                  <FormDescription className="text-xs">
                    If your wallet amount falls below this amount, the recharge
                    amount will automatically be pulled from your account.
                  </FormDescription>
                </div>
                <FormField
                  control={form.control}
                  name="threshold"
                  render={({ field }) => (
                    <FormItem className="justify-self-end w-full max-w-58">
                      <div className="relative">
                        <div className="text-muted-foreground absolute top-1.5 left-3">
                          $
                        </div>
                        <FormControl>
                          <Input
                            id="threshold"
                            type="number"
                            step="1"
                            className={cn(
                              customStyles.inputNoArrows,
                              "text-right"
                            )}
                            onKeyDown={handleKeyDown}
                            onChange={e => handleChange(e, field.onChange)}
                            value={field.value}
                          />
                        </FormControl>
                      </div>
                      <FormMessage className="text-right" />
                    </FormItem>
                  )}
                />

                <div className="space-y-1">
                  <FormLabel htmlFor="maxPerDay" className="text-foreground">
                    Max Recharge Per Day
                  </FormLabel>
                  <FormDescription className="text-xs">
                    The amount of automatic top ups per day.
                  </FormDescription>
                </div>
                <FormField
                  control={form.control}
                  name="maxPerDay"
                  render={({ field }) => (
                    <FormItem className="justify-self-end w-full max-w-58">
                      <FormControl>
                        <Input
                          id="maxPerDay"
                          type="number"
                          step="1"
                          className={cn(
                            customStyles.inputNoArrows,
                            "text-right w-58"
                          )}
                          onKeyDown={handleKeyDown}
                          onChange={e => handleChange(e, field.onChange)}
                          value={field.value}
                        />
                      </FormControl>
                      <FormMessage className="text-right" />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {error && (
              <Alert variant="destructive" className="mt-2 text-sm">
                <TriangleAlert />

                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <Separator />
            <DialogFooter className="md:items-center">
              <DialogClose asChild>
                <Button type="button" variant="ghost-destructive">
                  Cancel
                </Button>
              </DialogClose>

              <Button type="submit" size="lg" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 /> Saving Changes
                  </>
                ) : (
                  "Save Changes"
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default AutoRechargeSettingsModal;
