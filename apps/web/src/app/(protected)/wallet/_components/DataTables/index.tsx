"use client";

import SkeletonTableCell from "@/components/loading/SkeletonTableCell";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useGetWalletDashboardQuery } from "@/store/api/accountApi";
import RecentPayments from "./RecentPayments";
import RecentTransactions from "./RecentTransactions";

const DataTables = () => {
  const { data, isLoading } = useGetWalletDashboardQuery();

  return (
    <div className="w-full max-w-full flex flex-col">
      <Tabs defaultValue="recentTransactions">
        <div className="flex justify-between md:items-center flex-col md:flex-row space-y-4 md:space-y-0">
          <TabsList>
            <TabsTrigger value="recentTransactions" size="sm">
              Recent Transactions
            </TabsTrigger>
            <TabsTrigger value="recentPayment" size="sm">
              Recent Payments
            </TabsTrigger>
          </TabsList>
        </div>
        {!data || isLoading ? (
          <SkeletonTableCell className="min-h-[calc(100dvh-808px)] xl:min-h-[calc(100vh-41rem)]" />
        ) : (
          <>
            <TabsContent value="recentTransactions" className="mt-1">
              <RecentTransactions data={data.wallet.transactions} />
            </TabsContent>
            <TabsContent value="recentPayment" className="mt-1">
              <RecentPayments data={data.payments} />
            </TabsContent>
          </>
        )}
      </Tabs>
    </div>
  );
};

export default DataTables;
