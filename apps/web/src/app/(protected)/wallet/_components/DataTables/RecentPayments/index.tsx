import { createPaymentColumns } from "@/app/(protected)/reports/payments/_components/table/useColumns";
import DataTable from "@/components/common/table/DataTable";
import { WalletRecentPayment } from "@/store/types/walletDashboard";
import { getCoreRowModel, useReactTable } from "@tanstack/react-table";

type RecentPaymentsProps = {
  data: WalletRecentPayment[];
};

const RecentPayments = ({ data }: RecentPaymentsProps) => {
  const columns = [...createPaymentColumns<WalletRecentPayment>()];

  const table = useReactTable({
    data: data,
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  return (
    <DataTable
      table={table}
      height="h-[calc(100dvh-808px)] xl:h-[calc(100vh-41rem)]"
      showPagination={false}
    />
  );
};

export default RecentPayments;
