import { createTransactionColumns } from "@/app/(protected)/wallet/transactions/_components/table/useColumns";
import DataTable from "@/components/common/table/DataTable";
import { WalletTransactionWithPayment } from "@/store/types/walletDashboard";
import { getCoreRowModel, useReactTable } from "@tanstack/react-table";

type RecentTransactionsProps = {
  data: WalletTransactionWithPayment[];
};

const RecentTransactions = ({ data }: RecentTransactionsProps) => {
  const columns = [...createTransactionColumns()];

  const table = useReactTable({
    data: data,
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  return (
    <DataTable
      table={table}
      height="h-[calc(100dvh-808px)] xl:h-[calc(100vh-41rem)]"
      showPagination={false}
    />
  );
};

export default RecentTransactions;
