"use client";

import { DEFAULT_TOP_UP_AMOUNT } from "@/constants/wallet";
import Header from "@/components/layout/Header";
import { useState } from "react";
import TopUpAmountSelector from "../common/TopUpAmountSelector";
import TopUpModal from "../modal/TopUpModal";
import WireTransferForm from "../modal/WireTransferForm";

const TopUp = () => {
  const [topUpAmount, setTopUpAmount] = useState(DEFAULT_TOP_UP_AMOUNT);

  return (
    <div className="px-4 py-5 xl:px-5 xl:py-6 flex flex-col gap-4">
      <Header variant="section" as="h3">
        Top Up
      </Header>
      <TopUpAmountSelector value={topUpAmount} onChange={setTopUpAmount} />
      <div className="flex flex-col gap-2 lg:gap-2.5">
        <TopUpModal amount={topUpAmount} />
        <WireTransferForm />
      </div>
    </div>
  );
};

export default TopUp;
