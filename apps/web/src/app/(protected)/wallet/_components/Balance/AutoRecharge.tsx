"use client";
import Header from "@/components/layout/Header";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import { useGetWalletDashboardQuery } from "@/store/api/accountApi";
import AutoRechargeSettingsModal from "../modal/AutoRechargeSettingsModal";

const AutoRecharge = () => {
  const { data, isLoading } = useGetWalletDashboardQuery();

  return (
    <div className="px-4 py-5 xl:px-5 xl:py-6 flex flex-col gap-4">
      <div className="flex justify-between gap-2">
        <div className="space-y-0.5">
          <Header variant="section" as="h3">
            Auto Recharge
          </Header>
          {isLoading ? (
            <Skeleton className="w-20 h-3.5" />
          ) : (
            <div
              className={cn(
                "text-xs font-medium",
                data?.rechargePlan.enabled ? "text-success" : "text-destructive"
              )}
            >
              {data?.rechargePlan.enabled ? "Enabled" : "Disabled"}
            </div>
          )}
        </div>
        <AutoRechargeSettingsModal />
      </div>
      {/* recharge data */}
      <div className="grid grid-cols-2 space-y-1.5 text-sm">
        <div className="text-foreground font-medium">Recharge Amount</div>
        {isLoading ? (
          <div className="flex justify-end">
            <DataSkeleton />
          </div>
        ) : (
          <div className="text-foreground text-right">
            {data?.rechargePlan?.amount ? `$${data?.rechargePlan.amount}` : "-"}
          </div>
        )}

        <div className="text-foreground font-medium">Recharge Threshold</div>
        {isLoading ? (
          <div className="flex justify-end">
            <DataSkeleton />
          </div>
        ) : (
          <div className="text-foreground text-right">
            {data?.rechargePlan?.threshold
              ? `$${data?.rechargePlan.threshold}`
              : "-"}
          </div>
        )}

        <div className="text-foreground font-medium">Max per Day</div>
        {isLoading ? (
          <div className="flex justify-end">
            <DataSkeleton />
          </div>
        ) : (
          <div className="text-foreground text-right">
            {data?.rechargePlan?.maxPerDay ? data?.rechargePlan.maxPerDay : "-"}
          </div>
        )}
      </div>
    </div>
  );
};

const DataSkeleton = () => {
  return <Skeleton className="h-5 w-28" />;
};

export default AutoRecharge;
