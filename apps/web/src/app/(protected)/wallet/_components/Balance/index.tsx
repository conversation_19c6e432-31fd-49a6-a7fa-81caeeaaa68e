"use client";
import { But<PERSON> } from "@/components/ui/button";
import Header from "@/components/layout/Header";
import Link from "next/link";
import AutoRecharge from "./AutoRecharge";
import TopUp from "./TopUp";
import { useGetWalletDashboardQuery } from "@/store/api/accountApi";
import { Skeleton } from "@/components/ui/skeleton";

const Balance = () => {
  const { data, isLoading } = useGetWalletDashboardQuery();

  return (
    <section className="border rounded-lg overflow-hidden">
      <div className="px-4 py-5 xl:px-5 xl:py-6 flex flex-col gap-2 border-b">
        <Header>Total Balance</Header>
        {isLoading ? (
          <Skeleton className="w-52 md:h-9 h-8" />
        ) : (
          <div className="text-primary text-2xl md:text-3xl 2xl:text-4xl font-semibold">
            ${data?.wallet?.availableBalance}
          </div>
        )}
        <div className="pt-1 flex justify-end items-center gap-2">
          <Button variant="secondary" size="xs" asChild>
            <Link href="/wallet/transactions">View transaction report</Link>
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-1 xl:grid-cols-2 gap-[1px] bg-border [&>div]:bg-background">
        <TopUp />
        <AutoRecharge />
      </div>
    </section>
  );
};

export default Balance;
