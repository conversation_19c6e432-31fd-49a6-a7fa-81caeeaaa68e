import { cn } from "@/lib/utils";
import styles from "@/styles/Dashboard.module.css";
import PageHeader from "../../../../components/layout/PageHeader";
import Table from "./_components/table/Table";
import DataCard from "./_components/table/DataCard";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { ChevronLeft } from "lucide-react";

const TransactionsPage = () => {
  return (
    <main
      className={cn(
        styles.pagePadding,
        "flex flex-col justify-between gap-8 lg:gap-10"
      )}
    >
      <div className="space-y-2">
        <div className="flex items-center gap-1">
          <Link href="/wallet">
            <Button size="icon-sm" variant="ghost">
              <ChevronLeft />
            </Button>
          </Link>
          <PageHeader header="Wallet Transaction Report" />
        </div>
        <DataCard />
        <div className="mt-10">
          <Table />
        </div>
      </div>
    </main>
  );
};

export default TransactionsPage;
