import DateRangeSelect from "@/components/common/table/DateRangeSelect";
import { DateRange } from "react-day-picker";

type ToolbarProps = {
  dateRange: DateRange;
  onDateFilter: (range: DateRange) => void;
};

const Toolbar = ({ dateRange, onDateFilter }: ToolbarProps) => {
  return (
    <div className="flex md:items-center md:justify-between gap-4 md:flex-row flex-col">
      {/* LEFT */}
      <DateRangeSelect initialValue={dateRange} onApply={onDateFilter} />
    </div>
  );
};

export default Toolbar;
