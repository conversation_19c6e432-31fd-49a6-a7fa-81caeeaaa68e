"use client";
import { Skeleton } from "@/components/ui/skeleton";
import { useGetWalletQuery } from "@/store/api/walletApi";

const DataCard = () => {
  const { data: wallet, isLoading } = useGetWalletQuery(
    "include=pending_deposits"
  );

  if (isLoading) {
    return <Skeleton className="w-full lg:w-2/3 h-36 md:h-[84px]" />;
  }

  return (
    <div className="w-full lg:w-2/3 bg-secondary/30 rounded-lg py-5 px-6 grid grid-cols-2 md:grid-cols-3 gap-4">
      <div>
        <div className="text-accent font-semibold text-xs">
          Available Balance
        </div>
        <div className="text-xl font-semibold text-primary">
          ${wallet?.balance}
        </div>
      </div>

      <div>
        <div className="text-accent font-semibold text-xs">Reserved Amount</div>
        <div className="text-xl font-semibold text-accent">
          ${wallet?.reserved}
        </div>
      </div>

      <div>
        <div className="text-accent font-semibold text-xs">
          Pending Deposits
        </div>
        <div className="text-xl font-semibold text-accent">
          ${wallet?.pendingDeposits}
        </div>
      </div>
    </div>
  );
};

export default DataCard;
