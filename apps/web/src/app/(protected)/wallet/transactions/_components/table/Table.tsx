"use client";

import DataTable from "@/components/common/table/DataTable";
import { createPaginationHandler } from "@/lib/utils/table/pagination";
import { useGetWalletTransactionsQuery } from "@/store/api/walletApi";
import { WalletTransactionWithPayment } from "@/store/types/walletDashboard";
import { Pagination } from "@/types";
import { WalletTransaction } from "@/types/Payment/WalletTransaction";
import {
  ColumnDef,
  getCoreRowModel,
  OnChangeFn,
  PaginationState,
  Updater,
  useReactTable,
  VisibilityState,
} from "@tanstack/react-table";
import { startOfMonth } from "date-fns";
import { useCallback, useMemo, useState } from "react";
import { DateRange } from "react-day-picker";
import SkeletonTable from "./SkeletonTable";
import Toolbar from "./Toobar";
import { createTransactionColumns } from "./useColumns";

const PAGE_SIZE_OPTIONS = [25, 50, 75, 100];
const DEFAULT_PAGE_SIZE = PAGE_SIZE_OPTIONS[0];
const DEFAULT_PAGE_INDEX = 0;

const TableWrapper = () => {
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: DEFAULT_PAGE_INDEX,
    pageSize: DEFAULT_PAGE_SIZE,
  });

  const today = new Date();

  const [dateRange, setDateRange] = useState<DateRange>({
    from: startOfMonth(today),
    to: today,
  });

  const queryStr = useMemo(() => {
    const query = new URLSearchParams();
    // Pagination
    query.append("limit", pagination.pageSize.toString());
    query.append(
      "offset",
      (pagination.pageIndex * pagination.pageSize).toString()
    );
    // Filter Date
    if (dateRange.from) query.append("fromDate", dateRange.from.toISOString());
    if (dateRange.to) query.append("toDate", dateRange.to.toISOString());

    return query.toString();
  }, [pagination, dateRange]);

  const columns = [...createTransactionColumns()];

  const { data, isLoading } = useGetWalletTransactionsQuery(queryStr);

  const handleDateFilter = useCallback((range: DateRange) => {
    setDateRange(range);
    setPagination(prev => ({ ...prev, pageIndex: DEFAULT_PAGE_INDEX }));
  }, []);

  return (
    <>
      {!data || !columns || isLoading ? (
        <SkeletonTable />
      ) : (
        <>
          <Table
            data={data}
            columns={columns}
            pagination={pagination}
            setPagination={setPagination}
            dateRange={dateRange}
            onDateFilter={handleDateFilter}
          />
        </>
      )}
    </>
  );
};

type TableProps = {
  data: {
    data: WalletTransaction[];
    pagination: Pagination;
  };
  columns: ColumnDef<WalletTransactionWithPayment>[];
  pagination: PaginationState;
  dateRange: DateRange;
  setPagination: React.Dispatch<React.SetStateAction<PaginationState>>;
  onDateFilter: (range: DateRange) => void;
};

const Table = ({
  data,
  columns,
  pagination,
  dateRange,
  setPagination,
  onDateFilter,
}: TableProps) => {
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});

  const onPaginationChange = useCallback(
    (updater: Updater<PaginationState>) =>
      createPaginationHandler(
        data.pagination,
        pagination,
        setPagination
      )(updater),
    [data.pagination, pagination, setPagination]
  );

  const table = useReactTable({
    data: data.data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    // Pagination
    manualPagination: true,
    rowCount: data.pagination.total,
    onPaginationChange: onPaginationChange as OnChangeFn<PaginationState>,
    // Column Visibility Settings
    onColumnVisibilityChange: setColumnVisibility,
    state: {
      pagination,
      columnVisibility,
    },
  });

  return (
    <div className="flex flex-col gap-4">
      <Toolbar dateRange={dateRange} onDateFilter={onDateFilter} />

      <DataTable
        table={table}
        pageSizeOptions={PAGE_SIZE_OPTIONS}
        height="h-[calc(100dvh-425px)]"
      />
    </div>
  );
};

export default TableWrapper;
