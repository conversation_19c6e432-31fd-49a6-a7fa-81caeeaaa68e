import { TypesLabel, TextAmount } from "@/components/common/TypesLabel";
import { Button } from "@/components/ui/button";
import { snakeCaseToText } from "@/lib/utils/strings";
import { WalletTransactionWithPayment } from "@/store/types/walletDashboard";
import { ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";
import { Printer } from "lucide-react";

export const createTransactionColumns =
  (): ColumnDef<WalletTransactionWithPayment>[] => [
    {
      accessorKey: "createdAt",
      header: "Date / Time",
      cell: ({ row }) => (
        <div>{format(row.getValue("createdAt"), "MM/dd/yyyy - hh:mm")}</div>
      ),
    },
    {
      accessorKey: "type",
      header: "Transaction Type",
      cell: ({ row }) => {
        const { type } = row.original;
        return (
          <div>
            <TypesLabel text={type} />
          </div>
        );
      },
    },
    {
      accessorKey: "balanceChange",
      header() {
        return <div className="text-right">Amount</div>;
      },
      cell: ({ row }) => {
        const { balanceChange, type } = row.original;
        return (
          <div className="text-right">
            {balanceChange && <TextAmount type={type} value={balanceChange} />}
          </div>
        );
      },
    },
    {
      accessorKey: "description",
      header: "Description",
    },
    {
      accessorKey: "referenceId",
      header: "Reference",
    },
    {
      accessorKey: "payment",
      header: "Payment Type",
      cell: ({ row }) => {
        const paymentType = (row.original as WalletTransactionWithPayment)
          .payment?.type;
        return <div>{paymentType ? snakeCaseToText(paymentType) : "-"}</div>;
      },
    },
    {
      accessorKey: "newBalance",
      header() {
        return <div className="text-right">Ending Balanced</div>;
      },
      cell: ({ row }) => {
        const { newBalance } = row.original;
        return <div className="text-right font-medium">${newBalance}</div>;
      },
    },
  ];

export const createWalletActions =
  (): ColumnDef<WalletTransactionWithPayment>[] => [
    {
      id: "actions",
      header: "Actions",
      cell: () => {
        return (
          <Button variant="secondary" size="xs">
            <Printer />
            <span>Print Receipt</span>
          </Button>
        );
      },
    },
  ];
