import { cn } from "@/lib/utils";
import styles from "@/styles/Dashboard.module.css";
import { CreditCard } from "lucide-react";
import PageHeader from "../../../../components/layout/PageHeader";
import AddNewPaymentMethod from "./_components/AddNewPaymentMethod";
import SavedPaymentMethods from "./_components/SavedPaymentMethods";

const PaymentMethodsPage = async () => {
  return (
    <main
      className={cn(
        styles.pagePadding,
        styles.panelYSpacing,
        "max-w-5xl mx-auto"
      )}
    >
      <div className="space-y-2">
        <PageHeader header="Payment Methods" icon={CreditCard} />
        <div className="text-sm">
          Choose your preferred payment method for automatic wallet recharges
          and manual top-ups. You can set a default payment option and manage
          multiple cards or bank accounts for seamless transactions.
        </div>
      </div>

      <AddNewPaymentMethod />

      <SavedPaymentMethods />
    </main>
  );
};

export default PaymentMethodsPage;
