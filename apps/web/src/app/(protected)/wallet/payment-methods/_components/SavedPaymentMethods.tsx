"use client";
import { But<PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { useConfirm } from "@/hooks/useConfirm";
import { accountApi } from "@/store/api/accountApi";
import {
  useDeletePaymentMutation,
  useGetPaymentMethodsQuery,
  useSetDefaultMutation,
} from "@/store/api/paymentMethodsApi";
import { useAppDispatch } from "@/store/hooks";
import { PaymentMethod, PaymentMethodStatus } from "@repo/database";
import { Loader2 } from "lucide-react";
import { useCallback } from "react";
import { toast } from "sonner";
import PaymentMethodIcon from "../../_components/common/PaymentMethodIcon";
import PaymentMethodInfo from "../../_components/common/PaymentMethodInfo";

const SavedPaymentMethods = () => {
  const { data: payments, isLoading } = useGetPaymentMethodsQuery();

  return (
    <section>
      <h2 className="font-semibold">Saved payment methods</h2>
      <div className="space-y-6 mt-3">
        {isLoading &&
          Array.from({ length: 4 }).map((_, index) => (
            <Skeleton key={index} className="w-full h-[114px]" />
          ))}
        {payments?.map(payment => (
          <PaymentMethodItem key={payment.id} payment={payment} />
        ))}
      </div>
    </section>
  );
};

const PaymentMethodItem = ({ payment }: { payment: PaymentMethod }) => {
  const [deletePayment, { isLoading: deleting }] = useDeletePaymentMutation();
  const [setDefault, { isLoading: settingAsDefault }] = useSetDefaultMutation();

  const { confirm, ConfirmModal } = useConfirm();
  const dispatch = useAppDispatch();

  const handleDelete = useCallback(
    async (id: string) => {
      try {
        const confirmed = await confirm({
          title: "Remove",
          description: "Are you sure you want to remove payment method?",
          confirmText: "Confirm",
          variant: "destructive",
        });

        if (!confirmed) return;

        await deletePayment(id).unwrap();
        toast.success("Remove payment method successfully");
      } catch (error) {
        if (process.env.NODE_ENV === "development")
          console.error("Failed to remove payment method:", error);
        toast.error("Failed to remove payment method");
      }
    },
    [deletePayment, confirm]
  );

  const handleSetDefault = useCallback(
    async (id: string) => {
      try {
        const response = await setDefault(id).unwrap();
        if (response) {
          dispatch(accountApi.util.invalidateTags(["WalletDashboard"]));
        }
      } catch (error) {
        if (process.env.NODE_ENV === "development")
          console.error("Failed to set default of payment method:", error);
      }
    },
    [dispatch, setDefault]
  );

  return (
    <>
      <div className="border p-5 rounded-lg flex justify-between">
        <div>
          <div className="flex items-center space-x-4">
            <PaymentMethodIcon
              type={payment.type}
              cardBrand={payment.cardBrand}
            />
            <PaymentMethodInfo data={payment} />
          </div>
          <div className="flex space-x-2 text-xs mt-2">
            <Button
              size="xs"
              variant="link-accent"
              className="text-destructive"
              onClick={() => handleDelete(payment.id)}
            >
              {deleting ? (
                <Loader2 className="size-5 animate-spin" />
              ) : (
                <span>Remove</span>
              )}
            </Button>
          </div>
        </div>

        {payment.isDefault ? (
          <Button variant="accent" size="xs">
            Default
          </Button>
        ) : (
          <Button
            variant="link"
            size="xs"
            disabled={payment.status !== PaymentMethodStatus.verified}
            onClick={() => handleSetDefault(payment.id)}
          >
            {settingAsDefault ? (
              <Loader2 className="size-5 animate-spin" />
            ) : (
              <span>Set as default</span>
            )}
          </Button>
        )}
      </div>
      <ConfirmModal />
    </>
  );
};

export default SavedPaymentMethods;
