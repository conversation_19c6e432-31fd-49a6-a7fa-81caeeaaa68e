import { cn } from "@/lib/utils";
import styles from "@/styles/Dashboard.module.css";
import AchAccountFormModal from "../../../_components/modal/AchAccountFormModal";
import AddCreditCard from "./AddCreditCard";
// import PaymentMethodButton from "../PaymentMethodButton";
// import Image from "next/image";

const AddNewPaymentMethod = () => {
  return (
    <section className="overflow-visible">
      <h2 className="font-semibold">Add new payment method</h2>

      <div
        className={cn(
          "flex items-center overflow-x-auto md:overflow-x-visible pr-5 py-4",
          styles.gridGap
        )}
      >
        <AddCreditCard />

        <AchAccountFormModal />

        {/* <PaymentMethodButton
          textColor="text-secondary-foreground"
          bgColor="bg-muted hover:bg-muted/90"
        >
          <Image
            src="/payment/paypal.svg"
            alt="Paypal"
            width={40}
            height={40}
            className="w-auto h-auto object-contain"
          />
          Paypal
        </PaymentMethodButton> */}
      </div>
    </section>
  );
};

export default AddNewPaymentMethod;
