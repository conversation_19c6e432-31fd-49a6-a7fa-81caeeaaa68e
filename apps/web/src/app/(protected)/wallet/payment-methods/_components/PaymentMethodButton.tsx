import { cn } from "@/lib/utils";

type PaymentMethodButtonProps = {
  textColor: string;
  bgColor: string;
  onClick?: () => void;
  children: React.ReactNode;
};

const PaymentMethodButton = ({
  textColor,
  bgColor,
  children,
  ...props
}: PaymentMethodButtonProps) => {
  return (
    <button
      className={cn(
        "cursor-pointer rounded-md h-20 w-48 lg:h-24 lg:w-60 shrink-0 flex items-center justify-center gap-1.5 lg:gap-2 xl:gap-2.5 hover:scale-[102%] hover:-translate-y-0.5 hover:shadow-md transition-all duration-300 ease-in-out font-semibold lg:text-lg xl:text-xl",
        textColor,
        bgColor
      )}
      {...props}
    >
      {children}
    </button>
  );
};

export default PaymentMethodButton;
