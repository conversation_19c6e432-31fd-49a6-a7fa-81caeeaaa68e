"use client";

import { getJwtToken } from "@/actions/auth/getToken";
import Header from "@/components/layout/Header";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Elements,
  PaymentElement,
  useElements,
  useStripe,
} from "@stripe/react-stripe-js";
import { loadStripe } from "@stripe/stripe-js";
import { AlertCircleIcon, CreditCard, Loader2 } from "lucide-react";
import Image from "next/image";
import { useState } from "react";
import { toast } from "sonner";
import PaymentMethodButton from "../PaymentMethodButton";

const stripePromise = loadStripe(
  process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!
);

const appearance = {
  variables: {
    colorPrimary: "#1457ff",
    colorText: "#232360",
    colorTextSecondary: "#5f6388",
    colorDanger: "#d32f2f",
    fontFamily: "Poppins, system-ui, sans-serif",
    borderRadius: "10px",
  },
};

const AddCreditCard = () => {
  const [open, setOpen] = useState(false);
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleOpenChange = async (isOpen: boolean) => {
    setOpen(isOpen);

    if (isOpen) {
      await fetchSetupIntent();
    } else {
      setError(null);
      setClientSecret(null);
    }
  };

  const fetchSetupIntent = async () => {
    setLoading(true);
    setError(null);

    try {
      const token = await getJwtToken();
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/payment-methods/setup-intent`,
        {
          method: "GET",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (!response.ok) throw new Error("Failed to get setup intent");

      const data = await response.json();
      setClientSecret(data.client_secret);
    } catch (error) {
      if (process.env.NODE_ENV === "development") console.error(error);
      setError(
        "Please check your internet connection and try again, or contact support if the issue persists."
      );
    } finally {
      setLoading(false);
    }
  };

  const handleSuccess = () => {
    setClientSecret(null);
    setOpen(false);
    toast.success("Payment method added successfully!");
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        <PaymentMethodButton
          textColor="text-accent-foreground"
          bgColor="bg-accent hover:bg-accent/90"
        >
          <Image
            src="/payment/credit-card.svg"
            alt="Credit Card"
            width={40}
            height={40}
            className="w-auto h-auto object-contain"
          />
          Credit Card
        </PaymentMethodButton>
      </DialogTrigger>

      <DialogContent className="w-[95%] sm:max-w-none md:max-w-xl">
        <DialogTitle className="sr-only">
          Add Credit Card Payment Method
        </DialogTitle>
        <DialogDescription className="sr-only">
          Form to add credit card payment method.
        </DialogDescription>
        <DialogHeader>
          <Header icon={CreditCard} as="h3">
            Add New Credit Card
          </Header>
        </DialogHeader>

        {loading && (
          <div className="min-h-[340px] grid place-content-center">
            <Loader2 className="size-6 text-primary animate-spin" />
          </div>
        )}

        {error && (
          <div className="min-h-[340px] flex flex-col justify-between space-y-4 xl:space-y-5">
            <Alert variant="destructive">
              <AlertCircleIcon />
              <AlertTitle>
                We couldn&apos;t load the payment form right now
              </AlertTitle>
              <AlertDescription>
                <p>{error}</p>
              </AlertDescription>
            </Alert>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setOpen(false)}
              >
                Close
              </Button>
            </DialogFooter>
          </div>
        )}

        {clientSecret && !loading && (
          <Elements
            stripe={stripePromise}
            options={{ clientSecret, appearance }}
          >
            <SetupForm onSuccess={handleSuccess} />
          </Elements>
        )}
      </DialogContent>
    </Dialog>
  );
};

const SetupForm = ({ onSuccess }: { onSuccess: () => void }) => {
  const stripe = useStripe();
  const elements = useElements();

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<null | string>(null);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!stripe || !elements) return;

    setIsSubmitting(true);
    setError(null);

    try {
      const { error } = await stripe.confirmSetup({
        elements,
        confirmParams: {
          return_url: `${process.env.NEXT_PUBLIC_URL}/wallet/payment-methods/success`,
        },
      });

      if (error) {
        setError(
          error.message || "An error occurred while adding your payment method"
        );
      } else onSuccess();
    } catch {
      setError("An unexpected error occurred");
    } finally {
      setIsSubmitting(false);
    }
  };
  return (
    <form onSubmit={handleSubmit} className="space-y-4 xl:space-y-5">
      <PaymentElement />

      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <DialogFooter>
        <DialogClose asChild>
          <Button variant="ghost">Cancel</Button>
        </DialogClose>
        <Button type="submit" disabled={!stripe || isSubmitting}>
          {isSubmitting ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
              Adding Card...
            </>
          ) : (
            "Add Credit Card"
          )}
        </Button>
      </DialogFooter>
    </form>
  );
};

export default AddCreditCard;
