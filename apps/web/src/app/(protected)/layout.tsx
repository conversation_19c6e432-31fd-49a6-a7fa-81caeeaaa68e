import { auth } from "@/auth";
import { SidebarProvider } from "@/components/ui/sidebar";
import StoreProvider from "@/providers/StoreProvider";
import { SessionProvider } from "next-auth/react";
import { redirect } from "next/navigation";
import DashboardNavbar from "../../components/layout/dashboard/DashboardNavbar";
import DashboardSidebar from "../../components/layout/dashboard/DashboardSidebar";
import AccountProvider from "../../providers/AccountProvider";

const DashboardLayout = async ({ children }: { children: React.ReactNode }) => {
  const session = await auth();
  if (!session) redirect("/");

  return (
    <StoreProvider>
      <SessionProvider session={session}>
        <AccountProvider userId={session.user.id}>
          <SidebarProvider defaultOpen={false}>
            <DashboardSidebar />
            <main className="w-full">
              <DashboardNavbar />
              {children}
            </main>
          </SidebarProvider>
        </AccountProvider>
      </SessionProvider>
    </StoreProvider>
  );
};

export default DashboardLayout;
