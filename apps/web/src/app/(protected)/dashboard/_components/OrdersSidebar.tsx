"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { useIsMobile } from "@/hooks/useIsMobile";
import { cn } from "@/lib/utils";
import { ClipboardDocumentListIcon } from "@heroicons/react/24/outline";
import { CircleCheck, ReceiptText } from "lucide-react";
import Link from "next/link";
import {
  createContext,
  useCallback,
  useContext,
  useMemo,
  useState,
} from "react";

const SIDEBAR_WIDTH = "18rem";

type OrdersSidebarContextProps = {
  state: "expanded" | "collapsed";
  open: boolean;
  isMobile: boolean;
  toggleSidebar: () => void;
};

const OrdersSidebarContext = createContext<OrdersSidebarContextProps | null>(
  null
);

const useOrdersSidebar = () => {
  const context = useContext(OrdersSidebarContext);
  if (!context) {
    throw new Error(
      "useOrdersSidebar must be used within a OrdersSidebarProvider"
    );
  }
  return context;
};

const OrdersSidebarProvider = ({
  open: openProp,
  defaultOpen = false,
  children,
  className,
  ...props
}: React.ComponentProps<"div"> & { open?: boolean; defaultOpen?: boolean }) => {
  const isMobile = useIsMobile();
  const [_open, _setOpen] = useState(defaultOpen); // Used internally

  const open = openProp ?? _open;
  const state = open ? "expanded" : "collapsed";

  const toggleSidebar = useCallback(() => {
    return _setOpen(open => !open);
  }, [_setOpen]);

  const contextValue = useMemo<OrdersSidebarContextProps>(
    () => ({ state, open, isMobile, toggleSidebar }),
    [state, open, isMobile, toggleSidebar]
  );

  return (
    <OrdersSidebarContext.Provider value={contextValue} {...props}>
      <div
        style={
          {
            "--sidebar-width": SIDEBAR_WIDTH,
          } as React.CSSProperties
        }
        className={cn("flex min-h-[calc(100vh-176px)] w-full", className)}
        {...props}
      >
        {children}
      </div>
    </OrdersSidebarContext.Provider>
  );
};

const OrdersSidebar = ({ className }: React.ComponentProps<"div">) => {
  const { isMobile, state } = useOrdersSidebar();

  if (isMobile) {
    return null;
  }

  return (
    <div
      className={cn("group peer text-gray-700 hidden md:block", className)}
      data-state={state}
      data-slot="sidebar"
    >
      {/* This is what handles the sidebar gap on desktop */}
      <div
        data-slot="sidebar-gap"
        className={cn(
          "relative w-[var(--sidebar-width)] bg-transparent",
          "group-data-[state=collapsed]:w-0"
        )}
      />

      <div
        data-slot="sidebar-container"
        className={cn(
          "fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) md:flex",
          "right-0 group-data-[state=collapsed]:-right-(--sidebar-width)"
        )}
      >
        <div
          data-sidebar="sidebar"
          data-slot="sidebar-inner"
          className="bg-sidebar flex h-full w-full flex-col pt-20"
        >
          <OrdersSidebarContent />
        </div>
      </div>
    </div>
  );
};

const OrdersSidebarTrigger = ({
  className,
  onClick,
  ...props
}: React.ComponentProps<typeof Button>) => {
  const { toggleSidebar } = useOrdersSidebar();

  return (
    <Button
      type="button"
      variant="accent"
      size="icon"
      className={cn(
        // "peer-data-[state=collapsed]:hidden",
        // "peer-data-[state=expanded]:right-[calc(var(--sidebar-width)+0.5rem)]",
        "pl-2.5 rounded-r-none hover:w-11 justify-start [&_svg]:size-5!",
        className
      )}
      onClick={e => {
        onClick?.(e);
        toggleSidebar();
      }}
      aria-label="Toggle orders sidebar"
      {...props}
    >
      <ClipboardDocumentListIcon />
    </Button>
  );
};

const OrdersSidebarContent = () => {
  const { toggleSidebar } = useOrdersSidebar();

  return (
    <div className="h-full pl-4 pr-1 pt-10 pb-5 flex flex-col gap-4">
      <div className="pr-3">
        <div className="flex items-center justify-between">
          <div className="font-semibold text-sm">Latest Orders</div>
          <Button
            variant="muted"
            size="xs"
            className="bg-background"
            onClick={toggleSidebar}
          >
            Hide
          </Button>
        </div>
      </div>
      <div className="flex-1 overflow-auto pr-3 overscroll-contain">
        <OrderItem />
        <OrderItem />
        <OrderItem />
        <OrderItem />
        <OrderItem status="processed" />
        <OrderItem status="processed" />
        <OrderItem status="processed" />
        <OrderItem status="processed" />
        <OrderItem status="processed" />
        <OrderItem status="processed" />
        <OrderItem status="processed" />
        <OrderItem status="processed" />
        <OrderItem status="processed" />
        <OrderItem status="processed" />
        <OrderItem status="processed" />
      </div>
      <div className="pr-3">
        <Button variant="accent" className="w-full" asChild>
          <Link href="/orders">All Orders</Link>
        </Button>
      </div>
    </div>
  );
};

const OrderItem = ({
  status = "unprocessed",
}: {
  status?: "unprocessed" | "processed";
}) => {
  return (
    <div className="flex justify-between items-center">
      <div>
        <div className="text-muted-foreground text-xs">1 hr</div>
        <p className="font-semibold text-sm">Chris Morich</p>
        <div className="text-muted-foreground text-xs">Human Garage</div>
      </div>
      {status === "unprocessed" ? (
        <Button variant="secondary" size="xs">
          <ReceiptText strokeWidth={1.75} /> Create Label
        </Button>
      ) : (
        <Button variant="muted" size="xs" className="bg-background" disabled>
          <CircleCheck strokeWidth={1.75} /> Processed
        </Button>
      )}
    </div>
  );
};

export {
  OrdersSidebar,
  OrdersSidebarProvider,
  OrdersSidebarTrigger,
  useOrdersSidebar,
};
