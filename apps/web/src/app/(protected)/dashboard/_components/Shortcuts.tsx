import { Button } from "@/components/ui/button";
import { ClipboardDocumentIcon, TruckIcon } from "@heroicons/react/24/outline";
import Link from "next/link";

const shortcuts = [
  {
    label: "Create Order",
    icon: ClipboardDocumentIcon,
    href: "/orders/create",
  },
  {
    label: "Create Batch Order",
    icon: ClipboardDocumentIcon,
    href: "/",
  },
  { label: "Shipments", icon: TruckIcon, href: "/shipments" },
];

const Shortcuts = () => {
  return (
    <div className="space-y-3">
      <h2 className="font-semibold">Shortcuts</h2>
      <div className="flex gap-3 items-center flex-wrap">
        {shortcuts.map(shortcut => (
          <Button size="lg" key={shortcut.label} asChild>
            <Link href={shortcut.href}>
              <shortcut.icon />
              {shortcut.label}
            </Link>
          </Button>
        ))}
      </div>
    </div>
  );
};

export default Shortcuts;
