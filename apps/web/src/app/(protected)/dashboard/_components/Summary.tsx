import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { Calendar } from "lucide-react";
import Card from "./Card";
import styles from "@/styles/Dashboard.module.css";

const Summary = () => {
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="font-semibold">Summary</h2>
        <Button size="xs" variant="muted">
          <Calendar strokeWidth={1.5} className="size-3" />
          Last 30 days
        </Button>
      </div>

      <div className={cn("grid lg:grid-cols-2", styles.gridGap)}>
        <Card className="bg-accent text-accent-foreground flex flex-col space-y-1">
          <h3 className="font-semibold xl:text-lg">Orders Shipped</h3>
          <p className="text-3xl font-semibold">150</p>
        </Card>
        <Card className="bg-secondary text-secondary-foreground flex flex-col space-y-1">
          <h3 className="font-semibold xl:text-lg">Postage Paid</h3>
          <p className="text-3xl font-semibold">$200.00</p>
        </Card>
      </div>
    </div>
  );
};

export default Summary;
