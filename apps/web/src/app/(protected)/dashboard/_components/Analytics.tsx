import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Calendar } from "lucide-react";

const Analytics = () => {
  return (
    <div>
      <Tabs defaultValue="activity">
        <div className="flex justify-between items-center">
          <TabsList>
            <TabsTrigger value="activity">Activity</TabsTrigger>
            <TabsTrigger value="reports">Reports</TabsTrigger>
          </TabsList>
          <Button size="xs" variant="muted">
            <Calendar strokeWidth={1.5} className="size-3" />
            Last 30 days
          </Button>
        </div>
        <TabsContent value="activity">Chart</TabsContent>
        <TabsContent value="reports">Something</TabsContent>
      </Tabs>
    </div>
  );
};

export default Analytics;
