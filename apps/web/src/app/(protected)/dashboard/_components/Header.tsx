"use client";

import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import styles from "@/styles/Dashboard.module.css";
import { PlusCircleIcon } from "@heroicons/react/24/outline";
import { ExclamationCircleIcon } from "@heroicons/react/24/solid";
import Card from "./Card";

const Header = ({ name }: { name: string }) => {
  return (
    <div className={cn(styles.gridGap, "grid lg:grid-cols-2 gap-6")}>
      <div className="space-y-2">
        <h1 className="text-lg font-semibold text-foreground">
          Good morning {name}
        </h1>
        <div className="flex gap-1.5">
          <ExclamationCircleIcon className="shrink-0 size-4 text-destructive mt-[1px]" />
          <p className="text-sm text-muted-foreground">
            Your wallet is currently below your wallet threshold. Please top-up!
          </p>
        </div>
      </div>
      <WalletBalance />
    </div>
  );
};

const WalletBalance = () => {
  return (
    <Card className="bg-muted flex justify-between items-end">
      <div className="space-y-1">
        <div className="text-sm text-muted-foreground font-medium">Wallet</div>
        <div className="text-2xl font-semibold">$150.00</div>
      </div>
      <Button
        size="sm"
        variant="muted"
        className="bg-background hover:bg-background/80"
      >
        <PlusCircleIcon />
        Top-up
      </Button>
    </Card>
  );
};

export default Header;
