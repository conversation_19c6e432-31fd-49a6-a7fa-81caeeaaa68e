import { cn } from "@/lib/utils";
import Analytics from "./_components/Analytics";
import Header from "./_components/Header";
import {
  OrdersSidebar,
  OrdersSidebarProvider,
  OrdersSidebarTrigger,
} from "./_components/OrdersSidebar";
import Shortcuts from "./_components/Shortcuts";
import Summary from "./_components/Summary";
import styles from "@/styles/Dashboard.module.css";
import { getCurrentUser } from "@/lib/auth";

export default async function DashboardPage() {
  const session = await getCurrentUser();

  return (
    <div className={cn(styles.pagePadding, "relative")}>
      <OrdersSidebarProvider defaultOpen={true}>
        <div className="w-full space-y-8">
          <Header name={session?.name ?? ""} />
          <Shortcuts />
          <Summary />
          <Analytics />
        </div>
        <OrdersSidebar />
        <OrdersSidebarTrigger className="fixed top-28 right-0" />
      </OrdersSidebarProvider>
    </div>
  );
}
