@import "tailwindcss";
@plugin "@tailwindcss/typography";
@plugin "tailwindcss-animate";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-poppins);
  /* --font-mono: var(--font-geist-mono); */
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-success: var(--success);
  --color-warning: var(--warning);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;

  @keyframes accordion-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }
  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0;
    }
  }
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(29.69% 0.1056 277.82); /* set */
  --card: oklch(1 0 0);
  --card-foreground: oklch(29.69% 0.1056 277.82); /* set */
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(29.69% 0.1056 277.82); /* set */
  --primary: oklch(53.87% 0.2558 263.51); /* set */
  --primary-foreground: oklch(94.49% 0.0252 255.56); /* set */
  --secondary: oklch(91.39% 0.0582 222.68); /* set */
  --secondary-foreground: oklch(29.69% 0.1056 277.82); /* set */
  --muted: oklch(97.85% 0.0045 258.32); /* set */
  --muted-foreground: oklch(51.11% 0.0588 279.32); /* set */
  --accent: oklch(33.49% 0.0586 268.7); /* set */
  --accent-foreground: oklch(94.96% 0.0096 273.35); /* set */
  --destructive: oklch(56.8% 0.2002 26.41); /* set */
  --success: oklch(0.696 0.17 162.48); /* set */
  --warning: oklch(76.9% 0.188 70.08); /* set */
  --border: oklch(87.92% 0 0); /* set */
  --input: oklch(87.92% 0 0); /* set */
  --ring: oklch(51.11% 0.0588 279.32); /* set */
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(97.85% 0.0045 258.32);
  --sidebar-foreground: oklch(51.11% 0.0588 279.32); /* set */
  --sidebar-primary: oklch(0.21 0.034 264.665);
  --sidebar-primary-foreground: oklch(0.985 0.002 247.839);
  --sidebar-accent: oklch(0.967 0.003 264.542);
  --sidebar-accent-foreground: oklch(0.21 0.034 264.665);
  --sidebar-border: oklch(0.928 0.006 264.531);
  --sidebar-ring: oklch(0.707 0.022 261.325);
}

.dark {
  --background: oklch(20.37% 0.011 260.67); /* set */
  --foreground: oklch(0.985 0.002 247.839); /* set */
  --card: oklch(0.21 0.034 264.665);
  --card-foreground: oklch(0.985 0.002 247.839); /* set */
  --popover: oklch(25.61% 0.0139 267.02);
  --popover-foreground: oklch(0.985 0.002 247.839); /* set */
  --primary: oklch(91.39% 0.0582 222.68); /* set */
  --primary-foreground: oklch(29.69% 0.1056 277.82); /* set */
  --secondary: oklch(33.49% 0.0586 268.7); /* set */
  --secondary-foreground: oklch(94.96% 0.0096 273.35); /* set */
  --muted: oklch(25.61% 0.0139 267.02); /* set */
  --muted-foreground: oklch(83.6% 0.0207 257.48); /* set */
  --accent: oklch(58.81% 0.2241 264.25); /* set */
  --accent-foreground: oklch(94.49% 0.0252 255.56); /* set */
  --destructive: oklch(67.08% 0.2165 25.19); /* set */
  --success: oklch(0.765 0.177 163.223); /* set */
  --warning: oklch(82.8% 0.189 84.429); /* set */
  --border: oklch(51.62% 0.0121 261.77); /* set */
  --input: oklch(83.6% 0.0207 257.48); /* set */
  --ring: oklch(83.6% 0.0207 257.48); /* set */
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(25.61% 0.0139 267.02);
  --sidebar-foreground: oklch(83.6% 0.0207 257.48); /* set */
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0.002 247.839);
  --sidebar-accent: oklch(0.278 0.033 256.848);
  --sidebar-accent-foreground: oklch(0.985 0.002 247.839);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.551 0.027 264.364);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* .panelContainer {
  @apply px-4 py-5 border rounded-lg;
} */
