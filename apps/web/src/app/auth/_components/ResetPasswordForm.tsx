"use client";
import { resetPassword } from "@/actions/auth/reset-password";
import { verificationToken } from "@/actions/auth/verification-token";
import LoadingIcon from "@/components/icons/LoadingIcon";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  checkPasswordsMatch,
  checkPasswordStrength,
} from "@/lib/utils/password";
import ResetPasswordSchema from "@/schemas/auth/ResetPasswordSchema";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  AlertCircle,
  Check,
  Eye,
  EyeOff,
  Loader2,
  Mail,
  MoveLeft,
  TriangleAlert,
  X,
} from "lucide-react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

const ResetPasswordForm = () => {
  const [error, setError] = useState<string | undefined>("");
  const [isExpired, setIsExpired] = useState<boolean | null>(null);
  const [showPasswords, setShowPasswords] = useState({
    password: false,
    confirmPassword: false,
  });

  const { push } = useRouter();
  const searchParams = useSearchParams();
  const token = searchParams.get("token");

  const form = useForm<z.infer<typeof ResetPasswordSchema>>({
    resolver: zodResolver(ResetPasswordSchema),
    defaultValues: {
      password: "",
      confirmPassword: "",
    },
  });
  const { isSubmitting, isSubmitted } = form.formState;

  useEffect(() => {
    const verifyToken = async () => {
      if (!token) return;
      try {
        const data = await verificationToken(token);
        if (!data.success) setIsExpired(!data.success);
        else setIsExpired(false);
      } catch (error) {
        setIsExpired(true);
        console.log(error);
      }
    };
    verifyToken();
  }, [token]);

  const onSubmit = async (value: z.infer<typeof ResetPasswordSchema>) => {
    setError("");

    if (!token) return;

    try {
      const data = await resetPassword(value, token);

      if (!data.success) {
        setError("Could not reset password. Please try again later.");
        return;
      }

      toast.success("Password reset successfully! Please login");
      push("/auth/login");
    } catch {
      setError("Something went wrong. Please try again later.");
    }
  };

  if (isExpired === null) {
    return <LoadingIcon />;
  }

  return (
    <div className="space-y-10">
      {isExpired ? (
        <>
          <div className="flex justify-center mb-9">
            <div className="bg-destructive/20 p-3 w-fit h-fit rounded-full">
              <TriangleAlert className="text-destructive" />
            </div>
          </div>
          <div className="space-y-2">
            <h1 className="text-xl font-semibold">
              Reset link expired or invalid
            </h1>
            <p className="text-sm">
              Your password reset link is no longer valid. It may have expired
              or already been used.
            </p>
          </div>

          <Button
            type="button"
            className="w-full"
            size="lg"
            onClick={() => push("/auth/forgot-password")}
          >
            <Mail />
            Request new link
          </Button>
        </>
      ) : (
        <>
          <div className="space-y-2">
            <h1 className="text-2xl font-semibold">Set new password</h1>
            <p className="text-sm">
              At least 8 chars with 1 uppercase, 1 lowercase and 1 number
            </p>
          </div>

          {error && error !== "" && (
            <Alert variant="destructive" className="md:col-span-2">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(onSubmit)}
              className="space-y-6 mb-7"
            >
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem className="md:col-span-2">
                    <FormLabel className="flex items-center gap-1.5">
                      Password
                    </FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          // variant="ghost"
                          type={showPasswords.password ? "text" : "password"}
                          className="pr-10"
                          {...field}
                        />
                        {field.value && !checkPasswordStrength(field.value) && (
                          <X
                            size={20}
                            className="absolute right-11 top-1/2 -translate-y-1/2 text-destructive/50"
                          />
                        )}
                        {checkPasswordStrength(field.value) && (
                          <Check
                            size={20}
                            className="absolute right-11 top-1/2 -translate-y-1/2 text-emerald-500"
                          />
                        )}
                        <Button
                          type="button"
                          size="icon"
                          variant="link"
                          className="absolute right-2 top-1/2 -translate-y-1/2"
                          onClick={() =>
                            setShowPasswords(prev => ({
                              ...prev,
                              password: !prev.password,
                            }))
                          }
                        >
                          {showPasswords.password ? <Eye /> : <EyeOff />}
                        </Button>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="confirmPassword"
                render={({ field }) => (
                  <FormItem className="md:col-span-2">
                    <FormLabel>Confirm Password</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          // variant="ghost"
                          type={
                            showPasswords.confirmPassword ? "text" : "password"
                          }
                          className="pr-10"
                          {...field}
                        />
                        {checkPasswordStrength(field.value) &&
                          checkPasswordsMatch(
                            field.value,
                            form.getValues("password")
                          ) && (
                            <Check
                              size={20}
                              className="absolute right-11 top-1/2 -translate-y-1/2 text-emerald-500"
                            />
                          )}
                        <Button
                          type="button"
                          size="icon"
                          variant="link"
                          className="absolute right-2 top-1/2 -translate-y-1/2"
                          onClick={() =>
                            setShowPasswords(prev => ({
                              ...prev,
                              confirmPassword: !prev.confirmPassword,
                            }))
                          }
                        >
                          {showPasswords.confirmPassword ? <Eye /> : <EyeOff />}
                        </Button>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <Button
                type="submit"
                className="w-full"
                size="lg"
                disabled={isSubmitting || isSubmitted}
              >
                {isSubmitting && <Loader2 className="animate-spin" />}
                Reset password
              </Button>
            </form>
            <Link
              href={"/auth/login"}
              className="flex space-x-3 justify-center items-center font-medium text-primary hover:underline"
            >
              <MoveLeft className="size-5" />
              <span className="text-sm">Back to log in</span>
            </Link>
          </Form>
        </>
      )}
    </div>
  );
};

export default ResetPasswordForm;
