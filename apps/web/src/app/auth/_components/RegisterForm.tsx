"use client";

import { register } from "@/actions/auth/register";
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { SHIPMENT_VOLUME_OPTIONS } from "@/constants/registration";
import {
  checkPasswordsMatch,
  checkPasswordStrength,
} from "@/lib/utils/password";
import droprightLogo from "@/public/logos/primary-light.svg";
import RegisterSchema from "@/schemas/auth/RegisterSchema";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  AlertCircle,
  Check,
  CheckCircle,
  CircleHelp,
  Eye,
  EyeOff,
  LogIn,
  X,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useState, useTransition } from "react";
import { useForm } from "react-hook-form";
import * as z from "zod";
import RegisteredCard from "./RegisteredCard";

const RegisterForm = () => {
  const [showPasswords, setShowPasswords] = useState({
    password: false,
    confirmPassword: false,
  });
  const [success, setSuccess] = useState<string | undefined>("");
  const [error, setError] = useState<string | undefined>("");
  const [isRegistered, setIsRegistered] = useState(false);
  const [isPending, startTransition] = useTransition();

  const form = useForm<z.infer<typeof RegisterSchema>>({
    resolver: zodResolver(RegisterSchema),
    defaultValues: {
      companyName: "",
      firstName: "",
      lastName: "",
      phone: "",
      shipmentVolume: "",
      email: "",
      password: "",
      confirmPassword: "",
      terms: false,
    },
  });

  const onSubmit = (values: z.infer<typeof RegisterSchema>) => {
    setSuccess("");
    setError("");
    startTransition(() => {
      register(values)
        .then(data => {
          setError(data.error || "");
          setSuccess(data.success || "");
          setIsRegistered(true);
        })
        .catch(() => {
          setError("Something went wrong!");
        });
    });
  };

  useEffect(() => {
    checkPasswordStrength(form.getValues("password"));
  }, [form]);

  if (isRegistered) return <RegisteredCard email={form.getValues("email")} />;
  else
    return (
      <Card className="w-full max-w-xl">
        <Image
          src={droprightLogo}
          alt="Dropright logo"
          className="w-56 mx-auto mt-8"
        />
        <CardHeader className="text-center">
          <CardTitle className="text-2xl">Lets start shipping!</CardTitle>
          <CardDescription className="mt-1 max-w-md mx-auto">
            Create your account to start shipping with ease. Set up in minutes
            and ship today.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(onSubmit)}
              className="w-full grid gap-6 md:grid-cols-2 md:gap-x-4"
            >
              <FormField
                control={form.control}
                name="companyName"
                render={({ field }) => (
                  <FormItem className="md:col-span-2">
                    <FormLabel>Company Name</FormLabel>
                    <FormControl>
                      <Input variant="ghost" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="firstName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>First Name</FormLabel>
                    <FormControl>
                      <Input variant="ghost" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="lastName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Last Name</FormLabel>
                    <FormControl>
                      <Input variant="ghost" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone Number</FormLabel>
                    <FormControl>
                      <Input variant="ghost" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="shipmentVolume"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Shipment Volume</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger
                          variant="ghost"
                          className="w-full max-w-full overflow-x-hidden text-ellipsis"
                        >
                          <SelectValue placeholder="Select volume" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectGroup>
                          {SHIPMENT_VOLUME_OPTIONS.map(option => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem className="md:col-span-2">
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input variant="ghost" type="email" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              {/* PASSWORD */}
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem className="md:col-span-2">
                    <FormLabel className="flex items-center gap-1.5">
                      Password
                      <HoverCard>
                        <HoverCardTrigger>
                          <CircleHelp
                            size={14}
                            className="text-muted-foreground -translate-y-[1px]"
                          />
                        </HoverCardTrigger>
                        <HoverCardContent className="text-sm">
                          At least 8 characters long and contain at least one
                          uppercase letter, one lowercase letter, and one
                          number.
                        </HoverCardContent>
                      </HoverCard>
                    </FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          variant="ghost"
                          type={showPasswords.password ? "text" : "password"}
                          className="pr-10"
                          {...field}
                        />
                        {field.value && !checkPasswordStrength(field.value) && (
                          <X
                            size={20}
                            className="absolute right-11 top-1/2 -translate-y-1/2 text-destructive/50"
                          />
                        )}
                        {checkPasswordStrength(field.value) && (
                          <Check
                            size={20}
                            className="absolute right-11 top-1/2 -translate-y-1/2 text-emerald-500"
                          />
                        )}
                        <Button
                          type="button"
                          size="icon"
                          variant="link"
                          className="absolute right-2 top-1/2 -translate-y-1/2"
                          onClick={() =>
                            setShowPasswords(prev => ({
                              ...prev,
                              password: !prev.password,
                            }))
                          }
                        >
                          {showPasswords.password ? <Eye /> : <EyeOff />}
                        </Button>
                      </div>
                    </FormControl>
                    <FormDescription className="mt-1 text-xs">
                      At least 8 chars with 1 uppercase, 1 lowercase and 1
                      number
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="confirmPassword"
                render={({ field }) => (
                  <FormItem className="md:col-span-2">
                    <FormLabel>Confirm Password</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          variant="ghost"
                          type={
                            showPasswords.confirmPassword ? "text" : "password"
                          }
                          className="pr-10"
                          {...field}
                        />
                        {checkPasswordStrength(field.value) &&
                          checkPasswordsMatch(
                            field.value,
                            form.getValues("password")
                          ) && (
                            <Check
                              size={20}
                              className="absolute right-11 top-1/2 -translate-y-1/2 text-emerald-500"
                            />
                          )}
                        <Button
                          type="button"
                          size="icon"
                          variant="link"
                          className="absolute right-2 top-1/2 -translate-y-1/2"
                          onClick={() =>
                            setShowPasswords(prev => ({
                              ...prev,
                              confirmPassword: !prev.confirmPassword,
                            }))
                          }
                        >
                          {showPasswords.confirmPassword ? <Eye /> : <EyeOff />}
                        </Button>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="terms"
                render={({ field }) => (
                  <FormItem className="md:col-span-2 p-2">
                    <div className="flex flex-row items-start space-x-3">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>

                      <FormLabel className="font-normal text-muted-foreground">
                        I agree to the
                        <Link
                          href="/terms"
                          className="text-primary font-medium hover:underline"
                        >
                          Terms of Service
                        </Link>
                        and
                        <Link
                          href="/privacy"
                          className="text-primary font-medium hover:underline"
                        >
                          Privacy Policy
                        </Link>
                      </FormLabel>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
              {error && error !== "" && (
                <Alert variant="destructive" className="md:col-span-2">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Error</AlertTitle>
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}
              {success && success !== "" && (
                <Alert variant="success" className="md:col-span-2">
                  <CheckCircle className="h-4 w-4" />
                  <AlertTitle>Success</AlertTitle>
                  <AlertDescription>{success}</AlertDescription>
                </Alert>
              )}
              <Button
                type="submit"
                size="lg"
                className="w-full md:col-span-2"
                disabled={isPending}
              >
                <LogIn />
                {isPending ? "Creating account..." : "Create account"}
              </Button>
            </form>
          </Form>
        </CardContent>
        <CardFooter className="pt-4">
          <CardDescription className="text-sm text-center  w-full">
            Already have an account?{" "}
            <Link
              href="/auth/login"
              className="text-primary font-medium hover:underline"
            >
              Sign in
            </Link>
          </CardDescription>
        </CardFooter>
      </Card>
    );
};

export default RegisterForm;
