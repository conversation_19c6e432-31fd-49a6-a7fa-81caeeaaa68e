"use client";

import { acceptInvite } from "@/actions/auth/accept-invite";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import { Input } from "@/components/ui/input";
import {
  checkPasswordsMatch,
  checkPasswordStrength,
} from "@/lib/utils/password";
import droprightLogo from "@/public/logos/primary-light.svg";
import InviteUserSchema from "@/schemas/user/InviteUserSchema";
import { zodResolver } from "@hookform/resolvers/zod";
import { UserInviteStatus } from "@repo/database";
import {
  AlertCircle,
  Check,
  CheckCircle,
  CircleHelp,
  Eye,
  EyeOff,
  LogIn,
  X,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { notFound, useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import * as z from "zod";

const InviteForm = () => {
  const [showPasswords, setShowPasswords] = useState({
    password: false,
    confirmPassword: false,
  });
  const [invalidToken, setInvalidToken] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { push } = useRouter();

  const searchParams = useSearchParams();
  const token = searchParams.get("token");

  const form = useForm<z.infer<typeof InviteUserSchema>>({
    resolver: zodResolver(InviteUserSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      password: "",
      confirmPassword: "",
      terms: false,
    },
  });
  const { isSubmitSuccessful } = form.formState;

  const onSubmit = async (values: z.infer<typeof InviteUserSchema>) => {
    const payload = {
      firstName: values.firstName,
      lastName: values.lastName,
      password: values.password,
      token: token ?? "",
    };

    setError(null);
    setIsLoading(true);

    try {
      const { success, data, error } = await acceptInvite(payload);
      if (success) {
        if (data?.email) push(`/auth/login?email=${data.email}`);
        else push("/auth/login");
      } else setError(error ?? "Failed to accept user invite");
    } catch (error) {
      if (process.env.NODE_ENV === "development") console.error(error);
      // Ignore redirect errors as they're handled by NextAuth
      if (error instanceof Error && error.message.includes("NEXT_REDIRECT")) {
        return;
      }
      setError("An unexpected error occurred");
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (!token) return;

    const verifyUserInvite = async () => {
      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL}/auth/user-invite/${token}`
        );
        if (!response.ok) throw new Error("No user invite found");

        const data = await response.json();

        // Check if user invite is expired or not pending
        const isExpired = new Date(data.expires) < new Date();
        if (isExpired || data.status !== UserInviteStatus.pending)
          throw new Error("Token has expired or no longer valid");
      } catch (error) {
        if (process.env.NODE_ENV === "development") console.log(error);
        setInvalidToken(true);
      }
    };

    verifyUserInvite();
  }, [token]);

  // if token is not provided, return 404
  if (invalidToken) return notFound();

  return (
    <Card className="w-full max-w-md">
      <Image
        src={droprightLogo}
        alt="Dropright logo"
        className="w-56 mx-auto mt-8"
      />
      <CardHeader className="text-center">
        <CardTitle className="text-2xl">Welcome!</CardTitle>
        <CardDescription className="mt-1 max-w-md mx-auto">
          We&apos;re excited to have you join our community. Please tell us a
          bit about yourself to get started.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="space-y-4 md:space-y-6">
              <FormField
                control={form.control}
                name="firstName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>First Name</FormLabel>
                    <FormControl>
                      <Input variant="ghost" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="lastName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Last Name</FormLabel>
                    <FormControl>
                      <Input variant="ghost" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem className="md:col-span-2">
                    <FormLabel className="flex items-center gap-1.5">
                      Password
                      <HoverCard>
                        <HoverCardTrigger>
                          <CircleHelp
                            size={14}
                            className="text-muted-foreground -translate-y-[1px]"
                          />
                        </HoverCardTrigger>
                        <HoverCardContent className="text-sm">
                          At least 8 characters long and contain at least one
                          uppercase letter, one lowercase letter, and one
                          number.
                        </HoverCardContent>
                      </HoverCard>
                    </FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          variant="ghost"
                          type={showPasswords.password ? "text" : "password"}
                          className="pr-10"
                          {...field}
                        />
                        {field.value && !checkPasswordStrength(field.value) && (
                          <X
                            size={20}
                            className="absolute right-11 top-1/2 -translate-y-1/2 text-destructive/50"
                          />
                        )}
                        {checkPasswordStrength(field.value) && (
                          <Check
                            size={20}
                            className="absolute right-11 top-1/2 -translate-y-1/2 text-emerald-500"
                          />
                        )}
                        <Button
                          type="button"
                          size="icon"
                          variant="link"
                          className="absolute right-2 top-1/2 -translate-y-1/2"
                          onClick={() =>
                            setShowPasswords(prev => ({
                              ...prev,
                              password: !prev.password,
                            }))
                          }
                        >
                          {showPasswords.password ? <Eye /> : <EyeOff />}
                        </Button>
                      </div>
                    </FormControl>
                    <FormDescription className="mt-1 text-xs">
                      At least 8 chars with 1 uppercase, 1 lowercase and 1
                      number
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="confirmPassword"
                render={({ field }) => (
                  <FormItem className="md:col-span-2">
                    <FormLabel>Confirm Password</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          variant="ghost"
                          type={
                            showPasswords.confirmPassword ? "text" : "password"
                          }
                          className="pr-10"
                          {...field}
                        />
                        {checkPasswordStrength(field.value) &&
                          checkPasswordsMatch(
                            field.value,
                            form.getValues("password")
                          ) && (
                            <Check
                              size={20}
                              className="absolute right-11 top-1/2 -translate-y-1/2 text-emerald-500"
                            />
                          )}
                        <Button
                          type="button"
                          size="icon"
                          variant="link"
                          className="absolute right-2 top-1/2 -translate-y-1/2"
                          onClick={() =>
                            setShowPasswords(prev => ({
                              ...prev,
                              confirmPassword: !prev.confirmPassword,
                            }))
                          }
                        >
                          {showPasswords.confirmPassword ? <Eye /> : <EyeOff />}
                        </Button>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="terms"
                render={({ field }) => (
                  <FormItem className="md:col-span-2 p-2">
                    <div className="flex flex-row items-start space-x-2">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>

                      <FormLabel className="font-normal text-muted-foreground">
                        I agree to the
                        <Link
                          href="/terms"
                          className="text-primary font-medium hover:underline"
                        >
                          Terms of Service
                        </Link>
                        and
                        <Link
                          href="/privacy"
                          className="text-primary font-medium hover:underline"
                        >
                          Privacy Policy
                        </Link>
                      </FormLabel>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
              {error && (
                <Alert variant="destructive" className="md:col-span-2">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Error</AlertTitle>
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}
              {isSubmitSuccessful && (
                <Alert variant="success" className="md:col-span-2">
                  <CheckCircle className="h-4 w-4" />
                  <AlertTitle>Success</AlertTitle>
                  <AlertDescription>
                    Account created successfully!
                  </AlertDescription>
                </Alert>
              )}
              <Button
                type="submit"
                size="lg"
                className="w-full md:col-span-2"
                disabled={isLoading}
              >
                <LogIn />
                {isLoading ? "Creating account..." : "Register"}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

export default InviteForm;
