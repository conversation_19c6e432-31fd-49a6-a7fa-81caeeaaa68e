"use client";

import { <PERSON><PERSON>, <PERSON><PERSON>Des<PERSON>, AlertTitle } from "@/components/ui/alert";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import MailImage from "@/public/graphics/mail.svg";
import { AlertCircle, Loader2, Mail, MoveLeft } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";

const ForgotPasswordForm = () => {
  const [email, setEmail] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSent, setIsSent] = useState<boolean>(false);

  const handleResend = async () => {
    setIsSent(false);
    setEmail("");
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setError(null);
    setIsLoading(true);

    try {
      const url = `${process.env.NEXT_PUBLIC_API_URL}/auth/forgot-password`;
      const response = await fetch(url, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email }),
      });

      if (!response.ok)
        throw new Error(
          "Could not send reset password email. Please try again later"
        );

      setIsSent(true);
    } catch (error) {
      if (error instanceof Error) setError(error.message);
      else setError("Something unexpected happened. Please try again later");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-10">
      {isSent ? (
        <>
          <Image src={MailImage} alt="Mail image" className="w-16 mb-9" />
          <div className="space-y-2">
            <h1 className="text-2xl font-semibold">Email sent</h1>
            <p className="text-sm">
              We&apos;ve sent a password reset link to{" "}
              <span className="font-semibold">{email}</span>. Click the link in
              the email to reset your password.
            </p>
          </div>

          <div className="mt-9">
            <p className="text-sm">
              Don&apos;t get the email? Check your spam folder, or{" "}
              <span
                onClick={handleResend}
                className="text-primary font-medium hover:underline cursor-pointer"
              >
                click to resend
              </span>
            </p>
          </div>

          <Link
            href={"/auth/login"}
            className="flex space-x-3 justify-center items-center font-medium text-primary hover:underline"
          >
            <MoveLeft className="size-5" />
            <span className="text-sm">Back to log in</span>
          </Link>
        </>
      ) : (
        <>
          <div className="space-y-2">
            <h1 className="text-2xl font-semibold">Forgot your password?</h1>
            <p className="text-sm">
              No worries. Enter your email below and we&apos;ll send you a link
              to reset your password.
            </p>
          </div>

          {error && (
            <Alert variant="destructive" className="md:col-span-2">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <form onSubmit={handleSubmit} className="space-y-6 mb-7">
            <div className="grid w-full max-w-sm items-center gap-3">
              <Label htmlFor="email">Email Address</Label>
              <Input
                id="email"
                value={email}
                onChange={e => setEmail(e.target.value)}
              />
            </div>

            <Button
              type="submit"
              className="w-full"
              size="lg"
              disabled={isLoading}
            >
              {isLoading ? <Loader2 className="animate-spin" /> : <Mail />}
              Send reset link
            </Button>
          </form>
          <Link
            href={"/auth/login"}
            className="flex space-x-3 justify-center items-center font-medium text-primary hover:underline"
          >
            <MoveLeft className="size-5" />
            <span className="text-sm">Back to log in</span>
          </Link>
        </>
      )}
    </div>
  );
};

export default ForgotPasswordForm;
