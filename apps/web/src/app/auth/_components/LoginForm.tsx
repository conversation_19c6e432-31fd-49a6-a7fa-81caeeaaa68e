"use client";

import { login } from "@/actions/auth/login";
import { <PERSON>ert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import droprightLogo from "@/public/logos/primary-light.svg";
import LoginSchema from "@/schemas/auth/LoginSchema";
import { zodResolver } from "@hookform/resolvers/zod";
import { AlertCircle, CheckCircle, Eye, EyeOff, LogIn } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { useEffect, useState, useTransition } from "react";
import { useForm } from "react-hook-form";
import * as z from "zod";

const LoginForm = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [success, setSuccess] = useState<string | undefined>("");
  const [error, setError] = useState<string | undefined>("");
  const [isPending, startTransition] = useTransition();

  const searchParams = useSearchParams();
  const email = searchParams.get("email");

  const form = useForm<z.infer<typeof LoginSchema>>({
    resolver: zodResolver(LoginSchema),
    defaultValues: {
      email: email || "",
      password: "",
    },
  });

  useEffect(() => {
    if (email) form.setValue("email", email);
  }, [email, form]);

  const onSubmit = (values: z.infer<typeof LoginSchema>) => {
    setSuccess("");
    setError("");
    startTransition(() => {
      login(values)
        .then(data => {
          if (data?.error) {
            setError(data.error);
          }
          if (data?.success) {
            setSuccess(data.success);
          }
        })
        .catch(error => {
          // Ignore redirect errors as they're handled by NextAuth
          if (
            error instanceof Error &&
            error.message.includes("NEXT_REDIRECT")
          ) {
            return;
          }
          setError("Invalid email or password.");
        });
    });
  };

  return (
    <div className="space-y-10">
      <Image src={droprightLogo} alt="Dropright logo" className="w-56" />
      <div className="space-y-2">
        <h1 className="text-2xl font-semibold">Login to Your Account</h1>
        <p className="text-sm">
          Welcome back! Log back in to continue your shipping spree.
        </p>
      </div>
      {error && error !== "" && (
        <Alert variant="destructive" className="md:col-span-2">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      {success && success !== "" && (
        <Alert variant="success" className="md:col-span-2">
          <CheckCircle className="h-4 w-4" />
          <AlertTitle>Success</AlertTitle>
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input placeholder="Email" variant="ghost" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem className="pb-4">
                <FormLabel>Password</FormLabel>
                <FormControl>
                  <div className="relative">
                    <Input
                      placeholder="Password"
                      variant="ghost"
                      type={showPassword ? "text" : "password"}
                      className="pr-10"
                      {...field}
                    />
                    <Button
                      type="button"
                      size="icon"
                      variant="link"
                      className="absolute right-2 top-1/2 -translate-y-1/2"
                      onClick={() => setShowPassword(() => !showPassword)}
                    >
                      {showPassword ? <Eye /> : <EyeOff />}
                    </Button>
                  </div>
                </FormControl>
                <FormMessage />
                <p className="mt-4 text-xs text-right">
                  Forgot your password?{" "}
                  <Link
                    href="/auth/forgot-password"
                    className="text-primary font-medium hover:underline"
                  >
                    Click here
                  </Link>
                </p>
              </FormItem>
            )}
          />

          <Button
            type="submit"
            className="w-full"
            size="lg"
            disabled={isPending}
          >
            <LogIn />
            Login
          </Button>
        </form>

        <div className="absolute bottom-14 left-0 right-0">
          <p className="mt-4 text-xs text-center">
            Don&apos;t have an account yet?{" "}
            <Link
              href="/auth/register"
              className="text-primary font-medium hover:underline"
            >
              Get started!
            </Link>
          </p>
        </div>
      </Form>
    </div>
  );
};

export default LoginForm;
