"use client";

import { newVerification } from "@/actions/auth/new-verification";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { CheckCircleIcon, XCircleIcon } from "@heroicons/react/24/solid";
import { LoaderCircle } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

type VerificationStatus = {
  isSuccess: boolean;
  isError: boolean;
  message?: string;
};

const NewVerificationForm = () => {
  const [status, setStatus] = useState<VerificationStatus>({
    isSuccess: false,
    isError: false,
  });

  const { push } = useRouter();
  const searchParams = useSearchParams();
  const token = searchParams.get("token");

  useEffect(() => {
    if (!token || status.isSuccess || status.isError) return;

    const verify = async () => {
      try {
        const data = await newVerification(token);
        setStatus({
          isSuccess: !!data.success,
          isError: !!data.error,
          message: data.error || data.success,
        });

        if (data.success && data.email) push(`/auth/login?email=${data.email}`);
      } catch (error) {
        console.error(error);
        setStatus({
          isSuccess: false,
          isError: true,
          message: "Something went wrong",
        });
      }
    };

    verify();
  }, [token, status.isSuccess, status.isError, push]);

  return (
    <main className="h-svh w-full bg-secondary flex justify-center items-center">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle>Verifying Your Email</CardTitle>
          <CardDescription>
            Please wait while we validate your email
          </CardDescription>
        </CardHeader>
        <CardContent>
          {status.isSuccess ? (
            <div className="min-h-[100px] flex justify-center items-center">
              <CheckCircleIcon className="size-12 text-success" />
            </div>
          ) : status.isError ? (
            <div className="min-h-[100px] flex flex-col gap-2 justify-center items-center">
              <XCircleIcon className="size-12 text-destructive" />
              <p className="text-muted-foreground text-sm">{status.message}</p>
            </div>
          ) : (
            <div className="min-h-[100px] flex justify-center items-center">
              <LoaderCircle className="animate-spin size-12 text-primary" />
            </div>
          )}
        </CardContent>
      </Card>
    </main>
  );
};

export default NewVerificationForm;
