import resendVerificationToken from "@/actions/auth/resendVerificationToken";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import emailGraphic from "@/public/graphics/mail.png";
import Image from "next/image";

const RegisteredCard = ({ email }: { email: string }) => {
  const handleResendVerificationEmail = async () => {
    const response = await resendVerificationToken(email);
    console.log("response: ", response);
  };
  return (
    <Card className="w-full max-w-md py-8">
      <CardHeader>
        <CardTitle className="text-lg text-center">
          Registration Successful!
        </CardTitle>
      </CardHeader>
      <Image src={emailGraphic} alt="email" className="mx-auto w-20" />
      <CardContent className="space-y-2">
        <CardDescription>
          We&apos;ve sent a verification link to your email address. Please
          check your inbox.
        </CardDescription>
        <Button
          variant="link"
          size="sm"
          className="px-0"
          onClick={handleResendVerificationEmail}
        >
          Resend verification email
        </Button>
      </CardContent>
    </Card>
  );
};

export default RegisteredCard;
