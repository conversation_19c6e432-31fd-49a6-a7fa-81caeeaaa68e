import { <PERSON><PERSON> } from "@/components/ui/button";
import { Metadata } from "next";
import Link from "next/link";

export const dynamic = "force-static";

export const metadata: Metadata = {
  title: "Dropright",
  description: "Your application description here",
};

export default function Home() {
  return (
    <div className="h-dvh grid place-content-center">
      <div className="flex gap-2">
        <Button asChild>
          <Link href="/auth/login">Login</Link>
        </Button>
        <Button variant="secondary" asChild>
          <Link href="/auth/register">Register</Link>
        </Button>
      </div>
    </div>
  );
}
