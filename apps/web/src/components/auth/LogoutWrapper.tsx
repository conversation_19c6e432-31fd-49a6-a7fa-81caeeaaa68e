"use client";

import { logout } from "@/actions/auth/logout";
import { useAppDispatch } from "@/store/hooks";
import { clearAccount } from "@/store/slices/accountSlice";

interface LogoutWrapperProps {
  children?: React.ReactNode;
}

const LogoutWrapper = ({ children }: LogoutWrapperProps) => {
  const dispatch = useAppDispatch();

  const onClick = () => {
    logout();
    dispatch(clearAccount());
  };

  return (
    <span onClick={onClick} className="cursor-pointer">
      {children}
    </span>
  );
};

export default LogoutWrapper;
