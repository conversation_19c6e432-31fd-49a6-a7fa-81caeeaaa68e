import { Button } from "@/components/ui/button";

type PopoverFooterProps = {
  submitLabel: string;
  onSubmitClick?: () => void;
  destructiveLabel?: string;
  onDestructiveClick?: () => void;
};

const PopoverFooter = ({
  submitLabel,
  onSubmitClick,
  destructiveLabel,
  onDestructiveClick,
}: PopoverFooterProps) => {
  return (
    <div className="pt-4 flex justify-end items-center gap-2">
      {destructiveLabel && (
        <Button
          type="button"
          variant="ghost-destructive"
          onClick={onDestructiveClick}
        >
          {destructiveLabel}
        </Button>
      )}

      <Button type="submit" onClick={onSubmitClick}>
        {submitLabel}
      </Button>
    </div>
  );
};

export default PopoverFooter;
