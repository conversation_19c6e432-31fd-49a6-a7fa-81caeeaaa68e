import { cn } from "@/lib/utils";
import { cva, type VariantProps } from "class-variance-authority";
import { LucideIcon } from "lucide-react";
import { ComponentType } from "react";

type HeaderProps = {
  as?: "h1" | "h2" | "h3" | "h4" | "h5" | "h6";
  icon?: LucideIcon | ComponentType;
  variant?: "page" | "panel" | "section";
  className?: string;

  children: React.ReactNode;
};

const headerVariants = cva(
  "font-semibold text-foreground flex items-center gap-2 [&>svg]:size-5",
  {
    variants: {
      variant: {
        page: "text-lg lg:text-xl",
        panel: "text-base lg:text-lg",
        section: "text-sm lg:text-base [&>svg]:size-4",
      },
    },
    defaultVariants: {
      variant: "panel",
    },
  }
);

const Header = ({
  as = "h2",
  variant,
  icon,
  className = "",
  children,
}: HeaderProps & VariantProps<typeof headerVariants>) => {
  const Component = as;
  const Icon = icon ?? null;

  return (
    <Component className={cn(headerVariants({ variant }), className)}>
      {Icon && <Icon className="text-primary" />}
      {children}
    </Component>
  );
};

export default Header;
