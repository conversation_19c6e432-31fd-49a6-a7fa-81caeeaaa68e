import Header from "@/components/layout/Header";
import { LucideIcon } from "lucide-react";
import React, { ComponentType } from "react";

type PageHeaderProps = {
  header: string;
  icon?: LucideIcon | ComponentType;
  description?: React.ReactNode;
};

const PageHeader = ({ header, icon, description }: PageHeaderProps) => {
  return (
    <div className="space-y-2 flex flex-col text-sm text-muted-foreground max-w-prose">
      <Header as="h1" variant="page" icon={icon}>
        {header}
      </Header>
      {description}
    </div>
  );
};

export default PageHeader;
