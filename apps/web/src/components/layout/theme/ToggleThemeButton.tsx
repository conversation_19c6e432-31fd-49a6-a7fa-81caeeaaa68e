"use client";

import { But<PERSON> } from "@/components/ui/button";
import { MoonIcon, SunIcon } from "@heroicons/react/24/outline";
import { useTheme } from "next-themes";
import { useEffect, useState } from "react";

const ToggleThemeButton = () => {
  const { theme, setTheme } = useTheme();
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  const toggleTheme = () => {
    setTheme(theme === "dark" ? "light" : "dark");
  };

  if (!isMounted)
    return <div className="size-10 rounded-full bg-muted animate-pulse" />;

  return (
    <Button
      size="icon-lg"
      variant="ghost"
      aria-label={`Switch to ${theme === "light" ? "dark" : "light"} theme`}
      onClick={toggleTheme}
    >
      {theme === "light" ? (
        <MoonIcon className="size-auto" />
      ) : (
        <SunIcon className="size-auto" />
      )}
    </Button>
  );
};

export default ToggleThemeButton;
