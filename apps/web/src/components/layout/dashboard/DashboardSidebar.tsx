"use client";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { Separator } from "@/components/ui/separator";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarKeepExpandedTrigger,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import {
  additionalMenus,
  adminMenus,
  dashboardMenus,
  MenuItem,
} from "@/config/navMenus";
import { cn } from "@/lib/utils";
import droprightLogo from "@/public/logos/primary-light.svg";
import { ChevronDown } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";
import DashboardSidebarWrapper from "./DashboardSidebarWrapper";

const renderSidebarGroup = (
  menus: MenuItem[],
  groupLabel: string,
  pathname: string
) => {
  const isMenuActive = (menu: MenuItem, pathname: string) => {
    if (
      menu.href &&
      (pathname === menu.href || pathname.startsWith(menu.href + "/"))
    ) {
      return true;
    }

    if (menu.subMenus) {
      return menu.subMenus.some(
        sub => pathname === sub.href || pathname.startsWith(sub.href + "/")
      );
    }

    return false;
  };

  return (
    <SidebarGroup>
      <SidebarGroupLabel>{groupLabel}</SidebarGroupLabel>
      <SidebarGroupContent>
        <SidebarMenu>
          {menus.map(menu => {
            if (menu.href)
              return (
                <SidebarMenuButton
                  className={cn(pathname === menu.href && "bg-secondary")}
                  key={menu.label}
                  variant="primary"
                  asChild
                >
                  <Link href={menu.href}>
                    <menu.icon />
                    <span>{menu.label}</span>
                  </Link>
                </SidebarMenuButton>
              );
            if (menu.subMenus)
              return (
                <Collapsible key={menu.label} className="group/collapsible">
                  <SidebarMenuItem>
                    <CollapsibleTrigger asChild>
                      <SidebarMenuButton
                        variant="primary"
                        className={cn(
                          isMenuActive(menu, pathname) && "bg-secondary"
                        )}
                      >
                        <menu.icon />
                        <span>{menu.label}</span>
                        <ChevronDown
                          strokeWidth={1.75}
                          className="ml-auto transition-transform group-data-[state=open]/collapsible:rotate-180"
                        />
                      </SidebarMenuButton>
                    </CollapsibleTrigger>

                    <CollapsibleContent className="group-data-[collapsible=icon]:hidden">
                      {menu.subMenus?.map(subMenu => (
                        <SidebarMenuButton
                          className={cn(
                            pathname === subMenu.href && "text-primary"
                          )}
                          key={subMenu.label}
                          variant="primary"
                          asChild
                        >
                          <Link href={subMenu.href!}>
                            <subMenu.icon />
                            <span>{subMenu.label}</span>
                          </Link>
                        </SidebarMenuButton>
                      ))}
                    </CollapsibleContent>
                  </SidebarMenuItem>
                </Collapsible>
              );
          })}
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  );
};

const DashboardSidebar = () => {
  const pathname = usePathname();

  return (
    <DashboardSidebarWrapper>
      <Sidebar collapsible="icon" className="z-40 shadow-sm">
        <SidebarHeader>
          <div className="shrink-0 h-[38px]">
            <Image
              src={droprightLogo}
              alt="DroprightLogo"
              className="h-full w-full"
            />
          </div>
        </SidebarHeader>
        <SidebarContent className="relative overflow-x-hidden">
          <div className="flex justify-end px-6 h-7">
            <SidebarKeepExpandedTrigger className="hidden md:flex opacity-80 group-data-[collapsible=icon]:opacity-0 transition-opacity duration-200" />
          </div>
          {renderSidebarGroup(dashboardMenus, "Dashboard", pathname)}
          <div className="px-4 max-w-16 opacity-0 group-data-[collapsible=icon]:opacity-100 transition-opacity duration-100">
            <Separator />
          </div>
          {renderSidebarGroup(adminMenus, "Admin", pathname)}
          <div className="px-4 max-w-16 opacity-0 group-data-[collapsible=icon]:opacity-100 transition-opacity duration-100">
            <Separator />
          </div>
          {renderSidebarGroup(additionalMenus, "Additional", pathname)}
        </SidebarContent>
        <SidebarFooter />
      </Sidebar>
    </DashboardSidebarWrapper>
  );
};

export default DashboardSidebar;
