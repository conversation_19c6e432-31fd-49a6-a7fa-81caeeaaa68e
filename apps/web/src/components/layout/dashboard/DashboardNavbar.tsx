"use client";

import ToggleThemeButton from "@/components/layout/theme/ToggleThemeButton";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { SidebarTriggerMobile } from "@/components/ui/sidebar";
import { useAppSelector } from "@/store/hooks";
import { BellAlertIcon, Cog6ToothIcon } from "@heroicons/react/24/outline";
import { Building2, Wallet } from "lucide-react";
import Link from "next/link";
import NavbarAvatarDropdown from "./NavbarAvatarDropdown";

const DashboardNavbar = () => {
  const { data: account } = useAppSelector(state => state.account);

  return (
    <header className="sticky top-0 z-30 px-4 md:px-6 xl:px-8 h-16 lg:h-20 shadow-sm bg-background flex items-center justify-between">
      <div className="flex items-center gap-1 md:gap-2">
        <SidebarTriggerMobile className="md:hidden" />

        <Select value="org_123" disabled>
          <SelectTrigger className="w-48 md:w-54" variant="ghost">
            <div className="flex items-center gap-2 overflow-hidden">
              <Building2 strokeWidth={1.25} />
              <SelectValue />
            </div>
          </SelectTrigger>
          {account?.organization.name && (
            <SelectContent>
              <SelectItem value="org_123">
                {account.organization.name}
              </SelectItem>
            </SelectContent>
          )}
        </Select>
        <Button variant="ghost" size="icon" asChild className="hidden md:flex">
          <Link href="/settings">
            <Cog6ToothIcon className="size-auto" />
          </Link>
        </Button>
      </div>

      <div className="flex items-center gap-4 h-8">
        <div className="hidden md:flex items-center gap-4">
          <p className="text-sm text-muted-foreground">Wallet</p>
          <Button size="lg" asChild>
            <Link href="/wallet">
              <Wallet />${account?.organization?.wallet?.availableBalance}
            </Link>
          </Button>
        </div>

        <Separator orientation="vertical" className="hidden md:block" />

        <div className="hidden md:flex items-center gap-2">
          <Button size="icon-lg" variant="ghost">
            <BellAlertIcon className="size-auto" />
          </Button>
          <ToggleThemeButton />
        </div>
        <NavbarAvatarDropdown />
      </div>
    </header>
  );
};

export default DashboardNavbar;
