"use client";

import { useSidebar } from "@/components/ui/sidebar";
import { useEffect, useRef } from "react";

const DashboardSidebarWrapper = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const { setOpen, state, keepExpanded } = useSidebar();

  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const container = containerRef.current;

    const handleMouseLeave = () => setOpen(false);
    const handleMouseEnter = () => setOpen(true);

    if (container && !keepExpanded) {
      container.addEventListener("mouseleave", handleMouseLeave);
      container.addEventListener("mouseenter", handleMouseEnter);
    }
    return () => {
      container?.removeEventListener("mouseleave", handleMouseLeave);
      container?.removeEventListener("mouseenter", handleMouseEnter);
    };
  }, [state, keepExpanded, setOpen]);

  return <div ref={containerRef}>{children}</div>;
};

export default DashboardSidebarWrapper;
