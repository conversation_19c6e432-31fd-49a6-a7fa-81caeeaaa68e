"use client";

import LogoutWrapper from "@/components/auth/LogoutWrapper";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useAppSelector } from "@/store/hooks";
import { LogOut, User, Wallet } from "lucide-react";
import Link from "next/link";

const NavbarAvatarDropdown = () => {
  const { data: account } = useAppSelector(state => state.account);

  const getInitials = (name: string | undefined | null) => {
    if (!name || name.trim() === "") return "";
    const parts = name.trim().split(/\s+/);
    if (parts.length === 1) {
      return parts[0].charAt(0);
    }
    return `${parts[0].charAt(0)}${parts[parts.length - 1].charAt(0)}`;
  };

  const fullName =
    account?.firstName && account?.lastName
      ? `${account?.firstName} ${account?.lastName}`
      : "";

  return (
    <DropdownMenu>
      <DropdownMenuTrigger className="cursor-pointer">
        <Avatar>
          <AvatarImage src={account?.image ?? undefined} />
          <AvatarFallback>{getInitials(fullName)}</AvatarFallback>
        </Avatar>
      </DropdownMenuTrigger>
      <DropdownMenuContent side="bottom" align="end">
        <DropdownMenuLabel>My Account</DropdownMenuLabel>
        <DropdownMenuSeparator />

        <DropdownMenuItem asChild>
          <Link href="/settings">
            <User />
            Profile
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link href="/wallet">
            <Wallet />
            Wallet
          </Link>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <LogoutWrapper>
          <DropdownMenuItem variant="destructive">
            <LogOut />
            Logout
          </DropdownMenuItem>
        </LogoutWrapper>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default NavbarAvatarDropdown;
