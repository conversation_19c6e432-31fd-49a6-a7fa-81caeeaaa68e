"use client";

import * as React from "react";
import * as TabsPrimitive from "@radix-ui/react-tabs";

import { cn } from "@/lib/utils";
import { cva, VariantProps } from "class-variance-authority";

function Tabs({
  className,
  ...props
}: React.ComponentProps<typeof TabsPrimitive.Root>) {
  return (
    <TabsPrimitive.Root
      data-slot="tabs"
      className={cn("flex flex-col gap-2", className)}
      {...props}
    />
  );
}

function TabsList({
  className,

  ...props
}: React.ComponentProps<typeof TabsPrimitive.List>) {
  const listRef = React.useRef<HTMLDivElement>(null);
  const [selectedTabWidth, setSelectedTabWidth] = React.useState(0);
  const [selectedTabLeft, setSelectedTabLeft] = React.useState(0);

  React.useEffect(() => {
    if (!listRef.current) return;

    // Function to find the selected tab and update measurements
    const updateSelectedTab = () => {
      if (!listRef.current) return;
      // Find the element with data-state="active" among children
      for (let i = 0; i < listRef.current.children.length; i++) {
        const tab = listRef.current.children[i] as HTMLElement;
        const state = tab.getAttribute("data-state");

        if (state === "active") {
          setSelectedTabWidth(tab.clientWidth);
          setSelectedTabLeft(tab.offsetLeft);
          break;
        }
      }
    };

    // Initial measurement
    updateSelectedTab();

    // Set up observer for attribute changes
    const observer = new MutationObserver(mutations => {
      mutations.forEach(mutation => {
        if (
          mutation.type === "attributes" &&
          mutation.attributeName === "data-state"
        ) {
          updateSelectedTab();
        }
      });
    });

    // Observe all tabs for data-state changes
    Array.from(listRef.current.children).forEach((child, index) => {
      console.log(`Setting up observer for tab ${index}`);
      observer.observe(child, {
        attributes: true,
        attributeFilter: ["data-state"],
        attributeOldValue: true, // This will give you the old value in mutations
      });
    });

    return () => {
      observer.disconnect();
    };
  }, []);

  return (
    <TabsPrimitive.List
      ref={listRef}
      data-slot="tabs-list"
      className={cn(
        "relative inline-flex h-9 w-fit items-center justify-center",
        "before:content-[''] before:block before:absolute before:top-full before:left-0 before:h-[3px] before:bg-foreground before:translate-x-0 before:transition-all before:duration-300 before:ease-out",
        `before:w-[var(--before-width)] before:left-[var(--before-left)]`,

        className
      )}
      style={
        {
          "--before-width": `${selectedTabWidth}px`,
          "--before-left": `${selectedTabLeft}px`,
        } as React.CSSProperties
      }
      {...props}
    />
  );
}

const tabsTriggerVariants = cva(
  "text-muted-foreground data-[state=active]:text-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 px-6 xl:px-8 py-1 font-semibold whitespace-nowrap transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",
  {
    variants: {
      variant: {
        default: "",
      },
      size: {
        default: "text-base",
        sm: "text-sm",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

function TabsTrigger({
  className,
  variant,
  size,
  ...props
}: React.ComponentProps<typeof TabsPrimitive.Trigger> &
  VariantProps<typeof tabsTriggerVariants>) {
  return (
    <TabsPrimitive.Trigger
      data-slot="tabs-trigger"
      className={cn(tabsTriggerVariants({ variant, size, className }))}
      {...props}
    />
  );
}

function TabsContent({
  className,
  ...props
}: React.ComponentProps<typeof TabsPrimitive.Content>) {
  return (
    <TabsPrimitive.Content
      data-slot="tabs-content"
      className={cn("flex-1 outline-none", className)}
      {...props}
    />
  );
}

export { Tabs, TabsList, TabsTrigger, TabsContent };
