"use client";

import * as React from "react";

import { cn } from "@/lib/utils";
import customStyles from "@/styles/Custom.module.css";
import { cva, VariantProps } from "class-variance-authority";

const tableVariants = cva("", {
  variants: {
    variant: {
      default: "bg-background",
      dark: "bg-muted/60",
    },
  },
  defaultVariants: {
    variant: "default",
  },
});

function Table({
  className,
  variant = "default",
  height = "",
  ...props
}: React.ComponentProps<"table"> &
  VariantProps<typeof tableVariants> & { height?: string }) {
  return (
    <div
      data-slot="table-container"
      data-variant={variant}
      data-header={height ? "sticky" : "default"}
      className={cn(
        tableVariants({ variant }),
        "relative w-full overflow-x-auto overflow-y-auto group",
        height,
        height && "min-h-80",
        customStyles.customScrollbar
      )}
    >
      <table
        data-slot="table"
        className={cn("w-full caption-bottom text-sm", className)}
        {...props}
      />
    </div>
  );
}

function TableHeader({ className, ...props }: React.ComponentProps<"thead">) {
  return (
    <thead
      data-slot="table-header"
      className={cn(
        "[&_tr]:border-b",
        "bg-background group-data-[variant=dark]:bg-muted/60 group-data-[header=sticky]:sticky group-data-[header=sticky]:top-0 group-data-[header=sticky]:z-10 group-data-[header=sticky]:drop-shadow-xs",
        className
      )}
      {...props}
    />
  );
}

function TableBody({ className, ...props }: React.ComponentProps<"tbody">) {
  return (
    <tbody
      data-slot="table-body"
      className={cn("[&_tr:last-child]:border-0", className)}
      {...props}
    />
  );
}

function TableFooter({ className, ...props }: React.ComponentProps<"tfoot">) {
  return (
    <tfoot
      data-slot="table-footer"
      className={cn(
        "bg-muted/50 border-t font-medium [&>tr]:last:border-b-0",
        className
      )}
      {...props}
    />
  );
}

function TableRow({ className, ...props }: React.ComponentProps<"tr">) {
  return (
    <tr
      data-slot="table-row"
      className={cn(
        "hover:bg-muted/50 data-[state=selected]:bg-secondary/20 border-b transition-colors",
        "group-data-[variant=dark]:hover:bg-muted",
        className
      )}
      {...props}
    />
  );
}

function TableHead({ className, ...props }: React.ComponentProps<"th">) {
  return (
    <th
      data-slot="table-head"
      className={cn(
        "text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",
        className
      )}
      {...props}
    />
  );
}

function TableCell({ className, ...props }: React.ComponentProps<"td">) {
  return (
    <td
      data-slot="table-cell"
      className={cn(
        "p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",
        className
      )}
      {...props}
    />
  );
}

function TableCaption({
  className,
  ...props
}: React.ComponentProps<"caption">) {
  return (
    <caption
      data-slot="table-caption"
      className={cn("text-muted-foreground mt-4 text-sm", className)}
      {...props}
    />
  );
}

export {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
};
