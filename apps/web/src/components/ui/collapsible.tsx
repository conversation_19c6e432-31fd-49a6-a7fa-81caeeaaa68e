"use client";

import { cn } from "@/lib/utils";
import * as CollapsiblePrimitive from "@radix-ui/react-collapsible";
import { cva, VariantProps } from "class-variance-authority";

function Collapsible({
  ...props
}: React.ComponentProps<typeof CollapsiblePrimitive.Root>) {
  return <CollapsiblePrimitive.Root data-slot="collapsible" {...props} />;
}

const collapsibleTriggerVariants = cva(
  "peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-full px-4 py-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] [&>svg]:size-4 [&>svg]:shrink-0 transition-colors duration-200 cursor-pointer",
  {
    variants: {
      variant: {
        default:
          "text-muted-foreground hover:bg-primary hover:text-primary-foreground hover:shadow-xs font-medium hover:bg-primary/90",
      },
      size: {
        default: "h-9 text-sm",
        sm: "h-7 text-xs",
        lg: "h-12 text-sm",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

function CollapsibleTrigger({
  variant = "default",
  size = "default",
  className,
  ...props
}: React.ComponentProps<typeof CollapsiblePrimitive.CollapsibleTrigger> &
  VariantProps<typeof collapsibleTriggerVariants>) {
  return (
    <CollapsiblePrimitive.CollapsibleTrigger
      data-slot="collapsible-trigger"
      className={cn(collapsibleTriggerVariants({ variant, size }), className)}
      {...props}
    />
  );
}

function CollapsibleContent({
  className,
  ...props
}: React.ComponentProps<typeof CollapsiblePrimitive.CollapsibleContent>) {
  return (
    <CollapsiblePrimitive.CollapsibleContent
      data-slot="collapsible-content"
      className={cn("py-1 pl-4 font-sans", className)}
      {...props}
    />
  );
}

export { Collapsible, CollapsibleContent, CollapsibleTrigger };
