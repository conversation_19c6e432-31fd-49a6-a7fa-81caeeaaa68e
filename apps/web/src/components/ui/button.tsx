import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@/lib/utils";

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-full text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer",
  {
    variants: {
      variant: {
        // Solid
        default:
          "bg-primary text-primary-foreground shadow-xs font-medium hover:bg-primary/90",
        secondary:
          "bg-secondary text-secondary-foreground shadow-xs font-medium hover:bg-secondary/80",
        accent:
          "bg-accent text-accent-foreground shadow-xs font-medium hover:bg-accent/90",
        muted: "bg-muted hover:bg-accent/5 shadow-xs dark:hover:bg-muted/80",
        destructive:
          "bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",
        "muted-destructive":
          "bg-destructive/30 text-destructive shadow-xs hover:bg-destructive/40 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/80 dark:text-white dark:hover:bg-destructive/70",
        // Ghost
        ghost: "hover:bg-muted hover:shadow-xs dark:hover:bg-muted/80",
        "ghost-primary": "text-primary hover:bg-primary/5",
        "ghost-destructive": "text-destructive hover:bg-destructive/5",
        outline:
          "border border-primary/80 text-primary bg-background shadow-xs hover:bg-primary/5 dark:border dark:border-primary/80",
        link: "text-primary font-medium underline-offset-4 hover:underline px-0!",
        "link-accent":
          "text-accent font-medium underline-offset-4 hover:underline px-0!",
        "link-foreground":
          "text-foreground font-medium underline-offset-4 hover:underline px-0!",
        "link-destructive":
          "text-destructive font-medium underline-offset-4 hover:underline px-0!",
      },
      size: {
        default:
          "h-9 px-4 py-2 has-[>svg]:px-3 [&_svg:not([class*='size-'])]:size-4",
        xxs: "h-5 gap-2 px-2 has-[>svg]:px-1.5 text-[10px] [&_svg:not([class*='size-'])]:size-3",
        xs: "h-6 gap-1 px-2 has-[>svg]:px-1.5 text-xs [&_svg:not([class*='size-'])]:size-3",
        sm: "h-8 gap-1.5 px-3 has-[>svg]:px-2.5 [&_svg:not([class*='size-'])]:size-4",
        lg: "h-9 md:h-10 px-5 md:px-6 has-[>svg]:px-4 [&_svg:not([class*='size-'])]:size-4.5",
        icon: "size-9 [&_svg]:size-4",
        "icon-xs": "size-6 [&_svg]:size-3",
        "icon-sm": "size-8 [&_svg]:size-4",
        "icon-lg": "size-10 [&_svg]:size-5",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

function Button({
  variant,
  size,
  className,
  asChild = false,
  ...props
}: React.ComponentProps<"button"> &
  VariantProps<typeof buttonVariants> & {
    asChild?: boolean;
  }) {
  const Comp = asChild ? Slot : "button";

  return (
    <Comp
      data-slot="button"
      className={cn(buttonVariants({ variant, size, className }))}
      {...props}
    />
  );
}

export { Button, buttonVariants };
