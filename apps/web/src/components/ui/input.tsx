import * as React from "react";

import { cn } from "@/lib/utils";
import { cva, VariantProps } from "class-variance-authority";
import { But<PERSON> } from "./button";

const inputVariants = cva(
  "file:text-foreground placeholder:text-muted-foreground/50 selection:bg-primary selection:text-primary-foreground dark:bg-transparent flex w-full min-w-0 rounded-full border bg-transparent py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-40 disabled:bg-muted md:text-sm focus-visible:border-input/80 focus-visible:ring-ring/70 focus-visible:ring-[1px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",

  {
    variants: {
      variant: {
        default: "border-input",
        ghost:
          "border-none bg-muted hover:bg-accent/5 shadow-xs dark:bg-muted dark:hover:bg-muted/80",
      },
      size: {
        default: "h-9",
        sm: "h-8",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

function Input({
  className,
  variant = "default",
  rightIcon,
  type,
  inputSize,
  ...props
}: React.ComponentProps<"input"> &
  VariantProps<typeof inputVariants> & {
    inputSize?: "default" | "sm";
    rightIcon?: React.ElementType;
  }) {
  return (
    <div className="relative">
      <input
        type={type}
        data-slot="input"
        className={cn(
          rightIcon ? "pl-4 pr-10" : "px-4",
          inputVariants({ variant, size: inputSize, className })
        )}
        {...props}
      />
      {rightIcon && (
        <Button
          type="button"
          size="icon"
          variant="link"
          className="absolute right-2 top-1/2 -translate-y-1/2"
        >
          {React.createElement(rightIcon)}
        </Button>
      )}
    </div>
  );
}

export { Input };
