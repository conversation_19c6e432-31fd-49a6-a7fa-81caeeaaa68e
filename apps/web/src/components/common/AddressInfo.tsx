import { EnvelopeIcon, HomeIcon, PhoneIcon } from "@heroicons/react/24/solid";

type AddressInfoProps = {
  name: string;
  company?: string | null;
  street1: string;
  street2?: string | null;
  city: string;
  state?: string | null;
  zip: string;
  country: string;
  phone?: string | null;
  email?: string | null;
  residential?: boolean;
};

const AddressInfo = ({
  name,
  company,
  street1,
  street2,
  city,
  state,
  zip,
  country,
  phone,
  email,
  residential = false,
}: AddressInfoProps) => {
  const addressLine = `${street1}, ${street2 ?? ""} ${city}, ${state ?? ""} ${zip} ${country}`;

  return (
    <div className="space-y-1 text-sm">
      <h3 className="font-semibold capitalize flex gap-1 items-center">
        {residential && <HomeIcon className="size-3.5 text-primary mb-[2px]" />}
        {name.toLowerCase()}
      </h3>
      <p className="text-muted-foreground capitalize">
        {company && <>{company.toLowerCase()},</>} {addressLine.toLowerCase()}
      </p>

      <div className="flex flex-wrap gap-4">
        {phone && (
          <div className="flex items-center gap-1.5 text-muted-foreground">
            <PhoneIcon className="size-3.5 mb-0.5" />
            {phone}
          </div>
        )}
        {email && (
          <div className="flex items-center gap-1.5 text-muted-foreground">
            <EnvelopeIcon className="size-3.5 mb-0.5" />
            {email.toLowerCase()}
          </div>
        )}
      </div>
    </div>
  );
};

export default AddressInfo;
