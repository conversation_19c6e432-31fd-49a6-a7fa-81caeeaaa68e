"use client";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { formatFileSize } from "@/lib/utils/fileUtils";
import { FileUp, PlusCircle, X } from "lucide-react";
import { useRef, useState } from "react";

const FILE_MAPPING = {
  ".pdf": "application/pdf",
  ".jpg": "image/jpeg",
  ".jpeg": "image/jpeg",
  ".png": "image/png",
  ".webp": "image/webp",
  ".csv": "text/csv",
  ".xlsx": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  ".xls": "application/vnd.ms-excel",
};
const DEFAULT_MAX_FILES = 10;
const DEFAULT_ACCEPT: (keyof typeof FILE_MAPPING)[] = [
  ".pdf",
  ".jpg",
  ".jpeg",
  ".png",
  ".webp",
  ".csv",
  ".xlsx",
  ".xls",
];

type FileUploadProps = {
  files: File[];
  maxFiles?: number;
  accept?: (keyof typeof FILE_MAPPING)[];
  className?: string;
  onChange: (files: File[]) => void;
  onError?: (value: string | null) => void;
};

const FileUpload = ({
  files,
  maxFiles = DEFAULT_MAX_FILES,
  accept = DEFAULT_ACCEPT,
  className = "",
  onChange,
  onError,
}: FileUploadProps) => {
  // States
  const [inDropZone, setInDropZone] = useState(false);

  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setInDropZone(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setInDropZone(false);
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setInDropZone(false);

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      if (files.length >= maxFiles) {
        onError?.("You can only upload one file at a time");
        return;
      }

      handleFiles(e.dataTransfer.files);
      e.dataTransfer.clearData();
    }
  };

  const handleFiles = (fileList: FileList) => {
    onError?.(null);
    const newFiles = Array.from(fileList);
    const isValid = newFiles.every(file => isValidFileType(file, accept));

    if (!isValid) {
      onError?.(`Please upload supported file types: ${accept.join(", ")}`);
      return;
    }

    if (newFiles.length + files.length > maxFiles) {
      onError?.(`You can only upload up to ${maxFiles} files at a time`);
      return;
    }

    const merged = [...files, ...newFiles].filter(
      (file, index, self) =>
        self.findIndex(f => f.name === file.name && f.size === file.size) ===
        index
    );

    onChange(merged.slice(0, maxFiles));
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newFiles = Array.from(e.target.files ?? []);
    // Prevent duplicates by name (optional)

    if (newFiles.length > maxFiles) {
      onError?.(`You can only upload ${maxFiles} file at a time`);
      return;
    }

    const merged = [...files, ...newFiles].filter(
      (file, index, self) =>
        self.findIndex(f => f.name === file.name && f.size === file.size) ===
        index
    );
    onChange(merged);
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  const handleRemoveFile = (file: File) => {
    const filtered = files.filter(f => f !== file);
    onChange(filtered);
    if (fileInputRef.current) fileInputRef.current.value = "";
  };

  return (
    <div
      onDragEnter={handleDragEnter}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
      className={cn("relative", className)}
    >
      <Input
        ref={fileInputRef}
        id="upload-file"
        type="file"
        accept={accept.join(",")}
        className="hidden"
        multiple
        onChange={e => handleInputChange(e)}
      />
      {files.length === 0 ? (
        <div
          className={cn(
            "h-full border border-primary border-dashed rounded-md grid place-content-center px-4",
            inDropZone ? "cursor-copy" : "cursor-default bg-muted",
            "transition-colors-duration-300",
            className
          )}
        >
          <div
            className={cn(
              "text-sm text-center transition-opacity duration-300",
              inDropZone ? "opacity-0" : "opacity-100"
            )}
          >
            <FileUp
              className="inline text-primary size-4 mx-0.5 mb-0.5 cursor-pointer hover:scale-105"
              onClick={openFileDialog}
            />
            Drag and drop file here or{" "}
            <Button
              type="button"
              variant="link"
              className="p-0"
              onClick={openFileDialog}
            >
              Browse Files
            </Button>
          </div>
          <div className="flex justify-center text-xs text-muted-foreground text-center">
            Accepted file types:{" "}
            {accept.map(ext => ext.replace(".", "")).join(", ")}
          </div>
        </div>
      ) : (
        <div>
          {files.map((file, index) => (
            <div
              key={index + file.name}
              className="flex justify-between gap-4 p-2.5 not-first:mt-1 bg-white rounded-md"
            >
              <div>
                <p className="text-sm font-medium">{file.name}</p>
                <p className="text-muted-foreground/80 text-sm uppercase">
                  {file.type.split("/")[1]}, {formatFileSize(file.size, 2)}
                </p>
              </div>
              <Button
                variant="ghost-destructive"
                size="icon-sm"
                onClick={() => handleRemoveFile(file)}
              >
                <X />
              </Button>
            </div>
          ))}
          {files.length < maxFiles && (
            <Button
              className="mt-2"
              variant="secondary"
              onClick={openFileDialog}
              type="button"
              size="sm"
            >
              <PlusCircle />
              Add More
            </Button>
          )}
        </div>
      )}
      {files.length < maxFiles && (
        <div
          className={cn(
            "absolute pointer-events-none inset-0 grid place-content-center transition-opacity duration-300 bg-secondary/70 rounded-md",
            inDropZone ? "opacity-100" : "opacity-0"
          )}
        >
          <FileUp className="text-secondary-foreground size-6" />
        </div>
      )}
    </div>
  );
};

// Utils

/* Check if the file type is valid */
const isValidFileType = (
  file: File,
  accept: (keyof typeof FILE_MAPPING)[]
): boolean => {
  const validTypes = accept.map(ext => FILE_MAPPING[ext]);
  return validTypes.includes(file.type);
};

export default FileUpload;
