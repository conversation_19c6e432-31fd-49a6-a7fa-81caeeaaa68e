import {
  Batch<PERSON>tatus,
  ClaimStatus,
  OrderStatus,
  ShipmentStatus,
} from "@repo/database";
import { Split } from "lucide-react";
import Status from "../Status";

type SummaryCardData = {
  label: string;
  value: string;
  isSplit?: boolean;
};

type SummaryCardItemProps = {
  data: SummaryCardData[];
};

const SummaryCard = ({ data }: SummaryCardItemProps) => {
  return (
    <div className="w-full sm:w-fit sm:min-w-60 md:min-w-80 rounded-md bg-muted px-4 py-2 md:py-4 text-sm space-y-1.5 self-end md:self-auto">
      {data.map(item => (
        <SummaryCardItem key={item.label} {...item} />
      ))}
    </div>
  );
};

const SummaryCardItem = (data: SummaryCardData) => {
  return (
    <div className="flex justify-between items-center gap-2">
      <div className="font-medium capitalize">{data.label}:</div>
      <div className="flex items-center gap-1">
        {data.isSplit && <Split className="size-3 text-primary" />}{" "}
        {data.label === "status" ? (
          <Status
            text={
              data.value as
                | OrderStatus
                | BatchStatus
                | ShipmentStatus
                | ClaimStatus
            }
          />
        ) : (
          data.value
        )}
      </div>
    </div>
  );
};

export default SummaryCard;
