import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cn } from "@/lib/utils";
import { useConvertLabelMutation } from "@/store/api/shipmentApi";
import { PostageLabelJson } from "@/types/Shipment/PostageLabelJson";
import { DownloadIcon, Loader2, PrinterIcon } from "lucide-react";
import { useRef, useState } from "react";

type handlePrintProps = {
  postageLabel: PostageLabelJson;
  id: string;
  buttonSize?: "xs" | "sm" | "lg";
  buttonClassName?: string;
};

export function PrintModal({
  postageLabel,
  id,
  buttonSize = "xs",
  buttonClassName = "",
}: handlePrintProps) {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [saveDefault, setSaveDefault] = useState(false);
  const [fileFormat, setFileFormat] = useState<"PNG" | "PDF" | "ZPL">("PNG");
  const [convertLabel, { isLoading }] = useConvertLabelMutation();

  const handlePrint = () => {
    const printWindow = window.open("", "_blank");
    if (!printWindow) return;
    if (fileFormat === "PNG") {
      printWindow.document.write(`
        <html>
          <head>
            <title>Print Image</title>
          </head>
          <body style="margin: 0; text-align: center;">
            <img
              src="${postageLabel.labelUrl}"
              style="width: 92%; height: auto; display: block; margin: 10mm auto;"
              onload="window.print(); window.onafterprint = window.close();"
            />
          </body>
        </html>
      `);
      return printWindow.document.close();
    }

    if (fileFormat === "PDF") {
      window.open(postageLabel.labelPdfUrl, "_blank");
    }
  };

  const handleFormatChange = async (value: "PNG" | "PDF" | "ZPL") => {
    setFileFormat(value);

    const hasUrl =
      (value === "PDF" && postageLabel.labelPdfUrl) ||
      (value === "PNG" && postageLabel.labelUrl) ||
      (value === "ZPL" && postageLabel.labelZplUrl);

    if (hasUrl) return;

    try {
      await convertLabel({
        epShipmentId: id,
        data: { format: value },
      }).unwrap();
    } catch (err) {
      console.error("Failed to convert label:", err);
    }
  };

  const renderPreviewIframe = () => {
    if (fileFormat === "PDF") {
      return (
        <iframe
          ref={iframeRef}
          src={postageLabel.labelPdfUrl}
          className="w-full h-full"
          title="Label PDF"
        />
      );
    }

    if (fileFormat === "PNG") {
      return (
        <iframe
          ref={iframeRef}
          srcDoc={createImageWrapper(postageLabel?.labelUrl)}
          className="w-full h-full"
          title="Label PNG"
        />
      );
    }

    if (fileFormat === "ZPL") {
      return (
        <div className="text-white flex justify-center items-center h-full px-8">
          ZPL files cannot be previewed. Please download the file instead.
        </div>
      );
    }

    return null;
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button size={buttonSize} className={cn(buttonClassName, "h-full")}>
          <PrinterIcon className="size-3.5 mr-1" />
          Print Label
        </Button>
      </DialogTrigger>

      <DialogContent className="border-none lg:min-w-4xl xl:min-w-7xl p-0 gap-0 rounded">
        <DialogTitle className="p-4 bg-muted">
          Print Preview: 1 Label
        </DialogTitle>
        <div className="flex flex-col lg:flex-row w-full h-[70vh]">
          <div className="flex-1 bg-black">{renderPreviewIframe()}</div>

          <div className="w-full lg:w-[320px] bg-white p-4 flex flex-col justify-between">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold mb-2">Print Options</h3>

              <div>
                <p className="text-sm font-medium">Browser Print</p>
                <p className="text-sm text-muted-foreground">
                  Hover the preview or click below to print.
                </p>
                <Button asChild variant="outline" className="mt-2 w-full">
                  <a
                    href={`${process.env.NEXT_PUBLIC_API_URL}/shipments/${id}/download-label`}
                  >
                    {isLoading ? (
                      <Loader2 className="animate-spin" />
                    ) : (
                      <DownloadIcon className="h-4 w-4" />
                    )}
                    Download
                  </a>
                </Button>
              </div>

              <div>
                <div className="text-sm font-medium pb-1">File Format</div>
                <Select value={fileFormat} onValueChange={handleFormatChange}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select a Format" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      <SelectLabel>Format</SelectLabel>
                      <SelectItem value="PNG">PNG</SelectItem>
                      <SelectItem value="PDF">PDF</SelectItem>
                      <SelectItem value="ZPL">ZPL</SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="flex items-center gap-2 pt-2">
              <Checkbox
                id="save"
                checked={saveDefault}
                onCheckedChange={checked => setSaveDefault(!!checked)}
              />
              <label htmlFor="save" className="text-sm">
                Save as <span className="font-semibold">Label</span> print
                default
              </label>
            </div>
          </div>
        </div>
        {/* Footer Buttons */}
        <DialogFooter className="p-2">
          <div className="flex justify-between w-full">
            <Button variant="destructive">Void Label</Button>

            <div className="flex gap-2 justify-end">
              <DialogTrigger asChild>
                <Button variant="outline">Cancel</Button>
              </DialogTrigger>
              <Button onClick={handlePrint} disabled={fileFormat === "ZPL"}>
                <PrinterIcon className="h-4 w-4" /> Print
              </Button>
            </div>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

const createImageWrapper = (imageUrl: string) => `
  <!DOCTYPE html>
  <html>
  <head>
    <style>
      body { margin: 0; padding: 0; display: flex; justify-content: center; align-items: center; height: 100vh; }
      img { max-width: 100%; max-height: 100%; object-fit: contain; }
    </style>
  </head>
  <body>
    <img src="${imageUrl}" alt="Image">
  </body>
  </html>
`;
