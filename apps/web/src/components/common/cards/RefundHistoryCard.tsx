import { cn } from "@/lib/utils";
import { capitalize } from "@/lib/utils/strings";
import styles from "@/styles/Dashboard.module.css";
import { LabelRefund } from "@repo/database";
import { formatDate } from "date-fns";
import { Banknote } from "lucide-react";
import CarrierServiceBadge from "../CarrierServiceBadge";
import Header from "@/components/layout/Header";

type RefundHistoryCardProps = {
  refunds: LabelRefund[];
};

const RefundHistoryCard = ({ refunds }: RefundHistoryCardProps) => {
  if (!refunds || refunds.length === 0) return null;

  return (
    <div className={cn(styles.panelContainer, styles.panelYSpacing)}>
      <Header icon={Banknote}>Refund History</Header>
      <div className="[&>div:not(:last-child)]:after:content-[''] [&>div:not(:last-child)]:after:block [&>div:not(:last-child)]:after:border-b [&>div:not(:last-child)]:after:border-border [&>div:not(:last-child)]:after:my-2">
        {refunds.map(refund => (
          <div key={refund.id} className="space-y-1.5">
            <div className="flex justify-between">
              <CarrierServiceBadge
                carrierId={refund.carrier}
                serviceId={refund.service}
              />
              <div className="font-semibold text-sm">${refund.rate}</div>
            </div>
            <div className="space-y-0.5">
              <div className="text-xs flex justify-between gap-1">
                <div className="font-medium">Status:</div>
                <div className="text-muted-foreground">
                  {capitalize(refund.status)}
                </div>
              </div>
              <div className="text-xs flex justify-between gap-1">
                <div className="font-medium">Voided at:</div>
                <div className="text-muted-foreground">
                  {formatDate(refund.createdAt, "MMMM d, yyyy")}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default RefundHistoryCard;
