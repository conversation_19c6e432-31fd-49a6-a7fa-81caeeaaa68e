"use client";

import CreateClaimModal from "@/app/(protected)/shipments/_components/modal/CreateClaimModal";
import CreateReturnModal from "@/app/(protected)/shipments/_components/modal/CreateReturnModal";
import CopyTextWrapper from "@/components/common/CopyTextWrapper";
import ClaimIcon from "@/components/icons/ClaimIcon";
import { Button } from "@/components/ui/button";
import { DialogTrigger } from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useConfirm } from "@/hooks/useConfirm";
import { cn } from "@/lib/utils";
import { useRefundShipmentMutation } from "@/store/api/shipmentApi";
import { FindOneOrder } from "@/types/Order/Order";
import { FindOneReturn } from "@/types/Return/Return";
import { PostageLabelJson } from "@/types/Shipment/PostageLabelJson";
import { FindOneShipment } from "@/types/Shipment/Shipment";
import {
  ArrowUturnDownIcon,
  ChevronDownIcon,
} from "@heroicons/react/24/outline";
import { Shipment, ShipmentStatus } from "@repo/database";
import { CircleX, Copy } from "lucide-react";
import Link from "next/link";
import { useLayoutEffect, useRef, useState } from "react";
import { toast } from "sonner";
import CarrierServiceCard from "./CarrierServiceCard";
import { PrintModal } from "./PrintModal";

type RateDetailCardProps = {
  objectType: "order" | "shipment" | "return";
  shipment: FindOneShipment | FindOneReturn | FindOneOrder["shipment"];
};

const RateDetailCard = ({ objectType, shipment }: RateDetailCardProps) => {
  const [open, setOpen] = useState(false);
  const triggerRef = useRef<HTMLButtonElement>(null);
  const [contentWidth, setContentWidth] = useState<number | undefined>(
    undefined
  );

  const { confirm, ConfirmModal } = useConfirm();

  useLayoutEffect(() => {
    if (triggerRef.current) {
      setContentWidth(triggerRef.current.offsetWidth);
    }
  }, []);

  const trackingCode = shipment.trackingCode;
  // TODO: add tracker URL to shipment
  // const publicUrl = epShipment?.tracker?.publicUrl;
  const insurance = shipment.insurance;
  const postageLabel = shipment.postageLabel;
  const rate = shipment.rate;

  // const today = new Date();
  // const fifteenDaysAgo = subDays(today, 15);
  // const shipDate = shipment.shipDate;
  // const isWithinClaimWindow =
  //   shipDate !== null &&
  //   isAfter(shipDate, fifteenDaysAgo) &&
  //   isBefore(shipDate, today);

  const isGetShipmentResponse = (
    shipment: Shipment | FindOneShipment | FindOneReturn
  ): shipment is FindOneShipment =>
    (shipment as FindOneShipment).order !== undefined;

  const [refundShipment] = useRefundShipmentMutation();

  const handleVoidLabel = async () => {
    const isConfirmed = await confirm({
      title: "Void Label",
      description: "Are you sure you want to void this label?",
      variant: "destructive",
    });

    if (!isConfirmed) return;

    try {
      await refundShipment({ id: shipment.id }).unwrap();
      toast.success("Label voided successfully");
    } catch (error) {
      if (process.env.NODE_ENV === "development") console.log(error);
      toast.error("Unable to request refund. The parcel has been shipped.");
    }
  };

  return (
    <div className="border rounded-lg p-2 space-y-6 overflow-x-hidden">
      <div>
        <CarrierServiceCard
          carrierId={shipment.carrier}
          serviceId={shipment.service}
          rate={rate}
          isVoided={shipment.status === ShipmentStatus.voided}
        />
      </div>

      <div className={cn("pb-4 px-4 text-sm space-y-4")}>
        <div>
          <div className="text-xs text-muted-foreground font-semibold">
            Tracking Code
          </div>
          <div className="flex justify-between items-center">
            {trackingCode}
            <div className="relative">
              <CopyTextWrapper text={trackingCode} isTable={false}>
                <Button variant="muted" size="xs">
                  <Copy />
                </Button>
              </CopyTextWrapper>
            </div>
          </div>
        </div>

        <div>
          <div className="text-xs text-muted-foreground font-semibold">
            Insurance
          </div>
          <div className="text-accent">{String(insurance ?? "N/A")}</div>
        </div>
        <div>
          <div className="text-xs text-muted-foreground font-semibold">
            Label
          </div>
          <div className="flex justify-between items-center">
            <div className="text-accent">
              {shipment?.labelPrinted ? "Printed" : "Not Printed"}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          {shipment.status !== ShipmentStatus.voided ? (
            <>
              <DropdownMenu open={open} onOpenChange={setOpen}>
                <DropdownMenuTrigger
                  ref={triggerRef}
                  className="w-full rounded-full py-2 bg-muted cursor-pointer flex justify-center items-center space-x-2"
                >
                  <span className="font-medium">Other Actions</span>
                  <ChevronDownIcon
                    className={`size-4 shrink-0 transition-transform duration-200 ${open ? "rotate-180" : "rotate-0"}`}
                  />
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  align="end"
                  style={{ width: contentWidth }}
                  className="!p-1"
                >
                  {objectType === "shipment" &&
                    isGetShipmentResponse(shipment) && (
                      <CreateReturnModal
                        shipmentId={shipment.id}
                        orderItems={shipment.order.orderItems}
                        trigger={
                          <DropdownMenuItem
                            onClick={e => {
                              e.preventDefault();
                              e.stopPropagation();
                            }}
                          >
                            <DialogTrigger className="flex items-center gap-2 cursor-pointer w-full">
                              <ArrowUturnDownIcon className="w-4 h-4" />
                              <span className="text-sm">Create Return</span>
                            </DialogTrigger>
                          </DropdownMenuItem>
                        }
                      />
                    )}
                  {(objectType === "shipment" || objectType === "return") &&
                    insurance && (
                      <CreateClaimModal
                        type={objectType}
                        shipmentId={shipment.id}
                        trackingCode={shipment.trackingCode}
                        insurance={shipment.insurance}
                        trigger={
                          <DropdownMenuItem
                            onClick={e => {
                              e.preventDefault();
                              e.stopPropagation();
                            }}
                          >
                            <DialogTrigger className="flex items-center gap-2 cursor-pointer w-full">
                              <ClaimIcon />
                              <span className="text-sm">Create Claim</span>
                            </DialogTrigger>
                          </DropdownMenuItem>
                        }
                      />
                    )}

                  <DropdownMenuItem
                    variant="destructive"
                    onClick={handleVoidLabel}
                  >
                    <CircleX className="size-4 text-destructive" />
                    Void label
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              {postageLabel && (
                <PrintModal
                  postageLabel={postageLabel as PostageLabelJson}
                  id={shipment.epShipmentId as string}
                  buttonSize="sm"
                />
              )}
            </>
          ) : (
            <>
              <Button className="col-start-2" asChild>
                <Link href={`/shipments/${shipment.id}/create-new-label`}>
                  Create new label
                </Link>
              </Button>
            </>
          )}

          <ConfirmModal />
        </div>
      </div>
    </div>
  );
};

export default RateDetailCard;
