import Header from "@/components/layout/Header";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { cn } from "@/lib/utils";
import styles from "@/styles/Dashboard.module.css";
import { AddressJson } from "@/types/Address/AddressJson";
import { Address, OrderItem, Product, ReturnItem } from "@repo/database";
import { format } from "date-fns";
import { Package, ReceiptText } from "lucide-react";
import AddressInfo from "../AddressInfo";

type OrderDetailsCardProps = {
  toAddress: AddressJson | Address;
  orderDate: Date;
  orderNo: string;
  orderItems:
    | (OrderItem & { product: Product })[]
    | (ReturnItem & { product: Product })[];
  notes: string;
  isReturn?: boolean;
  rma?: string;
};

const OrderDetailsCard = ({
  toAddress,
  orderDate,
  orderNo,
  orderItems,
  notes,
  isReturn = false,
  rma,
}: OrderDetailsCardProps) => {
  return (
    <div className={cn(styles.panelContainer, styles.panelYSpacing)}>
      <div className="flex flex-col md:flex-row md:items-start md:justify-between lg:flex-col xl:flex-row gap-4">
        <div className="space-y-4">
          <Header icon={ReceiptText}>
            {isReturn ? "Return Details" : "Order Details"}
          </Header>
          <div className="flex flex-col gap-1">
            <label className="text-sm text-muted-foreground font-medium">
              Customer
            </label>
            <AddressInfo
              name={toAddress.name}
              company={toAddress.company}
              street1={toAddress.street1}
              street2={toAddress.street2}
              city={toAddress.city}
              state={toAddress.state}
              zip={toAddress.zip}
              country={toAddress.country}
              phone={toAddress.phone}
              email={toAddress.email}
              residential={toAddress.residential}
            />
          </div>
        </div>

        <div className="text-sm text-muted-foreground grid grid-cols-[auto_1fr] gap-x-4 gap-y-1">
          <div className="font-medium">Date</div>
          <div>{format(orderDate, "MMMM d, yyyy")}</div>
          <div className="font-medium">Order No</div>
          <div>{orderNo}</div>
          {isReturn && rma && (
            <>
              <div className="font-medium">RMA</div>
              <div>{rma}</div>
            </>
          )}
        </div>
      </div>

      <div className="space-y-3">
        <Header icon={Package} as="h3" variant="section">
          Items
        </Header>
        <OrderItemTable items={orderItems} isReturn={isReturn} />
        <Notes notes={notes} />
      </div>
    </div>
  );
};

const OrderItemTable = ({
  items,
  isReturn = false,
}: {
  items:
    | (OrderItem & { product: Product })[]
    | (ReturnItem & { product: Product })[];
  isReturn?: boolean;
}) => {
  return (
    <div className="border rounded-md overflow-hidden">
      <Table variant="dark">
        <TableHeader>
          <TableRow>
            <TableHead className="w-[100px]">SKU</TableHead>
            <TableHead>Name</TableHead>
            <TableHead className="text-right">Quantity</TableHead>
            <TableHead className="text-right">Price (USD)</TableHead>
            {isReturn && <TableHead>Reason</TableHead>}
          </TableRow>
        </TableHeader>
        <TableBody>
          {items.map((item, index) => (
            <TableRow key={index}>
              <TableCell>{item?.product?.sku || "-"}</TableCell>
              <TableCell>{item?.product?.name ?? "-"}</TableCell>
              <TableCell className="text-right">{item.quantity}</TableCell>
              <TableCell className="text-right">
                ${item.price.toFixed(2)}
              </TableCell>
              {isReturn && (
                <TableCell>{(item as ReturnItem).reason || "-"}</TableCell>
              )}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

const Notes = ({ notes }: { notes: string }) => {
  return (
    <div className="py-2 text-sm space-y-1 flex flex-col md:flex-row items-baseline gap-x-2.5">
      <h4 className="font-medium">Notes:</h4>
      <p className="text-muted-foreground">{notes}</p>
    </div>
  );
};

export default OrderDetailsCard;
