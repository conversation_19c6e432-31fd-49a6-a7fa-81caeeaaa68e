import { cn } from "@/lib/utils";
import styles from "@/styles/Dashboard.module.css";
import { AddressJson } from "@/types/Address/AddressJson";
import { ParcelJson } from "@/types/Parcel/ParcelJson";
import { Address } from "@repo/database";
import { Parcel } from "@repo/easypost-types";
import { format } from "date-fns";
import { PencilRuler, Truck } from "lucide-react";
import Header from "@/components/layout/Header";
import AddressInfo from "../AddressInfo";
import UnitDisplay from "../../parcel/UnitDisplay";

type ShipmentDetailsCardProps = {
  fromAddress: AddressJson | Address;
  toAddress: AddressJson | Address;
  parcel: ParcelJson | Parcel;
  shipDate?: Date | null;
  textToAddress?: string;
};

const ShipmentDetailsCard = ({
  fromAddress,
  toAddress,
  parcel,
  shipDate,
  textToAddress = "Ship To",
}: ShipmentDetailsCardProps) => {
  return (
    <div className={cn(styles.panelContainer, styles.panelYSpacing)}>
      {/* Shipping Details */}
      <div className="flex flex-col md:flex-row md:justify-between gap-4">
        <div className="space-y-4 w-full">
          <div className="space-y-4 sm:flex justify-between items-start">
            <Header icon={Truck}>Shipment Details</Header>

            <div className="text-sm flex gap-3">
              <div className="font-medium text-muted-foreground">Ship Date</div>
              <div className="text-muted-foreground">
                {shipDate ? format(shipDate, "MMMM d, yyyy") : "-"}
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex flex-col gap-1">
              <label className="text-sm text-muted-foreground font-medium">
                Ship From
              </label>
              <AddressInfo
                name={fromAddress.name}
                company={fromAddress.company}
                street1={fromAddress.street1}
                street2={fromAddress.street2}
                city={fromAddress.city}
                state={fromAddress.state}
                zip={fromAddress.zip}
                country={fromAddress.country}
                residential={fromAddress.residential}
              />
            </div>

            <div className="flex flex-col gap-1">
              <label className="text-sm text-muted-foreground font-medium">
                {textToAddress}
              </label>
              <AddressInfo
                name={toAddress.name}
                company={toAddress.company}
                street1={toAddress.street1}
                street2={toAddress.street2}
                city={toAddress.city}
                state={toAddress.state}
                zip={toAddress.zip}
                country={toAddress.country}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Parcel Details */}
      <div className="flex flex-col sm:flex-row justify-between items-baseline gap-2">
        <Header icon={PencilRuler}>Parcel Details</Header>
        <UnitDisplay />
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <div className="text-sm text-muted-foreground font-medium">
            Size (in)
          </div>
          <p className="text-sm text-muted-foreground">
            {parcel.length ?? "-"} x {parcel.width ?? "-"} x{" "}
            {parcel.height ?? "-"}
          </p>
        </div>

        <div>
          <label className="text-sm text-muted-foreground font-medium">
            Weight (oz)
          </label>
          <p className="text-sm text-muted-foreground">{parcel.weight}</p>
        </div>

        <div>
          <div className="text-sm text-muted-foreground font-medium">
            Predefined Package
          </div>
          <p className="text-sm text-muted-foreground">
            {parcel.predefinedPackage ?? "-"}
          </p>
        </div>
      </div>
    </div>
  );
};

export default ShipmentDetailsCard;
