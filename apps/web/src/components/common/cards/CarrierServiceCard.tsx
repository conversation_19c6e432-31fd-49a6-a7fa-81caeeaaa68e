import { Badge } from "@/components/ui/badge";
import { getCarrierIcon } from "@/lib/mappings/carriers";
import { cn } from "@/lib/utils";
import { getCarrierBasicInfo, getServiceLevel } from "@repo/shared-data";
import Image from "next/image";

type CarrierServiceCardProps = {
  carrierId: string;
  serviceId: string;
  rate: string;
  isVoided?: boolean;
};

const CarrierServiceCard = ({
  carrierId,
  serviceId,
  rate,
  isVoided = false,
}: CarrierServiceCardProps) => {
  const carrier = getCarrierBasicInfo(carrierId);
  const service = getServiceLevel(carrierId, serviceId);
  const carrierIcon = getCarrierIcon(carrierId);

  return (
    <div className="w-full relative rounded-md p-4 bg-muted">
      {!carrier || !service || !carrierIcon ? (
        <div>Unavailable</div>
      ) : (
        <div className={cn("flex justify-between", isVoided && "opacity-70")}>
          {carrier && service && (
            <div className="flex items-center gap-2 lg:gap-3">
              <Image
                src={carrierIcon}
                alt={carrier.fullName}
                width={48}
                height={48}
              />
              <div>
                <div className="xl:text-lg font-semibold leading-snug flex items-center gap-1.5">
                  {carrier.shortName}{" "}
                  {isVoided && (
                    <Badge variant="destructive" size="sm">
                      Voided
                    </Badge>
                  )}
                </div>
                <div className="text-muted-foreground text-xs">
                  {service.label}
                </div>
              </div>
            </div>
          )}
          <div className="font-semibold xl:text-lg">$ {rate}</div>
        </div>
      )}
    </div>
  );
};

export default CarrierServiceCard;
