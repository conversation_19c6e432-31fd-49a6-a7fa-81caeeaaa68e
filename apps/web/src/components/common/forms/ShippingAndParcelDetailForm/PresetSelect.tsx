import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const PresetSelect = () => {
  return (
    <Select>
      <SelectTrigger className="w-[160px]" variant="ghost">
        <SelectValue placeholder="Choose preset" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="preset-1">Preset 1</SelectItem>
        <SelectItem value="preset-2">Preset 2</SelectItem>
        <SelectItem value="preset-3">Preset 3</SelectItem>
      </SelectContent>
    </Select>
  );
};

export default PresetSelect;
