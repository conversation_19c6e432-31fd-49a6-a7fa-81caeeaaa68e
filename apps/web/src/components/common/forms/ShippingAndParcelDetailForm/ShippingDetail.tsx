"use client";

import AddressInfo from "@/components/common/AddressInfo";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectSeparator,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cn } from "@/lib/utils";
import AddressSchema from "@/schemas/common/AddressSchema";
import { useGetAllAddressQuery } from "@/store/api/addressApi";
import styles from "@/styles/Dashboard.module.css";
import {
  filterPredefinedPackagesByServiceId,
  getAllCarriers,
  ServiceLevel,
} from "@repo/shared-data";
import { LoaderCircle } from "lucide-react";
import { useFormContext, useWatch } from "react-hook-form";
// import { Calendar } from "@/components/ui/calendar";
// import {
//   Popover,
//   PopoverContent,
//   PopoverTrigger,
// } from "@/components/ui/popover";
// import CalendarIcon from "@heroicons/react/24/outline/CalendarIcon";
// import { format } from "date-fns"

const ShippingDetail = () => {
  const form = useFormContext();

  const toAddress = useWatch({ name: "toAddress" });
  const carrier = useWatch({ name: "carrier" });
  const service = useWatch({ name: "service" });

  const { success: toAddressIsFilled } = AddressSchema.safeParse(toAddress);

  const { data: addresses, isLoading: isLoadingAddress } =
    useGetAllAddressQuery();

  return (
    <div className={cn(styles.formGrid)}>
      <FormField
        control={form.control}
        name="fromAddressId"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Ship From</FormLabel>
            <Select
              onValueChange={field.onChange}
              value={field.value}
              disabled={isLoadingAddress}
            >
              <FormControl>
                <SelectTrigger className="w-full">
                  <SelectValue
                    placeholder={
                      isLoadingAddress ? (
                        <>
                          <LoaderCircle className="size-4 animate-spin" />{" "}
                          Loading...
                        </>
                      ) : (
                        "Select a location"
                      )
                    }
                  />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {addresses?.map(address => (
                  <SelectItem key={address.id} value={address.id}>
                    {address.name}{" "}
                    {address.company ? `- ${address.company}` : ""}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />

      <div className="sm:row-span-2">
        <div className="space-y-2">
          <Label>Ship To</Label>
          {toAddressIsFilled ? (
            <AddressInfo
              name={toAddress.name}
              company={toAddress.company}
              street1={toAddress.street1}
              street2={toAddress.street2}
              city={toAddress.city}
              state={toAddress.state}
              zip={toAddress.zip}
              country={toAddress.country}
              phone={toAddress.phone}
              email={toAddress.email}
              residential={toAddress.residential}
            />
          ) : (
            <p className="text-muted-foreground/50 text-xs py-2">
              Customer address is not yet specified.
            </p>
          )}
        </div>
      </div>
      <div>
        {/* <FormField
        control={form.control}
        name="shipDate"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Ship Date</FormLabel>
            <Popover>
              <PopoverTrigger asChild>
                <FormControl>
                  <Button
                    variant={"outline"}
                    className={cn(
                      "w-full pl-3 text-left font-normal border-input text-popover-foreground",
                      !field.value && "text-muted-foreground"
                    )}
                  >
                    {field.value ? (
                      format(field.value, "dd MMM yyyy")
                    ) : (
                      <span>Pick a date</span>
                    )}
                    <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                  </Button>
                </FormControl>
              </PopoverTrigger>
              <PopoverContent className="w-auto !p-0" align="start">
                <Calendar
                  mode="single"
                  selected={field.value}
                  onSelect={field.onChange}
                  captionLayout="dropdown"
                />
              </PopoverContent>
            </Popover>

            <FormMessage />
          </FormItem>
        )}
      /> */}
      </div>

      <FormField
        control={form.control}
        name="service"
        render={({ field }) => {
          const value =
            carrier && field.value ? `${carrier} - ${field.value}` : "";
          return (
            <FormItem>
              <FormLabel>Service</FormLabel>
              <Select
                onValueChange={value => {
                  form.setValue("parcel.predefinedPackage", null);
                  const [carrier, service] = value.split(" - ");
                  form.setValue("carrier", carrier);
                  field.onChange(service);
                }}
                value={value ?? ""}
              >
                <FormControl>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select service level" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {getAllCarriers().map(c => (
                    <SelectGroup key={c.id}>
                      <SelectLabel>{c.shortName}</SelectLabel>
                      {c.serviceLevels.map((s: ServiceLevel) => (
                        <SelectItem
                          key={c.id + s.id}
                          value={`${c.id} - ${s.id}`}
                        >
                          {s.label}
                        </SelectItem>
                      ))}
                    </SelectGroup>
                  ))}
                  <SelectSeparator />
                  <Button
                    variant="ghost-destructive"
                    size="sm"
                    className="w-full"
                    onClick={() => {
                      form.setValue("carrier", null);
                      field.onChange(null);
                    }}
                  >
                    Clear
                  </Button>
                </SelectContent>
              </Select>
            </FormItem>
          );
        }}
      />

      <FormField
        control={form.control}
        name="parcel.predefinedPackage"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Package Type</FormLabel>
            <Select onValueChange={field.onChange} value={field.value ?? ""}>
              <FormControl>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select package type" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {filterPredefinedPackagesByServiceId(service ?? "").map(c => (
                  <SelectGroup key={c.id}>
                    <SelectLabel>{c.shortName}</SelectLabel>
                    {c.predefinedPackages.map(p => (
                      <SelectItem key={p.id} value={p.id}>
                        {p.label}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                ))}
                <SelectSeparator />
                <Button
                  variant="ghost-destructive"
                  size="sm"
                  className="w-full"
                  onClick={() => field.onChange(null)}
                >
                  Clear
                </Button>
              </SelectContent>
            </Select>
          </FormItem>
        )}
      />
    </div>
  );
};

export default ShippingDetail;
