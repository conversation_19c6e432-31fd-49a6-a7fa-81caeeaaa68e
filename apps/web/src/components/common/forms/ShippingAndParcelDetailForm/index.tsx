import UnitDisplay from "@/components/parcel/UnitDisplay";
import Header from "@/components/layout/Header";
import { cn } from "@/lib/utils";
import styles from "@/styles/Dashboard.module.css";
import { PencilRuler, Truck } from "lucide-react";
import ParcelDetail from "./ParcelDetail";
import PresetSelect from "./PresetSelect";
import ShippingDetail from "./ShippingDetail";

const ShippingAndParcelDetailForm = () => {
  return (
    <div className={cn(styles.panelContainer, styles.panelYSpacing)}>
      {/* Shipping Detail */}
      <div className={styles.panelYSpacing}>
        <div className="flex justify-between items-center">
          <Header icon={Truck}>Shipping Detail</Header>
          <PresetSelect />
        </div>
        <ShippingDetail />
      </div>

      {/* Parcel Detail */}
      <div className={cn(styles.panelYSpacing)}>
        <div className="flex flex-col sm:flex-row justify-between items-baseline gap-2">
          <Header icon={PencilRuler}>Parcel Detail</Header>
          <UnitDisplay />
        </div>
        <ParcelDetail />
      </div>
    </div>
  );
};

export default ShippingAndParcelDetailForm;
