import Header from "@/components/layout/Header";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";
import AddressSchema, { defaultValues } from "@/schemas/common/AddressSchema";
import styles from "@/styles/Dashboard.module.css";
import { zodResolver } from "@hookform/resolvers/zod";
import { BookUser, LoaderCircle } from "lucide-react";
import { memo } from "react";
import { Form<PERSON>rovider, useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

type AddressFormModalProps = {
  values?: z.infer<typeof AddressSchema>;
  isOpen: boolean;
  setOpen: (open: boolean) => void;
  onSubmit: (values: z.infer<typeof AddressSchema>) => void;
  isLoading?: boolean;
  header?: string;
};

const AddressFormModal = ({
  values,
  isOpen,
  setOpen,
  onSubmit,
  isLoading = false,
  header = "Address",
}: AddressFormModalProps) => {
  const form = useForm<z.infer<typeof AddressSchema>>({
    resolver: zodResolver(AddressSchema),
    defaultValues: values ?? defaultValues,
  });

  const { isDirty } = form.formState;

  const handleSubmit = (values: z.infer<typeof AddressSchema>) => {
    if (!isDirty) {
      setOpen(false);
      return;
    }

    onSubmit(values);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setOpen}>
      <DialogContent className="md:min-w-2xl xl:gap-y-5">
        <DialogTitle className="hidden">Customer Address</DialogTitle>
        <DialogDescription className="hidden">
          Form to add customer address.
        </DialogDescription>
        <DialogHeader>
          <Header icon={BookUser} as="h3">
            {header}
          </Header>
        </DialogHeader>
        <Separator />
        <FormProvider {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-4 xl:space-y-5"
          >
            <ContactInformation />
            <Separator />
            <AddressInformation />
            <Separator />
            <DialogFooter>
              <DialogClose asChild>
                <Button type="button" variant="ghost-destructive">
                  Cancel
                </Button>
              </DialogClose>
              <Button type="submit" disabled={isLoading}>
                {isLoading && <LoaderCircle className="size-4 animate-spin" />}
                Save Changes
              </Button>
            </DialogFooter>
          </form>
        </FormProvider>
      </DialogContent>
    </Dialog>
  );
};

const ContactInformation = () => {
  const { control } = useFormContext();
  return (
    <div className="space-y-4">
      <h2 className="font-semibold">Contact Information</h2>
      <div className={cn(styles.formGrid)}>
        <FormField
          control={control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Name</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={control}
          name="company"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Company</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={control}
          name="phone"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Phone No.</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
};

const AddressInformation = memo(() => {
  const { control } = useFormContext();
  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <h2 className="font-semibold">Address Information</h2>
      </div>
      <div className={cn(styles.formGrid)}>
        <FormField
          control={control}
          name="street1"
          render={({ field }) => (
            <FormItem className={styles.colSpan2}>
              <FormLabel>Street 1</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={control}
          name="street2"
          render={({ field }) => (
            <FormItem className={styles.colSpan2}>
              <FormLabel>Street 2</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={control}
          name="city"
          render={({ field }) => (
            <FormItem>
              <FormLabel>City</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={control}
          name="state"
          render={({ field }) => (
            <FormItem>
              <FormLabel>State</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={control}
          name="country"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Country</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger className="w-full">
                    <SelectValue />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="US">United States</SelectItem>
                  <SelectItem value="CA">Canada</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={control}
          name="zip"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Postal Code</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={control}
          name="residential"
          render={({ field }) => (
            <FormItem className="flex items-center space-x-1 px-4 py-3 border bg-muted rounded-sm">
              <FormControl>
                <Checkbox
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
              <FormLabel className="font-medium">Is residential</FormLabel>
            </FormItem>
          )}
        />
      </div>
    </div>
  );
});

AddressInformation.displayName = "AddressInformation";

export default AddressFormModal;
