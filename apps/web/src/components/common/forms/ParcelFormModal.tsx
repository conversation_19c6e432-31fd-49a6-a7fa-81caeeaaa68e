"use client";

import WeightInputs from "@/components/parcel/WeightInputs";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { UNIT_SYSTEMS } from "@/config/unitSystem";
import { cn } from "@/lib/utils";
import ParcelSchema, { defaultValues } from "@/schemas/settings/ParcelSchema";
import customStyles from "@/styles/Custom.module.css";
import styles from "@/styles/Dashboard.module.css";
import { zodResolver } from "@hookform/resolvers/zod";
import { Parcel } from "@repo/database";
import { Loader<PERSON>ir<PERSON>, PencilRuler, Settings } from "lucide-react";
import { Dispatch, SetStateAction } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import Header from "@/components/layout/Header";

type ParcelFormModalProps = {
  open: boolean;
  selectedParcel?: Parcel | null;
  submitButtonText?: string;
  isSubmitting: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  onClose: () => void;
  onSubmit: (values: z.infer<typeof ParcelSchema>) => Promise<void>;
};

const ParcelFormModal = ({
  open,
  selectedParcel,
  submitButtonText = "Save changes",
  isSubmitting,
  setOpen,
  onClose,
  onSubmit,
}: ParcelFormModalProps) => {
  const form = useForm<z.infer<typeof ParcelSchema>>({
    resolver: zodResolver(ParcelSchema),
    defaultValues: selectedParcel
      ? {
          name: selectedParcel.name,
          length: selectedParcel.length ?? undefined,
          width: selectedParcel.width ?? undefined,
          height: selectedParcel.height ?? undefined,
          weight: selectedParcel.weight ?? undefined,
          predefinedPackage: selectedParcel.predefinedPackage ?? undefined,
        }
      : defaultValues,
  });

  const handleNumberChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    onChange: (value: number) => void
  ) => {
    const value = e.target.value;
    // Only allow one decimal point and one digit after it
    if (
      /^\d*\.?\d{0,1}$/.test(value) &&
      (value.match(/\./g) || []).length <= 1
    ) {
      onChange(Number(value));
    }
  };

  const handleSubmit = async (values: z.infer<typeof ParcelSchema>) => {
    await onSubmit(values);
    form.reset();
  };

  return (
    <Dialog
      open={open}
      onOpenChange={() =>
        setOpen(prev => {
          if (prev) {
            form.reset();
            onClose();
          }
          return !prev;
        })
      }
    >
      <DialogContent className="md:min-w-2xl xl:gap-y-5">
        <DialogTitle className="hidden">Parcel Type</DialogTitle>
        <DialogDescription className="hidden">
          Form to add customer address.
        </DialogDescription>
        <DialogHeader>
          <Header icon={PencilRuler} as="h3">
            Parcel Type
          </Header>
        </DialogHeader>
        <Separator />
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-4 xl:space-y-5"
          >
            <div className={cn(styles.formGrid, "items-end")}>
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem className="relative">
                    <FormLabel>Name</FormLabel>

                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="flex gap-4 text-sm items-center justify-end">
                <div>
                  Dimensions:{" "}
                  <span className="font-semibold">
                    {UNIT_SYSTEMS.imperial.dimension.shortLabel}
                  </span>
                </div>
                <div>
                  Weight:{" "}
                  <span className="font-semibold">
                    {UNIT_SYSTEMS.imperial.weight.shortLabel}
                  </span>
                </div>
                <Button type="button" size="xs" variant="muted">
                  <Settings />
                </Button>
              </div>
            </div>
            <div className="space-y-4">
              <div className="grid sm:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="length"
                  render={({ field }) => (
                    <FormItem className="relative">
                      <FormLabel>Length</FormLabel>
                      <div className="relative">
                        <FormControl>
                          <Input
                            type="number"
                            step="0.1"
                            min={0}
                            className={cn(
                              customStyles.inputNoArrows,
                              "text-right pr-8"
                            )}
                            {...field}
                            onChange={e =>
                              handleNumberChange(e, field.onChange)
                            }
                          />
                        </FormControl>
                        <span className="text-muted-foreground text-xs absolute right-3.5 top-1/2 -translate-y-1/2">
                          {UNIT_SYSTEMS.imperial.dimension.shortLabel}
                        </span>
                      </div>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="width"
                  render={({ field }) => (
                    <FormItem className="relative">
                      <FormLabel>Width</FormLabel>
                      <div className="relative">
                        <FormControl>
                          <Input
                            type="number"
                            step="0.1"
                            min={0}
                            className={cn(
                              customStyles.inputNoArrows,
                              "text-right pr-8"
                            )}
                            {...field}
                            onChange={e =>
                              handleNumberChange(e, field.onChange)
                            }
                          />
                        </FormControl>
                        <span className="text-muted-foreground text-xs absolute right-3.5 top-1/2 -translate-y-1/2">
                          {UNIT_SYSTEMS.imperial.dimension.shortLabel}
                        </span>
                      </div>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="height"
                  render={({ field }) => (
                    <FormItem className="relative">
                      <FormLabel>Height</FormLabel>
                      <div className="relative">
                        <FormControl>
                          <Input
                            type="number"
                            step="0.1"
                            min={0}
                            className={cn(
                              customStyles.inputNoArrows,
                              "text-right pr-8"
                            )}
                            {...field}
                            onChange={e =>
                              handleNumberChange(e, field.onChange)
                            }
                          />
                        </FormControl>
                        <span className="text-muted-foreground text-xs absolute right-3.5 top-1/2 -translate-y-1/2">
                          {UNIT_SYSTEMS.imperial.dimension.shortLabel}
                        </span>
                      </div>
                    </FormItem>
                  )}
                />
              </div>
              <WeightInputs
                weight={form.getValues("weight")}
                onWeightChange={value => {
                  form.setValue("weight", value);
                }}
              />
            </div>
            <Separator />
            <DialogFooter>
              <DialogClose asChild>
                <Button type="button" variant="ghost-destructive">
                  Cancel
                </Button>
              </DialogClose>

              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting && (
                  <LoaderCircle className="size-4 animate-spin" />
                )}
                {submitButtonText}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default ParcelFormModal;
