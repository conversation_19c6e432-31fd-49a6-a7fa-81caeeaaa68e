import { Checkbox } from "@/components/ui/checkbox";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import customStyles from "@/styles/Custom.module.css";
import { InformationCircleIcon } from "@heroicons/react/24/solid";
import { useState } from "react";
import { useFormContext, useWatch } from "react-hook-form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const ShippingOptionsForm = () => {
  const insurance = useWatch({ name: "insurance" });

  const [showInsuranceInput, setShowInsuranceInput] = useState(!!insurance);

  const form = useFormContext();

  const handleInsuranceCheckboxChange = (checked: boolean) => {
    if (!checked) form.setValue("insurance", 0);
    setShowInsuranceInput(checked);
  };

  return (
    <div className="space-y-4 md:space-y-6">
      <div className="space-y-4 px-3">
        <div className="flex items-center space-x-2">
          <Checkbox
            id="showInsurance"
            checked={showInsuranceInput}
            onCheckedChange={handleInsuranceCheckboxChange}
          />
          <Label htmlFor="showInsurance">
            Require insurance{" "}
            <InformationCircleIcon className="size-4 text-muted-foreground" />
          </Label>
        </div>
        {showInsuranceInput && (
          <FormField
            control={form.control}
            name="insurance"
            render={({ field }) => (
              <FormItem className="ml-6">
                <FormLabel>Insured Amount</FormLabel>
                <div className="relative">
                  <FormControl className="relative">
                    <Input
                      type="number"
                      step="0.01"
                      {...field}
                      className={cn(
                        customStyles.inputNoArrows,
                        "pl-6 text-right"
                      )}
                      onChange={e => field.onChange(Number(e.target.value))}
                    />
                  </FormControl>
                  <span className="text-muted-foreground absolute left-3 top-1/2 -translate-y-1/2">
                    $
                  </span>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
        )}
      </div>
      <FormField
        control={form.control}
        name="signature"
        render={({ field }) => (
          <FormItem className="px-3">
            <FormLabel>Signature Confirmation</FormLabel>
            <Select onValueChange={field.onChange} value={field.value ?? ""}>
              <FormControl>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder={"Select Signature Confirmation"} />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                <SelectItem value={"A"}>A</SelectItem>
                <SelectItem value={"B"}>B</SelectItem>
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
};

export default ShippingOptionsForm;
