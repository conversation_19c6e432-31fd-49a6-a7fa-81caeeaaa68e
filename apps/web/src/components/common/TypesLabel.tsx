import { snakeCaseToText } from "@/lib/utils/strings";
import { WalletTransactionType } from "@repo/database";
import { Badge } from "../ui/badge";
import { getTransactionConfig } from "@/lib/mappings/transactionTypeColor";
import { cn } from "@/lib/utils";

type TypeProps = {
  text: WalletTransactionType;
  className?: string;
};

export const TypesLabel = ({ text, className = "" }: TypeProps) => {
  const { badge } = getTransactionConfig(text);

  return (
    <Badge variant={badge} className={className}>
      {snakeCaseToText(text)}
    </Badge>
  );
};

type AmountProps = {
  type: WalletTransactionType;
  value: string;
};

export const TextAmount = ({ type, value }: AmountProps) => {
  const { text: colorClass, isNegative } = getTransactionConfig(type);

  return (
    <div className={cn(colorClass, "font-medium")}>
      {isNegative ? `-$${value.split("-").join("")}` : `+$${value}`}
    </div>
  );
};
