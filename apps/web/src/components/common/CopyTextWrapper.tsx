"use client";

import { useEffect, useState } from "react";
import { toast } from "sonner";

type CopyTextWrapperProps = {
  text: string;
  children: React.ReactNode;
  isTable?: boolean;
};

const CopyTextWrapper = ({
  text,
  children,
  isTable = false,
}: CopyTextWrapperProps) => {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      if (isTable)
        toast.success("Copied", {
          className: "!w-[150px]",
        });
    } catch (error) {
      console.log(`Error copying text: ${error}`);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" || e.key === " ") {
      e.preventDefault();
      handleCopy();
    }
  };

  useEffect(() => {
    if (copied) {
      const timer = setTimeout(() => setCopied(false), 2000);
      return () => clearTimeout(timer);
    }
  }, [copied]);

  return (
    <div
      className="relative"
      role="button"
      tabIndex={0}
      aria-label={`Copy "${text}" to clipboard`}
      onClick={handleCopy}
      onKeyDown={handleKeyDown}
    >
      {children}
      {!isTable && copied && (
        <>
          <div
            role="status"
            aria-live="polite"
            className="absolute -top-7 right-1/2 translate-x-1/2 bg-accent text-white text-xs px-2 py-1 rounded-md"
          >
            Copied!
          </div>
          <div className="absolute left-1/2 -translate-x-1/2 -mt-7 w-0 h-0 border-l-[5px] border-l-transparent border-t-[5px] border-t-accent border-r-[5px] border-r-transparent"></div>
        </>
      )}
    </div>
  );
};

export default CopyTextWrapper;
