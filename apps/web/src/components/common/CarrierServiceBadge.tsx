import Image from "next/image";
import { getCarrierIcon } from "@/lib/mappings/carriers";
import { getCarrierBasicInfo, getServiceLevel } from "@repo/shared-data";

type CarrierServiceBadgeProps = {
  carrierId: string;
  serviceId: string;
};

const CarrierServiceBadge = ({
  carrierId,
  serviceId,
}: CarrierServiceBadgeProps) => {
  const carrier = getCarrierBasicInfo(carrierId);
  const service = getServiceLevel(carrierId, serviceId);
  const carrierIcon = getCarrierIcon(carrierId);

  return (
    <div className="flex flex-col gap-0.5">
      <div className="flex items-center justify-start gap-1">
        <Image
          src={carrierIcon}
          alt={carrier.fullName}
          className="h-4 w-auto mr-auto max-w-20"
        />
        <span className="text-xs grow-1 font-semibold">
          {carrier.shortName}
        </span>
      </div>
      <span className="text-xs text-muted-foreground">{service.label}</span>
    </div>
  );
};

export default CarrierServiceBadge;
