"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { CalendarIcon, Search } from "lucide-react";
import { useState } from "react";
import { DateRange } from "react-day-picker";

type DateRangeSelectProps = {
  initialValue: DateRange;
  onApply: (range: DateRange) => void;
};

export default function DateRangeSelect({
  initialValue,
  onApply,
}: DateRangeSelectProps) {
  const [dateRange, setDateRange] = useState(initialValue);

  return (
    <div className="flex items-end space-x-2">
      <div className="flex flex-col w-full md:w-auto">
        <Label className="mb-2">From Date - To Date</Label>
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant={"outline"}
              className={cn(
                "w-full md:w-[230px] pl-3 text-left font-normal text-accent !border-input"
              )}
            >
              {dateRange?.from && dateRange?.to ? (
                <>
                  {format(dateRange.from, "dd/MM/yyyy")} -{" "}
                  {format(dateRange.to, "dd/MM/yyyy")}
                </>
              ) : (
                <span>Select a date range</span>
              )}
              <CalendarIcon className="ml-auto text-accent size-3.5" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto !p-0" align="start">
            <Calendar
              mode="range"
              selected={dateRange}
              onSelect={setDateRange}
              disabled={date =>
                date > new Date() || date < new Date("1900-01-01")
              }
              captionLayout="dropdown"
              required
            />
          </PopoverContent>
        </Popover>
      </div>
      <Button onClick={() => onApply(dateRange)} size="icon">
        <Search />
      </Button>
    </div>
  );
}
