"use client";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { cn } from "@/lib/utils";
import { flexRender, Row, Table as TableType } from "@tanstack/react-table";
import { memo } from "react";
import Pagination from "./Pagination";

interface DataTableProps<TData> {
  table: TableType<TData>;
  variant?: "default" | "dark";
  showPagination?: boolean;
  rowColor?: string | ((row: Row<TData>) => string);
  pageSizeOptions?: number[];
  height?: string;
}

const DataTable = <TData,>({
  table,
  variant = "default",
  showPagination = true,
  rowColor,
  pageSizeOptions = [25, 50, 75, 100],
  height,
}: DataTableProps<TData>) => {
  const getRowClassName = (row: Row<TData>) => {
    if (!rowColor) return "";
    if (typeof rowColor === "string") {
      return rowColor;
    }
    return rowColor(row);
  };

  return (
    <div className={cn("rounded-lg border overflow-hidden")}>
      <Table variant={variant} height={height}>
        <TableHeader>
          {table.getHeaderGroups().map(headerGroup => (
            <TableRow key={headerGroup.id}>
              {headerGroup.headers.map(header => (
                <TableHead key={header.id}>
                  {header.isPlaceholder
                    ? null
                    : flexRender(
                        header.column.columnDef.header,
                        header.getContext()
                      )}
                </TableHead>
              ))}
            </TableRow>
          ))}
        </TableHeader>
        <TableBody>
          {table.getRowModel().rows?.length ? (
            table
              .getRowModel()
              .rows.map(row => (
                <MemoizedTableRow
                  key={row.id}
                  row={row}
                  rowClassName={getRowClassName(row)}
                  selected={row.getIsSelected()}
                />
              ))
          ) : (
            <TableRow className="border-none">
              <TableCell
                colSpan={table.getAllColumns().length}
                className="h-24 text-center text-muted-foreground/70"
              >
                No results.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>

      {showPagination && (
        <Pagination
          table={table}
          variant={variant}
          pageSizeOptions={pageSizeOptions}
        />
      )}
    </div>
  );
};

const TableRowComponent = <TData,>({
  row,
  rowClassName,
  selected,
}: {
  row: Row<TData>;
  rowClassName: string;
  selected: boolean;
}) => {
  return (
    <TableRow
      key={row.id}
      data-state={selected && "selected"}
      className={cn(rowClassName)}
    >
      {row.getVisibleCells().map(cell => (
        <TableCell key={cell.id}>
          {flexRender(cell.column.columnDef.cell, cell.getContext())}
        </TableCell>
      ))}
    </TableRow>
  );
};

const MemoizedTableRow = memo(TableRowComponent) as typeof TableRowComponent;

// MemoizedTableRow.displayName = "TableRowComponent";

export default DataTable;
