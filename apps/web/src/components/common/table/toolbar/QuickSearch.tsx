import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";
import { useState } from "react";

type QuickSearchProps = {
  setSearch: (search: string) => void;
};

const QuickSearch = ({ setSearch }: QuickSearchProps) => {
  const submitSearch = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setSearch(inputSearch.trim());
  };
  const [inputSearch, setInputSearch] = useState("");
  return (
    <form onSubmit={submitSearch} className="flex items-center gap-1.5">
      <Input
        placeholder="Quick search..."
        value={inputSearch}
        onChange={e => setInputSearch(e.target.value)}
        className="w-[200px]"
      />
      <Button type="submit" size="icon">
        <Search />
      </Button>
    </form>
  );
};

export default QuickSearch;
