import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cn } from "@/lib/utils";
import customStyles from "@/styles/Custom.module.css";
import { Table } from "@tanstack/react-table";
import {
  ChevronFirst,
  ChevronLast,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import { useEffect, useState } from "react";

interface PaginationProps<TData> {
  table: Table<TData>;
  pageSizeOptions: number[];
  variant?: "default" | "dark";
}

function Pagination<TData>({
  table,
  pageSizeOptions,
  variant = "default",
}: PaginationProps<TData>) {
  const [currentPage, setCurrentPage] = useState(
    table.getState().pagination.pageIndex + 1
  );

  const pageIndex = table.getState().pagination.pageIndex + 1;

  useEffect(() => {
    setCurrentPage(pageIndex);
  }, [pageIndex]);

  const handleBlur = () => {
    let newPage: number;

    if (currentPage < 1) newPage = 1;
    else if (currentPage > table.getPageCount()) newPage = table.getPageCount();
    else newPage = currentPage;

    setCurrentPage(newPage);

    table.setPageIndex(newPage - 1);
  };

  const handleFirstPage = () => {
    table.firstPage();
    setCurrentPage(1);
  };

  const handlePreviousPage = () => {
    table.previousPage();
    setCurrentPage(prev => prev - 1);
  };

  const handleNextPage = () => {
    table.nextPage();
    setCurrentPage(prev => prev + 1);
  };

  const handleLastPage = () => {
    table.lastPage();
    setCurrentPage(table.getPageCount());
  };

  return (
    <div
      className={cn(
        "flex md:items-center justify-between px-2 border-t py-1.5",
        "flex-col md:flex-row",
        variant === "dark" && "bg-muted/60"
      )}
    >
      <div className="hidden md:flex flex-1 text-sm text-muted-foreground mb-2 md:mb-0">
        {table.getFilteredSelectedRowModel().rows.length} of{" "}
        {table.getFilteredRowModel().rows.length} row(s) selected.
      </div>
      <div className="flex flex-col md:flex-row md:items-center space-x-0 md:space-x-6 space-y-2 md:space-y-0 lg:space-x-8">
        <div className="flex items-center space-x-2">
          <Select
            value={`${table.getState().pagination.pageSize}`}
            onValueChange={value => {
              table.setPageSize(Number(value));
            }}
          >
            <SelectTrigger>
              <SelectValue placeholder={table.getState().pagination.pageSize} />
            </SelectTrigger>
            <SelectContent side="top">
              {pageSizeOptions.map(pageSize => (
                <SelectItem key={pageSize} value={`${pageSize}`}>
                  {pageSize}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <p className="text-xs">per page</p>
        </div>

        <div className="flex items-center space-x-2 justify-end">
          <Button
            variant="outline"
            className="h-8 w-8 p-0"
            onClick={handleFirstPage}
            disabled={!table.getCanPreviousPage()}
          >
            <span className="sr-only">Go to first page</span>
            <ChevronFirst />
          </Button>
          <Button
            variant="outline"
            className="h-8 w-8 p-0"
            onClick={handlePreviousPage}
            disabled={!table.getCanPreviousPage()}
          >
            <span className="sr-only">Go to previous page</span>
            <ChevronLeft />
          </Button>

          <form
            onSubmit={e => {
              e.preventDefault();
              handleBlur();
            }}
            className="flex items-center justify-center text-xs gap-1"
          >
            Page
            <Input
              type="number"
              value={currentPage}
              className={cn(
                "w-10 px-1 pb-1.5 text-center text-xs! rounded-lg",
                customStyles.inputNoArrows
              )}
              inputSize="sm"
              onChange={e => setCurrentPage(Number(e.target.value))}
              onBlur={handleBlur}
            />
            of {table.getPageCount()}
            <button className="sr-only">Go to page</button>
          </form>
          <Button
            variant="outline"
            className="h-8 w-8 p-0"
            onClick={handleNextPage}
            disabled={!table.getCanNextPage()}
          >
            <span className="sr-only">Go to next page</span>
            <ChevronRight />
          </Button>
          <Button
            variant="outline"
            className="h-8 w-8 p-0"
            onClick={handleLastPage}
            disabled={!table.getCanNextPage()}
          >
            <span className="sr-only">Go to last page</span>
            <ChevronLast />
          </Button>
        </div>
      </div>
    </div>
  );
}

export default Pagination;
