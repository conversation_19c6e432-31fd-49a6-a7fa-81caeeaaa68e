import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import { PhotoIcon } from "@heroicons/react/24/outline";
import { OrderItem, Product } from "@repo/database";
import Image from "next/image";

type OrderItemHoverCardProps = {
  orderItems: (OrderItem & { product: Product })[];
};

const OrderItemHoverCard = ({ orderItems }: OrderItemHoverCardProps) => {
  return (
    <HoverCard>
      <HoverCardTrigger>({orderItems?.length} items)</HoverCardTrigger>
      <HoverCardContent className="text-sm text-accent p-2">
        <ul className="flex flex-col gap-2">
          {orderItems.map(item => {
            return <ProductItem key={item.id} item={item} />;
          })}
        </ul>
      </HoverCardContent>
    </HoverCard>
  );
};

const ProductItem = ({ item }: { item: OrderItem & { product: Product } }) => {
  return (
    <li className="flex gap-2 overflow-hidden">
      <div className="relative aspect-square h-8 bg-muted rounded-xs grid place-items-center">
        {item.product.imageUrl ? (
          <Image
            src={item.product.imageUrl}
            alt={item.product.name}
            fill
            className="object-cover"
          />
        ) : (
          <PhotoIcon className="text-secondary size-4" />
        )}
      </div>
      <div className="flex-1 flex justify-between items-center px-1">
        <div>
          <div className="text-xs text-muted-foreground leading-3">
            {item.product.sku}
          </div>
          <div className="font-medium text-sm truncate">
            {item.product.name}
          </div>
        </div>

        <div className="text-right">
          <div className="text-[10px] text-muted-foreground leading-3">Qty</div>
          <div className="font-medium text-sm truncate">{item.quantity}</div>
        </div>
      </div>
    </li>
  );
};

export default OrderItemHoverCard;
