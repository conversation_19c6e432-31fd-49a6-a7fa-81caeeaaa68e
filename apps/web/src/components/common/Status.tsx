import {
  claimStatusColor,
  shipmentStatusColor,
  orderStatusColor,
  batchStatusColor,
  paymentStatusColor,
} from "@/lib/mappings/statusColor";
import { cn } from "@/lib/utils";
import { snakeCaseToText } from "@/lib/utils/strings";
import {
  BatchStatus,
  ClaimStatus,
  OrderStatus,
  PaymentStatus,
  ShipmentStatus,
} from "@repo/database";

type StatusProps = {
  text:
    | OrderStatus
    | BatchStatus
    | ShipmentStatus
    | ClaimStatus
    | PaymentStatus;
  className?: string;
};

const Status = ({ text, className = "" }: StatusProps) => {
  const getStatusColor = (status: string): string => {
    if (status in orderStatusColor) {
      return orderStatusColor[status as OrderStatus];
    }
    if (status in batchStatusColor) {
      return batchStatusColor[status as BatchStatus];
    }
    if (status in shipmentStatusColor) {
      return shipmentStatusColor[status as ShipmentStatus];
    }
    if (status in claimStatusColor) {
      return claimStatusColor[status as ClaimStatus];
    }
    if (status in paymentStatusColor) {
      return paymentStatusColor[status as PaymentStatus];
    }
    // Fallback for unknown statuses
    return "text-muted-foreground";
  };

  return (
    <div className={cn("text-sm", className, getStatusColor(text))}>
      {snakeCaseToText(text)}
    </div>
  );
};

export default Status;
