import { UNIT_SYSTEMS } from "@/config/unitSystem";

const UnitDisplay = () => {
  return (
    <div className="flex gap-4 text-sm items-center justify-end">
      <div>
        Dimensions:{" "}
        <span className="font-semibold">
          {UNIT_SYSTEMS.imperial.dimension.shortLabel}
        </span>
      </div>
      <div>
        Weight:{" "}
        <span className="font-semibold">
          {UNIT_SYSTEMS.imperial.weight.shortLabel}
        </span>
      </div>
      {/* <Button type="button" size="xs" variant="muted">
    <Settings />
  </Button> */}
    </div>
  );
};

export default UnitDisplay;
