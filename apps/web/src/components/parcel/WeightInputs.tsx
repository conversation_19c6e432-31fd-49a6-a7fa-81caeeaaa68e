import { FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { UNIT_SYSTEMS } from "@/config/unitSystem";
import { cn } from "@/lib/utils";
import { parseWeight, totalWeight } from "@/lib/utils/parcel";
import customStyles from "@/styles/Custom.module.css";
import { useState } from "react";
import { useFormContext } from "react-hook-form";

type WeightInputsProps = {
  weight: number;
  onWeightChange: (weight: number) => void;
};
const WeightInputs = ({ weight, onWeightChange }: WeightInputsProps) => {
  const form = useFormContext();
  const weightState = form.getFieldState("parcel.weight");

  const [formData, setFormData] = useState(parseWeight(weight));

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;
    let oz = formData.oz;
    let lb = formData.lb;

    if (id === "oz") {
      const regex = /^\d*(\.\d{0,1})?$/;
      if (!regex.test(value)) return;
      setFormData(prev => ({ ...prev, oz: Number(value) }));
      oz = Number(value);
    } else if (id === "lb") {
      const number = Number(value);
      const int = Math.round(number);
      setFormData(prev => ({ ...prev, lb: int }));
      lb = int;
    }

    onWeightChange(totalWeight(lb, oz));
  };

  return (
    <div>
      <div className="grid sm:grid-cols-2 gap-4 items-end">
        <div className="grid w-full items-center gap-2">
          <Label htmlFor="picture">Weight </Label>
          <div className="relative">
            <Input
              id="lb"
              type="number"
              min={0}
              className={cn(customStyles.inputNoArrows, "text-right pr-8")}
              value={formData.lb}
              aria-invalid={
                weightState.invalid &&
                totalWeight(formData.lb, formData.oz) === 0
              }
              onChange={handleChange}
            />
            <span className="text-muted-foreground text-xs absolute right-3.5 top-1/2 -translate-y-1/2">
              {UNIT_SYSTEMS.imperial.weight.largeUnit.shortLabel}
            </span>
          </div>
        </div>
        <div className="grid w-full items-center gap-2">
          <div className="relative">
            <Input
              id="oz"
              type="number"
              step="0.1"
              min={0}
              className={cn(customStyles.inputNoArrows, "text-right pr-8")}
              value={formData.oz}
              aria-invalid={
                weightState.invalid &&
                totalWeight(formData.lb, formData.oz) === 0
              }
              onChange={handleChange}
            />
            <span className="text-muted-foreground text-xs absolute right-3.5 top-1/2 -translate-y-1/2">
              {UNIT_SYSTEMS.imperial.weight.shortLabel}
            </span>
          </div>
        </div>
      </div>
      {weightState.error && totalWeight(formData.lb, formData.oz) === 0 && (
        <FormMessage className="mt-2 px-1 text-right">
          {weightState.error.message}
        </FormMessage>
      )}
    </div>
  );
};

export default WeightInputs;
