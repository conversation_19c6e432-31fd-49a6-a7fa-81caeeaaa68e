"use client";

import { Input } from "@/components/ui/input";
import { getCarrierIcon } from "@/lib/mappings/carriers";
import { cn } from "@/lib/utils";
import { useGetRatesQuery } from "@/store/api/rateApi";
import { skipToken } from "@reduxjs/toolkit/query";
import { Rate } from "@repo/easypost-types";
import { getCarrierBasicInfo, getServiceLevel } from "@repo/shared-data";
import { Loader2 } from "lucide-react";
import Image from "next/image";
import { useState } from "react";

type RateListProps = {
  epShipmentId: string;
  selectedRate: Rate | null;
  onSelect: (rate: Rate) => void;
};

const RateList = ({ epShipmentId, selectedRate, onSelect }: RateListProps) => {
  const [search, setSearch] = useState("");

  const {
    data: rates,
    isLoading,
    isFetching,
  } = useGetRatesQuery(epShipmentId ?? skipToken);

  if (isLoading || isFetching)
    return <Loader2 className="animate-spin mx-auto size-6 text-primary" />;

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Input
          placeholder="Search carrier or service..."
          value={search}
          onChange={e => setSearch(e.target.value.trim())}
        />
      </div>
      <div className="border-y py-2">
        <div
          className={cn(
            //   customStyles.customScrollbar, TODO: use ScrollArea instead
            "space-y-2 max-h-[600px] overflow-y-auto pr-1"
          )}
        >
          {rates && rates.length > 0 ? (
            filterRates(rates, search).map(rate => (
              <RateItem
                key={rate.id}
                rate={rate}
                selected={selectedRate?.id === rate.id}
                onSelect={onSelect}
              />
            ))
          ) : (
            <div className="py-4 text-center text-muted-foreground/50 text-sm">
              No rates found
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

type RateItemProps = {
  rate: Rate;
  selected: boolean;
  onSelect: (rate: Rate) => void;
};

const RateItem = ({ rate, selected, onSelect }: RateItemProps) => {
  const carrier = getCarrierBasicInfo(rate.carrier);
  const service = getServiceLevel(rate.carrier, rate.service);
  const icon = getCarrierIcon(rate.carrier);

  return (
    <div
      className={cn(
        "flex justify-between items-baseline px-3 py-2 rounded-md hover:bg-secondary/30 transition-colors duration-300 cursor-pointer",
        selected && "bg-secondary/30 border border-accent"
      )}
      onClick={() => onSelect(rate)}
    >
      <div className="space-y-0.5">
        <div className="flex items-center gap-1.5">
          <Image
            src={icon}
            alt={carrier?.shortName || "icon"}
            className="h-5 w-auto"
          />
          <span className="text-sm capitalize font-medium">
            {carrier?.shortName || "undefined"}
          </span>
        </div>
        <div className="capitalize text-muted-foreground text-xs">
          {service?.label || "undefined"}
        </div>
      </div>
      <div className="flex flex-col items-end">
        <div className="font-semibold">${rate.rate}</div>
        <div className="text-xs text-muted-foreground leading-none">
          {rate.deliveryDays} day{rate.deliveryDays > 1 ? "s" : ""}
        </div>
      </div>
    </div>
  );
};

const filterRates = (rates: Rate[], search: string) => {
  return rates.filter(rate => {
    return (
      rate.carrier.toLowerCase().includes(search.toLowerCase()) ||
      rate.service.toLowerCase().includes(search.toLowerCase())
    );
  });
};

export default RateList;
