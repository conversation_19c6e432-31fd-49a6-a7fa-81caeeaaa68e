import Header from "@/components/layout/Header";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogTitle,
} from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import { useBalanceCheck } from "@/hooks/useBalanceCheck";
import clockGraphic from "@/public/graphics/clock.svg";
import labelsGraphic from "@/public/graphics/labels.svg";
import { accountApi } from "@/store/api/accountApi";
import { useAppDispatch } from "@/store/hooks";
import { updateWalletBalance } from "@/store/slices/accountSlice";
import { Return } from "@/types/Return/Return";
import { PostageLabelJson } from "@/types/Shipment/PostageLabelJson";
import { Shipment } from "@/types/Shipment/Shipment";
import { Rate } from "@repo/easypost-types";
import { AlertCircleIcon, Loader2, ScrollText, Wallet } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import React, { useMemo, useState } from "react";
import CarrierServiceCard from "../common/cards/CarrierServiceCard";
import { Alert, AlertDescription, AlertTitle } from "../ui/alert";
import { Skeleton } from "../ui/skeleton";

type BuyLabelModalProps = {
  open: boolean;
  setOpen: (open: boolean) => void;
  selectedRate: Rate | null;
  insuranceFee?: number;
  onSubmit: () => Promise<Return | Shipment | null>;
  isSubmitting: boolean;
};

type Status = "review" | "success" | "error";

const BuyLabelModal = ({
  open,
  setOpen,
  selectedRate,
  insuranceFee = 0,
  onSubmit,
  isSubmitting,
}: BuyLabelModalProps) => {
  const [status, setStatus] = useState<Status>("review");
  const [successData, setSuccessData] = useState<PostageLabelJson | null>(null);

  const handleClose = () => {
    setStatus("review");
    setOpen(false);
  };

  if (!selectedRate) return null;

  const renderComponent = () => {
    switch (status) {
      case "review":
        return (
          <Breakdown
            selectedRate={selectedRate}
            insuranceFee={insuranceFee}
            onClose={handleClose}
            setStatus={setStatus}
            onSubmit={onSubmit}
            isSubmitting={isSubmitting}
            setSuccessData={setSuccessData}
          />
        );
      case "success":
        return (
          <SuccessState postageLabel={successData} onClose={handleClose} />
        );

      default:
        return (
          <ErrorState
            onClose={handleClose}
            handleTryAgain={() => setStatus("review")}
          />
        );
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent
        onInteractOutside={e => e.preventDefault()}
        showCloseButton={false}
      >
        <DialogTitle className="sr-only">Buy Label</DialogTitle>
        <DialogDescription className="sr-only">
          Review label details and buy.
        </DialogDescription>
        <Header as="h3" icon={ScrollText}>
          Review Label Details
        </Header>
        <Separator />
        {renderComponent()}
      </DialogContent>
    </Dialog>
  );
};

const Breakdown = ({
  selectedRate,
  insuranceFee,
  onClose,
  setStatus,
  onSubmit,
  isSubmitting,
  setSuccessData,
}: {
  selectedRate: Rate;
  insuranceFee: number;
  onClose: () => void;
  setStatus: React.Dispatch<React.SetStateAction<Status>>;
  onSubmit: () => Promise<Return | Shipment | null>;
  isSubmitting: boolean;
  setSuccessData: React.Dispatch<React.SetStateAction<PostageLabelJson | null>>;
}) => {
  const dispatch = useAppDispatch();

  const { balance, checkSufficientBalance } = useBalanceCheck();

  const remainingBalance = useMemo(() => {
    const calculateRemainingBalance = (
      balance: string | undefined,
      rate: string
    ) => {
      if (!balance) return -1;

      const balanceNumber = parseFloat(balance);
      const rateNumber = parseFloat(rate);

      if (isNaN(balanceNumber) || isNaN(rateNumber)) {
        return -1; // default to negative value if invalid number
      }
      // Round to 2 decimal places for currency
      return Math.round((balanceNumber - rateNumber) * 100) / 100;
    };

    return calculateRemainingBalance(balance, selectedRate.rate);
  }, [balance, selectedRate.rate]);

  const renderRemainingBalanceComponent = () => {
    if (remainingBalance < 0) {
      return (
        <Alert variant="destructive">
          <AlertCircleIcon />
          <AlertTitle>Insufficient funds</AlertTitle>
          <AlertDescription>
            <p>Please add more funds to your wallet first. </p>
          </AlertDescription>
        </Alert>
      );
    }

    return (
      <div className="text-sm 2xl:text-base font-semibold flex justify-between items-center px-3 py-4 bg-secondary/50 rounded-md">
        <div>Remaining Balance</div>
        {balance ? (
          <div>${remainingBalance}</div>
        ) : (
          <Skeleton className="w-24 h-4 bg-secondary/80" />
        )}
      </div>
    );
  };

  const buyLabelAndCreate = async () => {
    const wallet = await checkSufficientBalance(parseFloat(selectedRate.rate));
    if (!wallet.sufficient) return;

    try {
      const result = await onSubmit();
      if (result) {
        setStatus("success");
        setSuccessData(result.postageLabel);

        // Update wallet balance
        const amount = parseFloat(result.rate);
        // Invalidate AccountApi's getWalletDashboard query
        dispatch(
          accountApi.util.updateQueryData(
            "getWalletDashboard",
            undefined,
            draft => {
              return {
                ...draft,
                wallet: {
                  ...draft.wallet,
                  balance: (
                    parseFloat(draft.wallet.balance) - amount
                  ).toString(),
                  availableBalance: (
                    parseFloat(draft.wallet.availableBalance) - amount
                  ).toString(),
                },
              };
            }
          )
        );
        // Update global account state
        dispatch(updateWalletBalance(-amount));
      } else setStatus("error");
    } catch (error) {
      if (process.env.NODE_ENV === "development") console.error(error);
      setStatus("error");
    }
  };

  if (isSubmitting) return <LoadingState />;

  return (
    <>
      <div className="space-y-4">
        <CarrierServiceCard
          carrierId={selectedRate.carrier}
          serviceId={selectedRate.service}
          rate={selectedRate.rate}
        />
        <div className="space-y-3 px-2">
          <h4 className="text-sm 2xl:text-base font-semibold">Summary</h4>
          <div className="text-sm space-y-2 font-medium">
            <div className="flex justify-between items-center">
              <div>Current Balance</div>
              {balance ? (
                <div>${balance}</div>
              ) : (
                <Skeleton className="h-3 w-20" />
              )}
            </div>

            <div className="flex justify-between items-center">
              <div>Label Fee</div>
              <div className="text-destructive">-${selectedRate.rate}</div>
            </div>

            {insuranceFee > 0 && (
              <div className="flex justify-between items-center">
                <div>Insurance Fee</div>
                <div className="text-destructive">
                  -
                  {insuranceFee.toLocaleString(undefined, {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                  })}
                </div>
              </div>
            )}
            {/* TODO: add signature confirmation cost */}
            <div className="flex justify-between items-center">
              <div>Signature Confirmation Fee</div>
              <div className="text-muted-foreground/50">-</div>
            </div>
          </div>
        </div>
        {balance ? (
          renderRemainingBalanceComponent()
        ) : (
          <Skeleton className="w-full h-80" />
        )}
      </div>
      <Separator />
      <DialogFooter>
        <Button
          type="button"
          variant="ghost-destructive"
          size="lg"
          onClick={onClose}
        >
          Cancel
        </Button>
        {remainingBalance >= 0 ? (
          <Button
            type="button"
            size="lg"
            className="min-w-36"
            onClick={buyLabelAndCreate}
          >
            Confirm
          </Button>
        ) : (
          <Button size="lg" asChild>
            <Link href="/wallet">
              <Wallet />
              Top-up Wallet
            </Link>
          </Button>
        )}
      </DialogFooter>
    </>
  );
};

const LoadingState = () => {
  return (
    <div className="p-4 grid place-content-center text-center min-h-68 gap-4">
      <h4 className="flex items-center justify-center gap-1 font-semibold">
        <Loader2 className="text-primary size-4 animate-spin" /> Processing...
      </h4>
      <Image src={clockGraphic} alt="clock" className="h-20 w-auto mx-auto" />
      <p className="text-muted-foreground text-sm">
        We are creating your label(s). <br /> Please wait...
      </p>
    </div>
  );
};

const SuccessState = ({
  postageLabel,
  onClose,
}: {
  postageLabel: PostageLabelJson | null;
  onClose: () => void;
}) => {
  console.log(postageLabel);
  return (
    <>
      <div className="p-4 grid place-content-center text-center min-h-60 gap-4">
        <h3 className="font-semibold lg:text-lg">Label Purchased</h3>
        <Image
          src={labelsGraphic}
          alt="labels"
          className="h-20 w-auto mx-auto"
        />
        <p className="text-muted-foreground text-sm">
          You can either print your labels now or later.
        </p>
      </div>
      <DialogFooter>
        <Button
          type="button"
          size="lg"
          variant="muted"
          className="w-1/2"
          onClick={onClose}
        >
          Print Later
        </Button>
        print modal here
        {/* <PrintModal
          postageLabel={postageLabel}
          id={epShipmentId}
          buttonSize="lg"
          buttonClassName="w-1/2"
        /> */}
      </DialogFooter>
    </>
  );
};

const ErrorState = ({
  onClose,
  handleTryAgain,
}: {
  onClose: () => void;
  handleTryAgain: () => void;
}) => {
  return (
    <div className="p-4 flex flex-col gap-4 justify-between min-h-60">
      <div className="space-y-4 text-center">
        <h3 className="font-semibold lg:text-lg">Could not purchase label</h3>
        <p className="text-muted-foreground text-sm">
          Please try again or contact support
        </p>
      </div>
      <DialogFooter>
        <Button
          type="button"
          variant="ghost-destructive"
          size="lg"
          onClick={onClose}
        >
          Cancel
        </Button>
        <Button onClick={handleTryAgain}>Try again</Button>
      </DialogFooter>
    </div>
  );
};

export default BuyLabelModal;
