import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";

type SkeletonTableProps = {
  className?: string;
  tableHeight?: string;
  advancedSearch?: boolean;
  viewOption?: boolean;
  statusSelect?: boolean;
};

const SkeletonTable = ({
  className,
  tableHeight = "h-[calc(100dvh-375px)]",
  advancedSearch = true,
  viewOption = true,
  statusSelect = true,
}: SkeletonTableProps) => {
  return (
    <div className={cn(className, "flex flex-col gap-4")}>
      <div className="flex md:justify-between md:items-center space-x-2 md:flex-row flex-col gap-4">
        {/* toolbar left */}
        <div className="flex gap-1.5">
          <Skeleton className="w-[200px] h-9 rounded-full" />
          <Skeleton className="size-9 rounded-full" />
        </div>
        {/* toolbar right */}
        <div className="flex space-x-2 self-end items-center">
          {advancedSearch && <Skeleton className="size-8 rounded-full" />}
          {viewOption && <Skeleton className="size-8 rounded-full" />}
          {statusSelect && <Skeleton className="w-[140px] h-9 rounded-full" />}
        </div>
      </div>
      <Skeleton className={cn(tableHeight, "grow-1 rounded-lg")} />
    </div>
  );
};

export default SkeletonTable;
