import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";
import OrderItemSchema, {
  defaultValues,
} from "@/schemas/order/OrderItemsSchema";
import customStyles from "@/styles/Custom.module.css";
import styles from "@/styles/Dashboard.module.css";
import { zodResolver } from "@hookform/resolvers/zod";
import { Package } from "lucide-react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import Header from "@/components/layout/Header";

type AddOrderItemFormModalProps = {
  isOpen: boolean;
  values?: z.infer<typeof OrderItemSchema>;
  setOpen: (open: boolean) => void;
  onSubmit: (values: z.infer<typeof OrderItemSchema>) => void;
  onClose?: () => void;
};

const AddOrderItemFormModal = ({
  isOpen,
  values,
  setOpen,
  onSubmit,
  onClose,
}: AddOrderItemFormModalProps) => {
  const form = useForm<z.infer<typeof OrderItemSchema>>({
    resolver: zodResolver(OrderItemSchema),
    values: values ?? defaultValues,
  });
  const { control, reset } = form;

  const handleSubmit = (values: z.infer<typeof OrderItemSchema>) => {
    onSubmit(values);
    reset();
    setOpen(false);
  };

  const handleOpenChange = (value: boolean) => {
    if (!value) {
      onClose?.();
      reset();
    }
    setOpen(value);
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent
        className="xl:gap-y-5 sm:w-11/12 sm:max-w-2xl"
        onInteractOutside={e => {
          e.preventDefault();
        }}
      >
        <DialogTitle className="sr-only">Add Item</DialogTitle>
        <DialogDescription className="sr-only">
          Add an item to the order.
        </DialogDescription>
        <DialogHeader>
          <Header icon={Package} variant="section" as="h3">
            Add Item
          </Header>
        </DialogHeader>
        <Separator />
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="flex flex-col gap-y-4 xl:gap-y-5"
          >
            <div>
              <div className={cn(styles.formGrid)}>
                <FormField
                  control={control}
                  name="product.sku"
                  render={({ field }) => {
                    return (
                      <FormItem>
                        <FormLabel>SKU</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    );
                  }}
                />
                <FormField
                  control={control}
                  name="product.name"
                  render={({ field }) => (
                    <FormItem className={styles.colSpan2}>
                      <FormLabel>Name</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={control}
                  name="price"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Price</FormLabel>
                      <div className="relative">
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            min={0}
                            className={cn(
                              customStyles.inputNoArrows,
                              "text-right pl-6"
                            )}
                            {...field}
                            onChange={(
                              e: React.ChangeEvent<HTMLInputElement>
                            ) => {
                              const value = Number(e.target.value);
                              field.onChange(value);
                            }}
                          />
                        </FormControl>
                        <span className="absolute left-3 top-1/2 -translate-y-1/2 text-xs text-muted-foreground">
                          $
                        </span>
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={control}
                  name="quantity"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Quantity</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min={0}
                          className={cn(
                            customStyles.inputNoArrows,
                            "text-right"
                          )}
                          {...field}
                          onChange={(
                            e: React.ChangeEvent<HTMLInputElement>
                          ) => {
                            const value = Number(e.target.value);
                            field.onChange(value);
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
            <Separator />
            <DialogFooter>
              <DialogClose asChild>
                <Button type="button" variant="ghost-destructive">
                  Cancel
                </Button>
              </DialogClose>
              <Button type="submit">Save Changes</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default AddOrderItemFormModal;
