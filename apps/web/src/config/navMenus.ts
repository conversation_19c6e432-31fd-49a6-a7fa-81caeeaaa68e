import ClaimIcon from "@/components/icons/ClaimIcon";
import {
  ArrowUturnDownIcon,
  ClipboardDocumentListIcon,
} from "@heroicons/react/24/outline";
import {
  Blocks,
  CircleHelp,
  ClipboardList,
  ClipboardMinus,
  CreditCard,
  Home,
  Package,
  PanelsTopLeft,
  Settings,
  Store,
  Truck,
  UsersRound,
  Wallet,
} from "lucide-react";

export type MenuItem = {
  label: string;
  href?: string;
  icon: React.ElementType;
  subMenus?: MenuItem[];
};

export const dashboardMenus: MenuItem[] = [
  {
    label: "Home",
    href: "/dashboard",
    icon: Home,
  },
  {
    label: "Orders",
    icon: ClipboardList,
    subMenus: [
      {
        label: "Overview",
        href: "/orders",
        icon: PanelsTopLeft,
      },
      {
        label: "Batches",
        href: "/orders/batches",
        icon: ClipboardDocumentListIcon,
      },
    ],
  },
  {
    label: "Shipments",
    icon: Truck,
    subMenus: [
      {
        label: "Overview",
        href: "/shipments",
        icon: PanelsTopLeft,
      },
      {
        label: "Returns",
        href: "/shipments/returns",
        icon: ArrowUturnDownIcon,
      },
    ],
  },
  {
    label: "Claims",
    href: "/claims",
    icon: ClaimIcon,
  },
];

export const adminMenus: MenuItem[] = [
  {
    label: "Wallet",
    href: "/wallet",
    icon: Wallet,
  },
  {
    label: "Reports",
    icon: ClipboardMinus,
    subMenus: [
      {
        label: "Payments",
        href: "/reports/payments ",
        icon: CreditCard,
      },
    ],
  },
  { label: "Users", href: "/user-management", icon: UsersRound },
  { label: "Settings", href: "/settings", icon: Settings },
  {
    label: "Integrations",
    icon: Blocks,
    subMenus: [
      { label: "Carriers", href: "/integrations/carriers", icon: Package },
      {
        label: "Marketplaces",
        href: "/integrations/marketplaces",
        icon: Store,
      },
    ],
  },
];

export const additionalMenus: MenuItem[] = [
  { label: "Support", href: "/support", icon: CircleHelp },
];
