const capitalize = (str: string) =>
  str
    .toLowerCase()
    .split("_")
    .map(w => w[0].toUpperCase() + w.slice(1))
    .join(" ");

/**
 * Converts snake_case text to normal capitalized text
 * @param text - The snake_case string to convert
 * @returns Formatted string with proper capitalization and spaces
 *
 * @example
 * snakeCaseToText("in_transit") // "In Transit"
 * snakeCaseToText("APPROVED_PARTIAL") // "Approved Partial"
 * snakeCaseToText("single") // "Single"
 */
const snakeCaseToText = (str: string): string => {
  if (!str) return "";

  return str
    .toLowerCase() // Handle both lowercase and uppercase snake_case
    .split("_")
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
};

export { capitalize, snakeCaseToText };
