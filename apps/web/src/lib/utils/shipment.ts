import CreateNewLabelSchema from "@/schemas/shipment/CreateLabelSchema";
import { FindOneShipment } from "@/types/Shipment/Shipment";
import { Parcel } from "@repo/database";
import { z } from "zod";
import { addressToAddressForm } from "./address";

const shipmentToCreateNewLabelForm = (
  shipment: FindOneShipment
): z.infer<typeof CreateNewLabelSchema> => {
  const parcel = shipment.parcel as unknown as Parc<PERSON>;
  return {
    fromAddressId: shipment.fromAddressId,
    toAddress: addressToAddressForm(shipment.toAddress),
    carrier: shipment.carrier,
    service: shipment.service,
    parcel: {
      weight: parcel.weight,
      length: parcel.length ?? 0,
      width: parcel.width ?? 0,
      height: parcel.height ?? 0,
      predefinedPackage: parcel.predefinedPackage ?? "",
    },
    epShipmentId: undefined,
    insurance: 0,
    rateId: "",
  };
};

export { shipmentToCreateNewLabelForm };
