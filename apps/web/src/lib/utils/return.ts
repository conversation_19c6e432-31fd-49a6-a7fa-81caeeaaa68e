import CreateReturnSchema from "@/schemas/return/CreateReturnSchema";
import { FindOneShipment } from "@/types/Shipment/Shipment";
import { z } from "zod";

const shipmentToReturnForm = (
  shipment: FindOneShipment
): z.infer<typeof CreateReturnSchema> => {
  return {
    rma: "",
    epShipmentId: null,
    service: null,
    toAddressId: shipment.fromAddressId,
    fromAddress: {
      name: shipment.toAddress.name ?? "",
      company: shipment.toAddress.company ?? "",
      street1: shipment.toAddress.street1 ?? "",
      street2: shipment.toAddress.street2 ?? "",
      city: shipment.toAddress.city ?? "",
      state: shipment.toAddress.state ?? "",
      country: shipment.toAddress.country ?? "",
      zip: shipment.toAddress.zip ?? "",
      phone: shipment.toAddress.phone ?? "",
      email: shipment.toAddress.email ?? "",
      residential: shipment.toAddress.residential ?? false,
      verified: false, // TODO: verify address with easypost shipment
    },
    notes: "",
    parcel: {
      weight: 0,
      length: 0,
      width: 0,
      height: 0,
      predefinedPackage: "",
    },
    insurance: 0,
    signature: undefined,
  };
};

export { shipmentToReturnForm };
