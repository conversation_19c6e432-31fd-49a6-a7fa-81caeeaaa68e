import { permissionIds } from "@/config/permissions";
import { UserInviteStatus, UserStatus } from "@repo/database";
import { z } from "zod";

const getInitials = (name: string | undefined | null) => {
  if (!name || name.trim() === "") return "";
  const parts = name.trim().split(/\s+/);
  if (parts.length === 1) {
    return parts[0].charAt(0);
  }
  return `${parts[0].charAt(0)}${parts[parts.length - 1].charAt(0)}`;
};

const getPermissions = (permissions: string[]) => {
  const hasAll = permissionIds.every(pi => permissions.includes(pi));

  if (hasAll) return "Admin";
  return permissions.join(", ");
};

const getColorStatus = (value: UserInviteStatus | UserStatus) => {
  if (value === UserStatus.active) return "text-success";
  if (value === UserStatus.deactivated) return "text-destructive";
  if (value === UserInviteStatus.rejected) return "text-destructive";
  if (value === UserInviteStatus.pending) return "text-primary";
};

const emailValidator = z.string().email();

const parseAndValidateEmails = (input: string) => {
  const emailList = input
    .split(/\s+/)
    .map(email => email.trim())
    .filter(email => email.length > 0);
  const invalidEmails = emailList.filter(
    email => !emailValidator.safeParse(email).success
  );
  return { emailList, invalidEmails };
};

export { getColorStatus, getInitials, getPermissions, parseAndValidateEmails };
