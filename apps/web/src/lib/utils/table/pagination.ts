import {
  functionalUpdate,
  PaginationState,
  Updater,
} from "@tanstack/react-table";

type PaginationData = {
  total: number;
};

export const createPaginationHandler = (
  data: PaginationData,
  pagination: PaginationState,
  setPagination: React.Dispatch<React.SetStateAction<PaginationState>>
) => {
  return (updater: Updater<PaginationState>) => {
    const newPagination = functionalUpdate(updater, pagination);

    const total = Math.max(0, data.total || 0);

    const maxPageIndex =
      total > 0 ? Math.floor((total - 1) / newPagination.pageSize) : 0;
    const safePageIndex = Math.min(
      Math.max(0, newPagination.pageIndex),
      maxPageIndex
    );

    const finalPagination = {
      pageIndex: safePageIndex,
      pageSize: newPagination.pageSize,
    };

    setPagination(finalPagination);
  };
};
