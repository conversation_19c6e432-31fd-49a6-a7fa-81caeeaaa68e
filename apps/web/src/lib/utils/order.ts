import { createOrderSchema } from "@/schemas/order/createOrderSchema";
import OrderItemSchema from "@/schemas/order/OrderItemsSchema";
import AddressSchema from "@/schemas/common/AddressSchema";
import { Order } from "@/types/Order/Order";
import { Parcel } from "@repo/database";
import { z } from "zod";

const getItemQuantity = (items: z.infer<typeof OrderItemSchema>[]) => {
  return items.reduce((acc, item) => acc + item.quantity, 0);
};

const orderToOrderForm = (order: Order): z.infer<typeof createOrderSchema> => {
  const parcel = order.parcel as unknown as Parcel;
  return {
    orderNo: order.orderNo,
    epShipmentId: order.epShipmentId ?? undefined,
    orderDate: new Date(order.orderDate),
    carrier: order.carrier ?? null,
    service: order.service ?? null,
    fromAddressId: order.fromAddressId,
    toAddress: order.toAddress as z.infer<typeof AddressSchema>,
    notes: order.notes ?? "",
    parcel: {
      weight: parcel.weight,
      length: parcel.length ?? 0,
      width: parcel.width ?? 0,
      height: parcel.height ?? 0,
      predefinedPackage: parcel.predefinedPackage ?? "",
    },
    insurance: order.insurance ?? 0,
    signature: order.signature ?? "",
    orderItems: order.orderItems.map(({ product, quantity, price }) => ({
      product: { id: product.id, sku: product.sku, name: product.name },
      quantity,
      price,
    })),
  };
};

export { getItemQuantity, orderToOrderForm };
