import AddressSchema from "@/schemas/common/AddressSchema";
import { AddressJson } from "@/types/Address/AddressJson";
import { Address } from "@repo/database";
import { z } from "zod";

const addressToAddressForm = (
  address: Address | AddressJson
): z.infer<typeof AddressSchema> => {
  return {
    name: address.name,
    company: address.company ?? undefined,
    street1: address.street1,
    street2: address.street2 ?? undefined,
    city: address.city,
    state: address.state,
    country: address.country,
    zip: address.zip,
    email: address.email,
    phone: address.phone ?? undefined,
    residential: address.residential,
    verified: address.verified ?? undefined,
  };
};

export { addressToAddressForm };
