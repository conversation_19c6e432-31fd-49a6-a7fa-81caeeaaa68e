import AddressSchema from "@/schemas/common/AddressSchema";
import ParcelSchema from "@/schemas/common/ParcelSchema";
import { Address } from "@repo/database";
import { z } from "zod";

type AddressType = Address | z.infer<typeof AddressSchema> | { id: string };

const canGetRates = ({
  fromAddress,
  toAddress,
  parcel,
}: {
  fromAddress: AddressType;
  toAddress: AddressType;
  parcel: z.infer<typeof ParcelSchema>;
}) => {
  const isValidAddress = (address: AddressType): boolean => {
    if ("id" in address && address.id) return true;

    const parsed = AddressSchema.safeParse(address);
    return parsed.success;
  };

  const fromAddressValid = isValidAddress(fromAddress);
  const toAddressValid = isValidAddress(toAddress);
  const parcelValid = ParcelSchema.safeParse(parcel).success;

  return fromAddressValid && toAddressValid && parcelValid;
};

export { canGetRates };
