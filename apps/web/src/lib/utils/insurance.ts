import { CLAIM_PERIOD, INSURANCE_CONFIG } from "@/constants/insurance";

/**
 * Calculates the insurance cost for a shipment based on declared value
 *
 * Cost is 1% of declared value with a minimum charge of $0.50
 * Results are rounded up to 2 decimal places
 *
 * @param {number} declaredValue - The declared value of the shipment contents
 * @returns {number} Insurance cost rounded up to 2 decimal places
 *
 * @example
 * calculateInsuranceCost(30);    // returns 0.50 (minimum applies)
 * calculateInsuranceCost(100);   // returns 1.00 (exactly 1%)
 * calculateInsuranceCost(133.33); // returns 1.34 (1.3333... rounded up)
 */
function calculateInsuranceCost(declaredValue: number): number {
  if (declaredValue === 0) {
    return 0;
  }

  const calculated = declaredValue * INSURANCE_CONFIG.RATE;
  const withMinimum = Math.max(calculated, INSURANCE_CONFIG.MINIMUM_CHARGE);
  return Math.ceil(withMinimum * 100) / 100;
}

/**
 * Check if a shipment is eligible for insurance claim
 * @param {Date|string} shipmentCreatedAt - When the shipment was created
 * @returns {Object} - Claim eligibility status and details
 */
function canClaimInsurance(shipmentCreatedAt: Date | string): {
  canClaim: boolean;
  reason: string;
  daysUntilEligible?: number;
  daysOverdue?: number;
  daysRemaining?: number;
} {
  const createdDate = new Date(shipmentCreatedAt);
  const currentDate = new Date();

  // Validate input date
  if (isNaN(createdDate.getTime())) {
    return {
      canClaim: false,
      reason: "Invalid shipment date",
    };
  }

  const daysSinceCreation = Math.floor(
    (currentDate.getTime() - createdDate.getTime()) / (1000 * 60 * 60 * 24)
  );

  const { MIN_DAYS, MAX_DAYS } = CLAIM_PERIOD;

  // Check if within claim window
  if (daysSinceCreation < MIN_DAYS) {
    return {
      canClaim: false,
      reason: `Claim period starts ${MIN_DAYS} days after shipment creation`,
      daysUntilEligible: MIN_DAYS - daysSinceCreation,
    };
  }

  if (daysSinceCreation > MAX_DAYS) {
    return {
      canClaim: false,
      reason: `Claim period expired ${daysSinceCreation - MAX_DAYS} days ago`,
      daysOverdue: daysSinceCreation - MAX_DAYS,
    };
  }

  return {
    canClaim: true,
    reason: "Shipment is eligible for insurance claim",
    daysRemaining: MAX_DAYS - daysSinceCreation,
  };
}

export { calculateInsuranceCost, canClaimInsurance };
