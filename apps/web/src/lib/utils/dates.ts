import {
  differenceInDays,
  differenceInHours,
  differenceInMinutes,
} from "date-fns";

type Age = {
  days: number;
  hours: number;
  minutes: number;
};

const calculateAge = (date: Date): Age => {
  const today = new Date();

  const diffDays = differenceInDays(today, date);
  const diffHours = differenceInHours(today, date) % 24;
  const diffMinutes = differenceInMinutes(today, date) % 60;

  return {
    days: diffDays,
    hours: diffHours,
    minutes: diffMinutes,
  };
};

const displayLargestTimeUnit = ({ days, hours, minutes }: Age) => {
  if (days > 0) return `${days} day${days > 1 ? "s" : ""}`;
  if (hours > 0) return `${hours} hr${hours > 1 ? "s" : ""}`;
  if (minutes > 0) return `${minutes} min${minutes > 1 ? "s" : ""}`;
  return "0 min";
};

export { calculateAge, displayLargestTimeUnit };
