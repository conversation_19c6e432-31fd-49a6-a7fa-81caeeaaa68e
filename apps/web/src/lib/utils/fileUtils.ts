/**
 * Converts bytes to a human-readable file size string
 * @param {number} bytes - The size in bytes
 * @param {number} [decimals=2] - Number of decimal places to show
 * @returns {string} Human-readable file size (e.g., "2.5 MB")
 */
const formatFileSize = (bytes: number, decimals: number = 2): string => {
  if (bytes === 0) return "0 Bytes";

  const k: number = 1024;
  const sizes: string[] = [
    "Bytes",
    "KB",
    "MB",
    "GB",
    "TB",
    "PB",
    "EB",
    "ZB",
    "YB",
  ];

  // Determine the appropriate unit
  const i: number = Math.floor(Math.log(bytes) / Math.log(k));

  // Format with specified decimal places
  return (
    parseFloat((bytes / Math.pow(k, i)).toFixed(decimals)) + " " + sizes[i]
  );
};

const isValidExtension = (
  filename: string,
  allowedExtensions: string[]
): boolean => {
  const extension = filename?.split(".").pop()?.toLowerCase();
  return !!extension && allowedExtensions.includes(extension);
};

const fileGenerate = (file: File) => {
  return {
    data: "base64",
    mimeType: file.type,
    fileName: file.name.split(".")[0] ?? null,
    fileSize: file.size ?? null,
  };
};

const truncateFileName = (name: string, maxLength: number = 15) => {
  return name.length > maxLength ? name.slice(0, maxLength) + "..." : name;
};

const base64ToFile = (base64: string, filename: string): File => {
  const parts = base64.split(",");
  const mime = parts[0].match(/:(.*?);/)?.[1] || "application/octet-stream";
  const bstr = atob(parts[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }

  const ext = mime.split("/")[1] || "bin";
  return new File([u8arr], `${filename}.${ext}`, { type: mime });
};

export {
  formatFileSize,
  isValidExtension,
  fileGenerate,
  truncateFileName,
  base64ToFile,
};
