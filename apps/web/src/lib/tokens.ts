"use server";

import { VerificationToken } from "@repo/database";
import { v4 as uuidv4 } from "uuid";

const ONE_HOUR_MS = 60 * 60 * 1000;

export const generateVerificationToken = async (
  email: string
): Promise<VerificationToken> => {
  const token = uuidv4();
  const expires = new Date(Date.now() + ONE_HOUR_MS);

  try {
    const url = `${process.env.API_URL}/auth/verification-token`;
    const response = await fetch(url, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        email,
        token,
        expires: expires.toISOString(),
      }),
    });

    if (!response.ok) {
      throw new Error(
        `Failed to generate verification token: ${response.status}`
      );
    }

    const data = (await response.json()) as VerificationToken;
    return data;
  } catch (error) {
    console.error("Error generating verification token: ", error);
    throw error;
  }
};
