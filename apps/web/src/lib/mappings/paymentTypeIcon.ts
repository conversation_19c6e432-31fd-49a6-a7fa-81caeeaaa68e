import { CreditCardBrand, PaymentMethodType } from "@repo/database";
import { StaticImageData } from "next/image";

// Icons (✅ TODO: import missing icons as needed)
import achIcon from "@/public/payment/graphio-checking.svg";
import mastercardIcon from "@/public/payment/mastercard-logo.svg";
import visaIcon from "@/public/payment/visa-logo.svg";
import amexIcon from "@/public/payment/amex.svg";
import cartesBancairesIcon from "@/public/payment/cartes-bancaires.svg";
import dciIcon from "@/public/payment/dci.svg";
import discoverIcon from "@/public/payment/discover.svg";
import eftposAuIcon from "@/public/payment/eftpos_au.svg";
import jcbAuIcon from "@/public/payment/jcb.svg";
import linkIcon from "@/public/payment/link.svg";
import unionPayIcon from "@/public/payment/union-pay.svg";

/**
 * Provide icons for payment methods: credit_card, ach
 * or
 * credit card brands: amex, cartes_bancaires,
 * diners, discover, eftpos_au, jcb, link, mastercard,
 * unionpay, visa, unknown
 *
 * **/

const GENERIC_ICON = visaIcon; // TODO: ✅ replace with generic icon

const PAYMENT_TYPE_ICONS: Record<
  Exclude<PaymentMethodType, "credit_card">,
  StaticImageData
> = {
  [PaymentMethodType.ach]: achIcon,
};

const CREDIT_CARD_BRAND_ICONS: Record<CreditCardBrand, StaticImageData> = {
  [CreditCardBrand.amex]: amexIcon,
  [CreditCardBrand.cartes_bancaires]: cartesBancairesIcon,
  [CreditCardBrand.diners]: dciIcon,
  [CreditCardBrand.discover]: discoverIcon,
  [CreditCardBrand.eftpos_au]: eftposAuIcon,
  [CreditCardBrand.jcb]: jcbAuIcon,
  [CreditCardBrand.link]: linkIcon,
  [CreditCardBrand.mastercard]: mastercardIcon,
  [CreditCardBrand.unionpay]: unionPayIcon,
  [CreditCardBrand.visa]: visaIcon,
  [CreditCardBrand.unknown]: GENERIC_ICON, // fallback
};

export const getPaymentTypeIcon = (
  type: PaymentMethodType,
  cardBrand?: CreditCardBrand | null
): StaticImageData => {
  switch (type) {
    case PaymentMethodType.credit_card:
      if (cardBrand != null) {
        return CREDIT_CARD_BRAND_ICONS[cardBrand];
      }
      return GENERIC_ICON;

    case PaymentMethodType.ach:
      return PAYMENT_TYPE_ICONS[PaymentMethodType.ach];

    default: {
      return _satisfyTypeCheck(type);
    }
  }
};

const _satisfyTypeCheck = (value: never): never => {
  throw new Error(`Unhandled case: ${value}`);
};
