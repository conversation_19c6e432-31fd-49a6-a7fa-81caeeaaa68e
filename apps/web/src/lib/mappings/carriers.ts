// Icons
import dhlIcon from "@/public/carriers/dhl-logo.svg";
import fedExIcon from "@/public/carriers/fedex-logo.svg";
import uspsIcon from "@/public/carriers/usps-icon.svg";
import upsIcon from "@/public/carriers/ups-logo.svg";

const getCarrierIcon = (carrierId: string) => {
  switch (carrierId) {
    case "USPS":
      return uspsIcon;
    case "FedExDefault":
      return fedExIcon;
    case "DHL":
      return dhlIcon;
    case "UPS":
      return upsIcon; // TODO: Replace with UPS
    default:
      return null;
  }
};

export { getCarrierIcon };
