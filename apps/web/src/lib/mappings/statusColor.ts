import {
  BatchStatus,
  ClaimStatus,
  OrderStatus,
  PaymentStatus,
  ShipmentStatus,
} from "@repo/database";

export const shipmentStatusColor: Record<ShipmentStatus, string> = {
  unknown: "text-muted-foreground",
  pre_transit: "text-muted-foreground",
  in_transit: "text-muted-foreground",
  out_for_delivery: "text-warning",
  delivered: "text-success",
  available_for_pickup: "text-warning",
  return_to_sender: "text-destructive",
  failure: "text-destructive",
  cancelled: "text-destructive",
  error: "text-destructive",
  voided: "text-destructive",
};

export const claimStatusColor: Record<ClaimStatus, string> = {
  submitted: "text-success",
  in_review: "text-muted-foreground",
  approved: "text-success",
  rejected: "text-destructive",
  approved_partial: "text-primary",
  cancelled: "text-destructive",
  needs_action: "text-warning",
};

export const orderStatusColor: Record<OrderStatus, string> = {
  cancelled: "text-destructive",
  open: "text-primary",
  processing: "text-warning",
  shipped: "text-success",
};

export const batchStatusColor: Record<BatchStatus, string> = {
  open: "text-primary",
  processed: "text-warning",
  archived: "text-success",
};

// TODO: change WalletStatus from @repo/database
export enum WalletStatus {
  pending = "pending",
  complete = "complete",
}

export const walletStatusColor: Record<WalletStatus, string> = {
  pending: "text-primary",
  complete: "text-success",
};

export const paymentStatusColor: Record<PaymentStatus, string> = {
  processing: "text-warning",
  pending: "text-warning",
  completed: "text-success",
  failed: "text-destructive",
  refunded: "text-warning",
};
