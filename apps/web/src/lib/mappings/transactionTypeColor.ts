import { WalletTransactionType } from "@repo/database";
import type { BadgeVariant } from "@/components/ui/badge";

type TransactionConfig = {
  badge: BadgeVariant;
  text: string;
  isNegative?: boolean;
};

export const transactionConfig: Record<
  WalletTransactionType,
  TransactionConfig
> = {
  topup_payment: {
    badge: "success",
    text: "text-success",
  },
  topup_auto: {
    badge: "success",
    text: "text-success",
  },
  refund_cancelled_label: {
    badge: "success",
    text: "text-success",
  },
  label_purchase: {
    badge: "danger",
    text: "text-destructive",
    isNegative: true,
  },
  label_adjustment: {
    badge: "ghost",
    text: "text-destructive",
    isNegative: true,
  },
  service_fee: {
    badge: "ghost",
    text: "text-success",
  },
  adjustment_credit: {
    badge: "warning",
    text: "text-warning",
  },
  adjustment_debit: {
    badge: "ghost",
    text: "text-destructive",
    isNegative: true,
  },
};

// 🔹 helper
export const getTransactionConfig = (type: WalletTransactionType) =>
  transactionConfig[type] ?? { badge: "ghost", text: "text-ghost" };
