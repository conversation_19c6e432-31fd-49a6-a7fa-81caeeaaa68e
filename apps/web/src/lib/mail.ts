"use server";

export const sendVerificationEmail = async (email: string, token: string) => {
  const verificationLink = `${process.env.NEXT_PUBLIC_URL}/auth/new-verification?token=${token}`;

  console.log({ verificationLink });
};

export const sendInviteEmail = async (email: string, token: string) => {
  const inviteLink = `${process.env.NEXT_PUBLIC_URL}/auth/invite?token=${token}`;

  // Console for now
  console.log({ inviteLink });
};
