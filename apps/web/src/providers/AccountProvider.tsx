"use client";

import { useAppDispatch } from "@/store/hooks";
import { fetchAccountData } from "@/store/slices/accountSlice";
import { useEffect } from "react";

const AccountProvider = ({
  userId,
  children,
}: {
  userId: string | undefined;
  children: React.ReactNode;
}) => {
  const dispatch = useAppDispatch();

  useEffect(() => {
    if (userId) {
      dispatch(fetchAccountData());
    }
  }, [userId, dispatch]);

  return <>{children}</>;
};

export default AccountProvider;
