import { getJwtToken } from "@/actions/auth/getToken";
import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { BillingAddress } from "@repo/database";
import { GetWalletDashboardResponse } from "../types/walletDashboard";

export const accountApi = createApi({
  reducerPath: "accountApi",
  baseQuery: fetchBaseQuery({
    baseUrl: process.env.NEXT_PUBLIC_API_URL,
    prepareHeaders: async headers => {
      const token = await getJwtToken();
      headers.set("authorization", `Bearer ${token}`);
      return headers;
    },
  }),
  tagTypes: ["BillingAddress", "WalletDashboard"],
  endpoints: builder => ({
    getWalletDashboard: builder.query<GetWalletDashboardResponse, void>({
      query: () => {
        return `account/dashboard/wallet`;
      },
      providesTags: ["WalletDashboard"],
    }),
    getBillingAddress: builder.query<BillingAddress, void>({
      query: () => "/account/billing-address",
      providesTags: ["BillingAddress"],
    }),
    updateBillingAddress: builder.mutation<
      BillingAddress,
      { data: Partial<BillingAddress> }
    >({
      query: ({ data }) => ({
        url: `account/billing-address`,
        method: "PUT",
        body: data,
      }),
      invalidatesTags: ["BillingAddress"],
    }),
  }),
});

export const {
  useGetBillingAddressQuery,
  useUpdateBillingAddressMutation,
  useGetWalletDashboardQuery,
} = accountApi;
