import { getJwtToken } from "@/actions/auth/getToken";
import { Shipment } from "@/types/Shipment/Shipment";
import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { Shipment as EpShipment } from "@repo/easypost-types";
import {
  BuyNewLabelPayload,
  GetShipmentResponse,
  GetShipmentsResponse,
} from "../types/shipment";

export const shipmentApi = createApi({
  reducerPath: "shipmentApi",
  baseQuery: fetchBaseQuery({
    baseUrl: process.env.NEXT_PUBLIC_API_URL,
    prepareHeaders: async headers => {
      const token = await getJwtToken();
      headers.set("authorization", `Bearer ${token}`);
      return headers;
    },
  }),
  tagTypes: ["Shipment", "EasypostShipment"],
  endpoints: builder => ({
    getShipments: builder.query<GetShipmentsResponse, string | void>({
      query: (params = "") => {
        return `shipments${params ? `?${params}` : ""}`;
      },
      providesTags: ["Shipment"],
    }),
    getShipment: builder.query<GetShipmentResponse, string>({
      query: (shipmentId: string) => `shipments/${shipmentId}`,
      providesTags: ["Shipment"],
    }),
    getShipmentEasypost: builder.query<EpShipment, string>({
      query: (epShipmentId: string) => `shipments/easypost/${epShipmentId}`,
      providesTags: ["EasypostShipment"],
    }),
    convertLabel: builder.mutation({
      query: ({
        epShipmentId,
        data,
      }: {
        epShipmentId: string;
        data: { format: string };
      }) => ({
        url: `shipments/easypost/${epShipmentId}/convert-label`,
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["EasypostShipment"],
    }),
    refetchRates: builder.mutation({
      query: ({
        id,
        options,
      }: {
        id: string;
        options: { insurance: number; confirmation: string };
      }) => ({
        url: `shipments/${id}/refetch-rates`,
        method: "POST",
        body: options,
      }),
      invalidatesTags: ["Shipment"],
    }),
    refundShipment: builder.mutation({
      query: ({ id }: { id: string }) => ({
        url: `shipments/${id}/refund`,
        method: "POST",
      }),
      invalidatesTags: ["Shipment"],
    }),
    buyNewLabel: builder.mutation<
      Shipment,
      { id: string; payload: BuyNewLabelPayload }
    >({
      query: ({
        id,
        payload,
      }: {
        id: string;
        payload: BuyNewLabelPayload;
      }) => ({
        url: `/shipments/${id}/buy-new-label`,
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["Shipment"],
    }),
  }),
});

export const {
  useGetShipmentsQuery,
  useGetShipmentQuery,
  useGetShipmentEasypostQuery,
  useConvertLabelMutation,
  useRefetchRatesMutation,
  useRefundShipmentMutation,
  useBuyNewLabelMutation,
} = shipmentApi;
