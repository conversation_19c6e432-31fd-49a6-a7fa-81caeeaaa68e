import { getJwtToken } from "@/actions/auth/getToken";
import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { PaymentMethod } from "@repo/database";
import { CreateAchPaymentResponse } from "../types/paymentMethods";

export const paymentMethodsApi = createApi({
  reducerPath: "paymentMethodsApi",
  baseQuery: fetchBaseQuery({
    baseUrl: process.env.NEXT_PUBLIC_API_URL,
    prepareHeaders: async headers => {
      const token = await getJwtToken();
      headers.set("authorization", `Bearer ${token}`);
      return headers;
    },
  }),
  tagTypes: ["PaymentMethods"],
  endpoints: builder => ({
    getPaymentMethods: builder.query<PaymentMethod[], void>({
      query: () => "/payment-methods",
      providesTags: ["PaymentMethods"],
    }),
    createAchPayment: builder.mutation<CreateAchPaymentResponse, FormData>({
      query: (formData: FormData) => ({
        url: "payment-methods/ach",
        method: "POST",
        body: formData,
      }),
      invalidatesTags: ["PaymentMethods"],
    }),
    setDefault: builder.mutation<PaymentMethod, string>({
      query: id => ({
        url: `payment-methods/${id}/default`,
        method: "PUT",
      }),
      invalidatesTags: ["PaymentMethods"],
    }),
    deletePayment: builder.mutation<PaymentMethod, string>({
      query: id => ({
        url: `payment-methods/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["PaymentMethods"],
    }),
  }),
});

export const {
  useGetPaymentMethodsQuery,
  useCreateAchPaymentMutation,
  useSetDefaultMutation,
  useDeletePaymentMutation,
} = paymentMethodsApi;
