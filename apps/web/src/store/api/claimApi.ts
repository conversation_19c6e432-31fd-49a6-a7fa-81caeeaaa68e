import { getJwtToken } from "@/actions/auth/getToken";
import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { GetClaimsResponse } from "../types/claim";
import { FindOneClaim } from "@/types/Claim/Claim";
import { TClaimHistory } from "@repo/easypost-types";

export const claimApi = createApi({
  reducerPath: "claimApi",
  baseQuery: fetchBaseQuery({
    baseUrl: process.env.NEXT_PUBLIC_API_URL,
    prepareHeaders: async headers => {
      const token = await getJwtToken();
      headers.set("authorization", `Bearer ${token}`);
      return headers;
    },
  }),
  tagTypes: ["Claim"],
  endpoints: builder => ({
    getClaims: builder.query<GetClaimsResponse, string | void>({
      query: (params = "") => {
        return `claims${params ? `?${params}` : ""}`;
      },
      providesTags: ["Claim"],
    }),
    getClaim: builder.query<FindOneClaim, string>({
      query: (id: string) => `claims/${id}`,
      providesTags: ["Claim"],
    }),
    getStatusHistory: builder.query<TClaimHistory[], string>({
      query: (id: string) => `claims/${id}/status-history`,
      providesTags: ["Claim"],
    }),
    createClaim: builder.mutation({
      query: (formData: FormData) => ({
        url: "claims/create",
        method: "POST",
        body: formData,
      }),
      invalidatesTags: ["Claim"],
    }),
    cancelClaim: builder.mutation({
      query: (id: string) => ({
        url: `claims/${id}/cancel`,
        method: "POST",
        body: id,
      }),
      invalidatesTags: ["Claim"],
    }),
  }),
});

export const {
  useGetClaimsQuery,
  useGetClaimQuery,
  useGetStatusHistoryQuery,
  useCreateClaimMutation,
  useCancelClaimMutation,
} = claimApi;
