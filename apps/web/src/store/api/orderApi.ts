import { getJwtToken } from "@/actions/auth/getToken";
import { createOrderSchema } from "@/schemas/order/createOrderSchema";
import OrderItemSchema from "@/schemas/order/OrderItemsSchema";
import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { OrderStatus } from "@repo/database";
import { z } from "zod";
import {
  BuyLabelPayload,
  BuyLabelResponse,
  GetOrderResponse,
  GetOrdersResponse,
} from "../types/orders";

export const orderApi = createApi({
  reducerPath: "orderApi",
  baseQuery: fetchBaseQuery({
    baseUrl: process.env.NEXT_PUBLIC_API_URL,
    prepareHeaders: async headers => {
      const token = await getJwtToken();
      headers.set("authorization", `Bearer ${token}`);
      return headers;
    },
  }),
  tagTypes: ["Order"],
  endpoints: builder => ({
    getOrders: builder.query<GetOrdersResponse, string | void>({
      query: (params = "") => {
        return `orders${params ? `?${params}` : ""}`;
      },
      providesTags: ["Order"],
    }),
    getOrder: builder.query<GetOrderResponse, string>({
      query: (id: string) => `orders/${id}`,
      providesTags: ["Order"],
    }),
    createOrder: builder.mutation({
      query: (
        newOrder: z.infer<typeof createOrderSchema> & { epShipmentId?: string }
      ) => ({
        url: "orders",
        method: "POST",
        body: newOrder,
      }),
      invalidatesTags: ["Order"],
    }),
    updateOrder: builder.mutation({
      query: ({
        id,
        data,
      }: {
        id: string;
        data: z.infer<typeof createOrderSchema>;
      }) => ({
        url: `orders/${id}`,
        method: "PUT",
        body: data,
      }),
      invalidatesTags: ["Order"],
    }),
    patchOrder: builder.mutation({
      query: ({
        id,
        data,
      }: {
        id: string;
        data: Partial<z.infer<typeof createOrderSchema>>;
      }) => ({
        url: `orders/${id}`,
        method: "PATCH",
        body: data,
      }),
      invalidatesTags: ["Order"],
    }),
    updateOrderStatus: builder.mutation({
      query: ({
        orderId,
        status,
      }: {
        orderId: string;
        status: OrderStatus;
      }) => ({
        url: `orders/${orderId}/status`,
        method: "PATCH",
        body: {
          status,
        },
      }),
      invalidatesTags: ["Order"],
    }),
    updateMultipleOrderStatus: builder.mutation({
      query: ({
        orderIds,
        status,
      }: {
        orderIds: string[];
        status: OrderStatus;
      }) => ({
        url: "orders/status",
        method: "PATCH",
        body: {
          orderIds,
          status,
        },
      }),
      invalidatesTags: ["Order"],
    }),
    splitOrder: builder.mutation({
      query: ({
        orderId,
        data,
      }: {
        orderId: string;
        data: {
          originalOrderItems: z.infer<typeof OrderItemSchema>[];
          newOrders: { items: z.infer<typeof OrderItemSchema>[] }[];
        };
      }) => ({
        url: `orders/${orderId}/split`,
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["Order"],
    }),
    addOrdersToBatch: builder.mutation({
      query: ({
        batchId,
        orderIds,
      }: {
        batchId: string;
        orderIds: string[];
      }) => ({
        url: `orders/assign-batch`,
        method: "PATCH",
        body: {
          batchId,
          orderIds,
        },
      }),
      invalidatesTags: ["Order"],
    }),
    buyLabel: builder.mutation<BuyLabelResponse, BuyLabelPayload>({
      query: ({
        orderId,
        rateId,
        epShipmentId,
        insuranceAmount,
      }: {
        orderId: string;
        rateId: string;
        epShipmentId: string;
        insuranceAmount?: number;
      }) => ({
        url: `/shipments/buy`,
        method: "POST",
        body: {
          orderId,
          rateId,
          epShipmentId,
          insuranceAmount: insuranceAmount ?? undefined,
        },
      }),
      invalidatesTags: ["Order"],
    }),
    oneCallBuy: builder.mutation({
      query: (orderId: string) => ({
        url: `orders/${orderId}/one-call-buy`,
        method: "POST",
        body: orderId,
      }),
      invalidatesTags: ["Order"],
    }),
    generateRatesWithOrderId: builder.mutation({
      query: (orderId: string) => ({
        url: `orders/${orderId}/generate-rates`,
        method: "GET",
      }),
      invalidatesTags: ["Order"],
    }),
  }),
});

export const {
  useGetOrdersQuery,
  useGetOrderQuery,
  useCreateOrderMutation,
  useUpdateOrderMutation,
  usePatchOrderMutation,
  useUpdateOrderStatusMutation,
  useUpdateMultipleOrderStatusMutation,
  useSplitOrderMutation,
  useAddOrdersToBatchMutation,
  useBuyLabelMutation,
  useOneCallBuyMutation,
  useGenerateRatesWithOrderIdMutation,
} = orderApi;
