import { getJwtToken } from "@/actions/auth/getToken";
import { autoRechargeSchema } from "@/schemas/wallet/autoRechargeSetting";
import { Pagination } from "@/types";
import { RechargePlan } from "@/types/Payment/RechargePlan";
import { Wallet } from "@/types/Payment/Wallet";
import { WalletTransaction } from "@/types/Payment/WalletTransaction";
import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { z } from "zod";
import {
  CreateTopUpWalletProps,
  CreateTopUpWalletResponse,
} from "../types/wallet";
import { Payment } from "@/types/Payment/Payment";

export const walletApi = createApi({
  reducerPath: "walletApi",
  baseQuery: fetchBaseQuery({
    baseUrl: process.env.NEXT_PUBLIC_API_URL,
    prepareHeaders: async headers => {
      const token = await getJwtToken();
      headers.set("authorization", `Bearer ${token}`);
      return headers;
    },
  }),
  tagTypes: ["Wallet", "WalletTransaction", "RechargePlan"],
  endpoints: builder => ({
    getWallet: builder.query<Wallet, string | void>({
      query: (query = "") => {
        return `/wallet${query ? `?${query}` : ""}`;
      },
      providesTags: ["Wallet"],
    }),
    getWalletTransactions: builder.query<
      {
        data: WalletTransaction[];
        pagination: Pagination;
      },
      string | void
    >({
      query: (params = "") => {
        return `/wallet/transactions${params ? `?${params}` : ""}`;
      },
      providesTags: ["WalletTransaction"],
    }),
    getRechargePlan: builder.query<RechargePlan, void>({
      query: () => "/wallet/recharge-plan",
      providesTags: ["RechargePlan"],
    }),
    setRechargePlane: builder.mutation({
      query: (data: z.infer<typeof autoRechargeSchema>) => ({
        url: `/wallet/recharge-plan`,
        method: "PUT",
        body: data,
      }),
      invalidatesTags: ["RechargePlan"],
    }),
    createTopUp: builder.mutation<
      CreateTopUpWalletResponse,
      CreateTopUpWalletProps
    >({
      query: data => ({
        url: "/wallet/top-up",
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["Wallet"],
    }),
    createWireTransfer: builder.mutation<Payment, FormData>({
      query: formData => ({
        url: "/wallet/wire-transfer ",
        method: "POST",
        body: formData,
      }),
      invalidatesTags: ["Wallet"],
    }),
  }),
});

export const {
  useGetWalletQuery,
  useGetWalletTransactionsQuery,
  useGetRechargePlanQuery,
  useSetRechargePlaneMutation,
  useCreateTopUpMutation,
  useCreateWireTransferMutation,
} = walletApi;
