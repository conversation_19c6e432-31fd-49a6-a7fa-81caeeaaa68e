import { getJwtToken } from "@/actions/auth/getToken";
import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { Rate } from "@repo/easypost-types";

export const rateApi = createApi({
  reducerPath: "rateApi",
  baseQuery: fetchBaseQuery({
    baseUrl: process.env.NEXT_PUBLIC_API_URL,
    prepareHeaders: async headers => {
      const token = await getJwtToken();
      headers.set("authorization", `Bearer ${token}`);
      return headers;
    },
  }),
  tagTypes: ["Rate"],
  endpoints: builder => ({
    getRates: builder.query<Rate[], string>({
      query: (epShipmentId: string) => `rates/${epShipmentId}`,
      providesTags: ["Rate"],
    }),
  }),
});

export const { useGetRatesQuery } = rateApi;
