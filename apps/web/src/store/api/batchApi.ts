import { getJwtToken } from "@/actions/auth/getToken";
import { Pagination } from "@/types";
import { BatchesWithOrderCount, BatchWithOrders } from "@/types/Batch/Batch";
import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { Batch } from "@repo/database";

export const batchApi = createApi({
  reducerPath: "batchApi",
  baseQuery: fetchBaseQuery({
    baseUrl: process.env.NEXT_PUBLIC_API_URL,
    prepareHeaders: async headers => {
      const token = await getJwtToken();
      headers.set("authorization", `Bearer ${token}`);
      return headers;
    },
  }),
  tagTypes: ["Batch"],
  endpoints: builder => ({
    getBatches: builder.query<
      {
        data: BatchesWithOrderCount[];
        pagination: Pagination;
      },
      string | void
    >({
      query: (params = "") => `batches${params ? `?${params}` : ""}`,
      providesTags: ["Batch"],
    }),
    getBatch: builder.query<BatchWithOrders, string>({
      query: id => `batches/${id}`,
      providesTags: ["Batch"],
    }),
    createBatch: builder.mutation<Batch, void>({
      query: () => ({
        url: "batches",
        method: "POST",
      }),
      invalidatesTags: ["Batch"],
    }),
    updateBatch: builder.mutation<Batch, { id: string; data: Partial<Batch> }>({
      query: ({ id, data }) => ({
        url: `batches/${id}`,
        method: "PUT",
        body: data,
      }),
      invalidatesTags: ["Batch"],
    }),
    archiveBatch: builder.mutation<Batch, { id: string }>({
      query: ({ id }) => ({
        url: `batches/${id}/archive`,
        method: "PATCH",
      }),
      invalidatesTags: ["Batch"],
    }),
    cancelBatch: builder.mutation<
      Batch & { _count: { orders: number } },
      string
    >({
      query: (id: string) => ({
        url: `batches/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["Batch"],
    }),
    uploadBatch: builder.mutation({
      query: (formData: FormData) => ({
        url: `batches/upload`,
        method: "POST",
        body: formData,
      }),
      invalidatesTags: ["Batch"],
    }),
  }),
});

export const {
  useGetBatchesQuery,
  useGetBatchQuery,
  useCreateBatchMutation,
  useUpdateBatchMutation,
  useArchiveBatchMutation,
  useCancelBatchMutation,
  useUploadBatchMutation,
} = batchApi;
