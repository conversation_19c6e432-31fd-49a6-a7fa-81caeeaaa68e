import { getJwtToken } from "@/actions/auth/getToken";
import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { GetPaymentsResponse } from "../types/payment";

export const paymentApi = createApi({
  reducerPath: "paymentApi",
  baseQuery: fetchBaseQuery({
    baseUrl: process.env.NEXT_PUBLIC_API_URL,
    prepareHeaders: async headers => {
      const token = await getJwtToken();
      headers.set("authorization", `Bearer ${token}`);
      return headers;
    },
  }),
  tagTypes: ["Payments"],
  endpoints: builder => ({
    getPayments: builder.query<GetPaymentsResponse, string | void>({
      query: (params = "") => {
        return `/payments${params ? `?${params}` : ""}`;
      },
      providesTags: ["Payments"],
    }),
  }),
});

export const { useGetPaymentsQuery } = paymentApi;
