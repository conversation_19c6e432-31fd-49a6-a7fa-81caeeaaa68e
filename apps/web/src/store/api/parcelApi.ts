import { getJwtToken } from "@/actions/auth/getToken";
import ParcelSchema from "@/schemas/settings/ParcelSchema";
import { Parcel } from "@repo/database";
import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { z } from "zod";

export const parcelApi = createApi({
  reducerPath: "parcelApi",
  baseQuery: fetchBaseQuery({
    baseUrl: process.env.NEXT_PUBLIC_API_URL,
    prepareHeaders: async headers => {
      const token = await getJwtToken();
      headers.set("authorization", `Bearer ${token}`);
      return headers;
    },
  }),
  tagTypes: ["Parcel"],
  endpoints: builder => ({
    getParcels: builder.query<Parcel[], void>({
      query: () => "parcels",
      providesTags: ["Parcel"],
    }),
    createParcel: builder.mutation({
      query: (newParcel: z.infer<typeof ParcelSchema>) => ({
        url: "parcels",
        method: "POST",
        body: newParcel,
      }),
      invalidatesTags: ["Parcel"],
    }),
    updateParcel: builder.mutation({
      query: ({
        id,
        updatedParcel,
      }: {
        id: string;
        updatedParcel: z.infer<typeof ParcelSchema>;
      }) => ({ url: `parcels/${id}`, method: "PUT", body: updatedParcel }),
      invalidatesTags: ["Parcel"],
    }),
    deleteParcel: builder.mutation<void, string>({
      query: id => ({
        url: `parcels/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["Parcel"],
    }),
    deleteParcels: builder.mutation({
      query: (ids: string[]) => ({
        url: "parcels",
        method: "DELETE",
        body: { ids },
      }),
      invalidatesTags: ["Parcel"],
    }),
  }),
});

export const {
  useGetParcelsQuery,
  useCreateParcelMutation,
  useUpdateParcelMutation,
  useDeleteParcelMutation,
  useDeleteParcelsMutation,
} = parcelApi;
