import { getJwtToken } from "@/actions/auth/getToken";
import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { Store } from "@repo/database";

export const storeApi = createApi({
  reducerPath: "storeApi",
  baseQuery: fetchBaseQuery({
    baseUrl: process.env.NEXT_PUBLIC_API_URL,
    prepareHeaders: async headers => {
      const token = await getJwtToken();
      headers.set("authorization", `Bearer ${token}`);
      return headers;
    },
  }),
  tagTypes: ["Store"],
  endpoints: builder => ({
    getStore: builder.query<
      Omit<Store, "apiKey" | "apiSecret" | "createdAt" | "updatedAt">[],
      string | void
    >({
      query: () => {
        return "stores/list";
      },
      providesTags: ["Store"],
    }),
  }),
});

export const { useGetStoreQuery } = storeApi;
