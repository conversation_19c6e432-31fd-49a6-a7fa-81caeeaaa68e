import { getJwtToken } from "@/actions/auth/getToken";
import InviteSchema from "@/schemas/user/InviteSchema";
import { User } from "@/types/User/User";
import { UserInvitesWithPermissions } from "@/types/User/UserInviteWithPermissions";
import { UserWithPermissions } from "@/types/User/UserWithPermissions";
import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { Permission, UserStatus } from "@repo/database";
import { z } from "zod";
import { CreateUserResponse } from "../types/users";

export const userApi = createApi({
  reducerPath: "userApi",
  baseQuery: fetchBaseQuery({
    baseUrl: process.env.NEXT_PUBLIC_API_URL,
    prepareHeaders: async headers => {
      const token = await getJwtToken();
      headers.set("authorization", `Bearer ${token}`);
      return headers;
    },
  }),
  tagTypes: ["User", "UserInvite"],
  endpoints: builder => ({
    getUsers: builder.query<UserWithPermissions[], void>({
      query: () => "users",
      providesTags: ["User"],
    }),
    getUser: builder.query<User, string>({
      query: (id: string) => `users/${id}`,
      providesTags: ["User"],
    }),
    getPermissions: builder.query<Permission[], void>({
      query: () => "users/permissions",
      providesTags: ["User"],
    }),
    getUsersInvite: builder.query<UserInvitesWithPermissions[], void>({
      query: () => "users/invite",
      providesTags: ["UserInvite"],
    }),
    createUserInvites: builder.mutation<
      CreateUserResponse,
      z.infer<typeof InviteSchema>
    >({
      query: (data: z.infer<typeof InviteSchema>) => ({
        url: "users/invite",
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["UserInvite"],
    }),
    deleteUserInvite: builder.mutation({
      query: (inviteId: string) => ({
        url: `users/invite/${inviteId}`,
        method: "DELETE",
      }),
      invalidatesTags: ["UserInvite"],
    }),
    updateUserStatus: builder.mutation<
      User,
      { userId: string; status: UserStatus }
    >({
      query: ({ userId, status }: { userId: string; status: UserStatus }) => ({
        url: `users/${userId}`,
        method: "PUT",
        body: {
          status,
        },
      }),
      invalidatesTags: ["User"],
    }),
  }),
});

export const {
  useGetUsersQuery,
  useGetUserQuery,
  useGetPermissionsQuery,
  useCreateUserInvitesMutation,
  useGetUsersInviteQuery,
  useDeleteUserInviteMutation,
  useUpdateUserStatusMutation,
} = userApi;
