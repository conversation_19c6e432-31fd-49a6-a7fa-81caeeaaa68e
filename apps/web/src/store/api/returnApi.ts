import { getJwtToken } from "@/actions/auth/getToken";
import { FindOneReturn, Return } from "@/types/Return/Return";
import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { CreateReturnPayload, GetReturnsResponse } from "../types/return";

export const returnApi = createApi({
  reducerPath: "returnApi",
  baseQuery: fetchBaseQuery({
    baseUrl: process.env.NEXT_PUBLIC_API_URL,
    prepareHeaders: async headers => {
      const token = await getJwtToken();
      headers.set("authorization", `Bearer ${token}`);
      return headers;
    },
  }),
  tagTypes: ["Return"],
  endpoints: builder => ({
    getReturns: builder.query<GetReturnsResponse, string, void>({
      query: (params = "") => {
        return `returns${params ? `?${params}` : ""}`;
      },
      providesTags: ["Return"],
    }),
    getReturn: builder.query<FindOneReturn, string>({
      query: (returnId: string) => `returns/${returnId}`,
      providesTags: ["Return"],
    }),
    createReturn: builder.mutation<Return, CreateReturnPayload>({
      query: payload => ({
        url: "returns",
        method: "POST",
        body: payload,
      }),
    }),
  }),
});

export const {
  useGetReturnsQuery,
  useGetReturnQuery,
  useCreateReturnMutation,
} = returnApi;
