import { getJwtToken } from "@/actions/auth/getToken";
import AddressSchema from "@/schemas/common/AddressSchema";
import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { Address } from "@repo/database";
import { z } from "zod";

export const addressApi = createApi({
  reducerPath: "addressApi",
  baseQuery: fetchBaseQuery({
    baseUrl: process.env.NEXT_PUBLIC_API_URL,
    prepareHeaders: async headers => {
      const token = await getJwtToken();
      headers.set("authorization", `Bearer ${token}`);
      return headers;
    },
  }),
  tagTypes: ["Address"],
  endpoints: builder => ({
    getAllAddress: builder.query<Address[], void>({
      query: () => "addresses",
      providesTags: ["Address"],
    }),
    createAddress: builder.mutation<Address, z.infer<typeof AddressSchema>>({
      query: (newAddress: z.infer<typeof AddressSchema>) => ({
        url: "addresses",
        method: "POST",
        body: newAddress,
      }),
      invalidatesTags: ["Address"],
    }),
    updateAddress: builder.mutation<
      Address,
      { id: string; updatedAddress: z.infer<typeof AddressSchema> }
    >({
      query: ({
        id,
        updatedAddress,
      }: {
        id: string;
        updatedAddress: z.infer<typeof AddressSchema>;
      }) => ({ url: `addresses/${id}`, method: "PUT", body: updatedAddress }),
      invalidatesTags: ["Address"],
    }),
    deleteAddress: builder.mutation<Address, string>({
      query: id => ({
        url: `addresses/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["Address"],
    }),
    deleteAddresses: builder.mutation<{ count: number }, string[]>({
      query: (ids: string[]) => ({
        url: "addresses",
        method: "DELETE",
        body: { ids },
      }),
      invalidatesTags: ["Address"],
    }),
  }),
});

export const {
  useGetAllAddressQuery,
  useCreateAddressMutation,
  useUpdateAddressMutation,
  useDeleteAddressMutation,
  useDeleteAddressesMutation,
} = addressApi;
