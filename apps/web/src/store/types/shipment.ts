import { Pagination } from "@/types";
import { Shipment } from "@/types/Shipment/Shipment";
import { LabelRefund, OrderItem, Product } from "@repo/database";
import { Order } from "@/types/Order/Order";

export interface GetShipmentsResponse {
  data: (Shipment & { order: Pick<Order, "orderNo"> })[];
  pagination: Pagination;
}

export interface GetShipmentResponse extends Shipment {
  order: {
    orderNo: string;
    orderItems: (OrderItem & { product: Product })[];
    notes: string;
  };
  labelRefunds: LabelRefund[];
}

export type BuyNewLabelPayload = {
  epShipmentId: string;
  selectedRateId: string;
  orderId: string;
  fromAddressId: string;
  insuranceAmount?: number;
};
