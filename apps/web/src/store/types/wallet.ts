import { Payment } from "@/types/Payment/Payment";
import { Wallet } from "@/types/Payment/Wallet";
import { WalletTransaction } from "@/types/Payment/WalletTransaction";
import { PaymentStatus } from "@repo/database";

export type CreateTopUpWalletProps = {
  paymentType: string;
  amount: number;
  fee?: number;
  paymentMethodId: string;
};
export type CreateTopUpWalletResponse = {
  paymentStatus: PaymentStatus;
  payment: Payment;
  message?: string;
  wallet?: Wallet;
  transaction?: WalletTransaction;
};
