import { Payment } from "@/types/Payment/Payment";
import { RechargePlan } from "@/types/Payment/RechargePlan";
import { Wallet } from "@/types/Payment/Wallet";
import { WalletTransaction } from "@/types/Payment/WalletTransaction";
import { Organization, PaymentMethod, PaymentType, User } from "@repo/database";

export type WalletTransactionWithPayment = WalletTransaction & {
  payment?: { type: PaymentType };
};

export type WalletRecentPayment = Pick<
  Payment,
  "id" | "type" | "status" | "amount" | "fee" | "initiatedAt"
> & {
  paymentMethod?: PaymentMethod;
  user: Pick<User, "id" | "firstName" | "lastName" | "image">;
};

export interface GetWalletDashboardResponse extends Organization {
  wallet: Wallet & {
    transactions: WalletTransactionWithPayment[];
    availableBalance: string;
  };
  paymentMethods: PaymentMethod[];
  rechargePlan: RechargePlan;
  payments: WalletRecentPayment[];
  _count: {
    users: number;
  };
}
