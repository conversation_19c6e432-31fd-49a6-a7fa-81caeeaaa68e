import { Pagination } from "@/types";
import { OrderItemWithReason } from "@/types/Return/OrderItemWithReason";
import { Return } from "@/types/Return/Return";

export type CreateReturnPayload = {
  rma: string | undefined;
  orderId: string;
  toAddressId: string;
  selectedRateId: string;
  orderItems: OrderItemWithReason[];
  epShipmentId: string;
  insuranceAmount?: number;
};

export interface GetReturnsResponse {
  data: Return[];
  pagination: Pagination;
}
