import { Pagination } from "@/types";
import { FindAllOrder, Order } from "@/types/Order/Order";
import { Shipment } from "@/types/Shipment/Shipment";
import { EasypostShipment } from "@repo/easypost-types";

export interface GetOrdersResponse {
  data: FindAllOrder[];
  pagination: Pagination;
}

export interface GetOrderResponse extends Order {
  shipment: Shipment[];
}

export interface BuyLabelPayload {
  orderId: string;
  rateId: string;
  epShipmentId: string;
  insuranceAmount?: number;
}

export interface BuyLabelResponse {
  order: Order;
  shipment: Shipment;
  epShipment: EasypostShipment;
}
