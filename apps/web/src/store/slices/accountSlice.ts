import { getJwtToken } from "@/actions/auth/getToken";
import { AccountData } from "@/types";
import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";

interface AccountState {
  data: AccountData | null;
  loading: boolean;
  error: string | null;
}

const initialState: AccountState = {
  data: null,
  loading: false,
  error: null,
};

export const fetchAccountData = createAsyncThunk(
  "account/fetchAccountData",
  async () => {
    const jwtToken = await getJwtToken();
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/account`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${jwtToken}`,
      },
    });

    const data = await response.json();
    if (!response.ok) throw new Error(data.message);
    return data;
  }
);

const accountSlice = createSlice({
  name: "account",
  initialState,
  reducers: {
    updateAccount: (state, action) => {
      if (state.data) {
        state.data = { ...state.data, ...action.payload };
      }
    },
    updateWalletBalance: (state, action: PayloadAction<number>) => {
      if (state.data?.organization?.wallet) {
        const newBalance = (
          parseFloat(state.data.organization.wallet.availableBalance) +
          action.payload
        ).toString();
        state.data.organization.wallet.availableBalance = newBalance;
      }
    },
    clearAccount: state => {
      state.data = null;
      state.error = null;
      state.loading = false;
    },
  },
  extraReducers: builder => {
    builder
      .addCase(fetchAccountData.pending, state => {
        state.loading = true;
      })
      .addCase(fetchAccountData.fulfilled, (state, action) => {
        state.loading = false;
        state.data = action.payload;
        state.error = null;
      })
      .addCase(fetchAccountData.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "An error occurred";
      });
  },
});

export const { updateAccount, clearAccount, updateWalletBalance } =
  accountSlice.actions;
export default accountSlice.reducer;
