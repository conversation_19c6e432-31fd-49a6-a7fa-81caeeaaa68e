import { configureStore } from "@reduxjs/toolkit";

// Reducers
import accountSlice from "./slices/accountSlice";
// Apis
import { accountApi } from "./api/accountApi";
import { addressApi } from "./api/addressApi";
import { parcelApi } from "./api/parcelApi";
import { orderApi } from "./api/orderApi";
import { userApi } from "./api/userApi";
import { batchApi } from "./api/batchApi";
import { rateApi } from "./api/rateApi";
import { shipmentApi } from "./api/shipmentApi";
import { returnApi } from "./api/returnApi";
import { storeApi } from "./api/storeApi";
import { claimApi } from "./api/claimApi";
import { paymentMethodsApi } from "./api/paymentMethodsApi";
import { walletApi } from "./api/walletApi";
import { paymentApi } from "./api/paymentApi";

export const makeStore = () => {
  return configureStore({
    reducer: {
      account: accountSlice,
      [accountApi.reducerPath]: accountApi.reducer,
      [addressApi.reducerPath]: addressApi.reducer,
      [parcelApi.reducerPath]: parcelApi.reducer,
      [orderApi.reducerPath]: orderApi.reducer,
      [userApi.reducerPath]: userApi.reducer,
      [batchApi.reducerPath]: batchApi.reducer,
      [rateApi.reducerPath]: rateApi.reducer,
      [shipmentApi.reducerPath]: shipmentApi.reducer,
      [returnApi.reducerPath]: returnApi.reducer,
      [storeApi.reducerPath]: storeApi.reducer,
      [claimApi.reducerPath]: claimApi.reducer,
      [paymentMethodsApi.reducerPath]: paymentMethodsApi.reducer,
      [walletApi.reducerPath]: walletApi.reducer,
      [paymentApi.reducerPath]: paymentApi.reducer,
    },
    middleware: getDefaultMiddleware =>
      getDefaultMiddleware().concat(
        accountApi.middleware,
        addressApi.middleware,
        parcelApi.middleware,
        orderApi.middleware,
        userApi.middleware,
        batchApi.middleware,
        rateApi.middleware,
        shipmentApi.middleware,
        returnApi.middleware,
        storeApi.middleware,
        claimApi.middleware,
        paymentMethodsApi.middleware,
        walletApi.middleware,
        paymentApi.middleware
      ),
  });
};

// Infer the type of makeStore
export type AppStore = ReturnType<typeof makeStore>;
// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<AppStore["getState"]>;
export type AppDispatch = AppStore["dispatch"];
