import {
  Address,
  Batch,
  OrderItem,
  Order as PrismaOrder,
  Product,
  LabelRefund,
  Shipment,
  Store,
} from "@repo/database";
import { AddressJson } from "../Address/AddressJson";
import { ParcelJson } from "../Parcel/ParcelJson";

export interface Order extends Omit<PrismaOrder, "toAddress" | "parcel"> {
  toAddress: AddressJson;
  parcel: Parcel<PERSON>son;
  fromAddress: Address;
  orderItems: (OrderItem & { product: Product })[];
  batch: Pick<Batch, "name">;
}

export interface FindAllOrder extends Order {
  store: Pick<Store, "name">;
}

export interface FindOneOrder extends Order {
  shipment: Shipment &
    {
      labelRefunds: LabelRefund[];
    }[];
}
