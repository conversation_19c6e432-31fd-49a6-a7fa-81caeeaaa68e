import {
  Address,
  LabelRefund,
  OrderItem,
  Shipment as PrismaShipment,
  Product,
} from "@repo/database";
import { AddressJson } from "../Address/AddressJson";
import { Fee, PostageLabel, Parcel } from "@repo/easypost-types";

/*
This shipment type has the same shape as the PrismaShipment type but has type assertions of Json objects
The asserted fields are:
- postageLabel: PostageLabelJson
- parcel: Parcel => from Easypost but converted to camelCase
- fromAddress: Address => from Easypost but converted to camelCase
- toAddress: Address => from database
- returnAddress: Address => from Easypost but converted to camelCase
- fees: Fee[] => from Easypost but converted to camelCase
*/
export interface Shipment
  extends Omit<
    PrismaShipment,
    "postageLabel" | "parcel" | "toAddress" | "returnAddress" | "fees"
  > {
  postageLabel: PostageLabel;
  parcel: Parcel;
  toAddress: AddressJson;
  returnAddress: AddressJson;
  fees: Fee[];
  fromAddress: Address;
}

export interface FindOneShipment extends Shipment {
  order: {
    orderNo: string;
    orderItems: (OrderItem & { product: Product })[];
    notes: string;
  };
  labelRefunds: LabelRefund[];
}

// TODO:
// export interface ShipmentListItem {}
