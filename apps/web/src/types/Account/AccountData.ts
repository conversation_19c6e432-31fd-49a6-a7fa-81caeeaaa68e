import { Organization as PrismaOrganization, Wallet } from "@repo/database";
import { User } from "../User/User";

interface OrganizationBasic
  extends Pick<PrismaOrganization, "id" | "name" | "phone" | "shipmentVolume"> {
  wallet: Pick<Wallet, "id" | "currency"> & {
    availableBalance: string /** Calculated field: balance - reserved */;
  };
}

export interface AccountData extends User {
  permissions: string[];
  organization: OrganizationBasic;
}
