import {
  Address,
  OrderItem,
  Return as PrismaReturn,
  Product,
  LabelRefund,
} from "@repo/database";
import { AddressJson } from "../Address/AddressJson";
import { ParcelJson } from "../Parcel/ParcelJson";
import { PostageLabelJson } from "../Shipment/PostageLabelJson";

/*
This return type has the same shape as the PrismaReturn type but has type assertions of Json objects
The asserted fields are:
- postageLabel: PostageLabelJson
- fromAddress: Address => from Easypost but converted to camelCase
- parcel: Parcel => from Easypost but converted to camelCase
- toAddress: Address => from database
*/
export interface Return
  extends Omit<PrismaReturn, "postageLabel" | "fromAddress" | "parcel"> {
  postageLabel: PostageLabelJson;
  fromAddress: AddressJson;
  parcel: Parcel<PERSON>son;
  toAddress: Address;
  order: {
    orderNo: string;
  };
  orderItems: (OrderItem & { product: Product })[];
}

export interface FindOneReturn extends Return {
  refund: LabelRefund[];
}
