import { WalletTransaction as PrismaWalletTransaction } from "@repo/database";

export type WalletTransaction = Omit<
  PrismaWalletTransaction,
  | "balanceChange"
  | "previousBalance"
  | "newBalance"
  | "reservedChange"
  | "previousReserved"
  | "newReserved"
> & {
  balanceChange: string;
  previousBalance: string;
  newBalance: string;
  reservedChange: string;
  previousReserved: string;
  newReserved: string;
};
