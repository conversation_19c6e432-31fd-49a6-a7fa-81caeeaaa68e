{"$schema": "https://turborepo.com/schema.json", "ui": "tui", "tasks": {"build": {"dependsOn": ["^build", "^db:generate"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": [".next/**", "!.next/cache/**"]}, "lint": {"dependsOn": ["^lint"]}, "check-types": {"dependsOn": ["^check-types"]}, "dev": {"dependsOn": ["^db:generate"], "cache": false, "persistent": true}, "db:generate": {"cache": false}, "db:migrate": {"cache": false, "persistent": true}, "db:deploy": {"cache": false}}}