**/node_modules
node_modules
npm-debug.log

# BUILD OUTPUTS - ADD THESE!
**/dist
**/.next
**/.turbo
.turbo
dist
.next
logs/

# Environment files
.env
.env.local
.env.development

# Git
.git
.gitignore
README.md

# IDE
.vscode
.idea
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Testing
.jest
.coverage

# Misc
*.tgz
*.tar.gz
.cache
.parcel-cache
.npm
.yarn
.pnp
.pnp.js