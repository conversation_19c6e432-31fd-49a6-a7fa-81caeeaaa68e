{"name": "@repo/easypost-types", "version": "1.0.0", "description": "EasyPost API types for internal use", "main": "dist/index.js", "types": "dist/index.d.ts", "private": true, "files": ["dist"], "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@easypost/api": "^8.1.0"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "typescript": "^5.0.0"}}