import { BaseObject } from "../base";
import { TrackerStatus } from "./TrackerStatus";
import { TrackingLocation } from "./TrackingLocation";

export declare interface TrackingDetail extends BaseObject<"TrackingDetail"> {
  message: string;
  description: string;
  status: TrackerStatus;
  statusDetail: string;
  datetime: string;
  source: string;
  carrierCode: string;
  trackingLocation: TrackingLocation;
}
