import { DatedObject, ObjectWithId } from "../base";
import { Fee } from "../Fee/Fee";
import { CarrierDetail } from "./CarrierDetail";
import { TrackerStatus } from "./TrackerStatus";
import { TrackerStatusDetail } from "./TrackerStatusDetail";
import { TrackingDetail } from "./TrackingDetail";

export declare interface Tracker extends ObjectWithId<"Tracker">, DatedObject {
  trackingCode: string;
  status: TrackerStatus;
  statusDetail: TrackerStatusDetail;
  signedBy: string;
  weight: number;
  estDeliveryDate: string;
  shipmentId: string;
  carrier: string;
  trackingDetails: TrackingDetail[];
  carrierDetail: CarrierDetail;
  publicUrl: string;
  fees: Fee[];
  finalized: boolean;
  isReturn: boolean;
}
