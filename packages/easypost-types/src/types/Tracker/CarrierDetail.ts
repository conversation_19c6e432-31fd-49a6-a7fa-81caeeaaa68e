import { BaseObject } from "../base";
import { TrackingLocation } from "./TrackingLocation";

export declare interface CarrierDetail extends BaseObject<"CarrierDetail"> {
  /**
   * The service level the associated shipment was shipped with (if available)
   */
  service: string;

  /**
   * The type of container the associated shipment was shipped in (if available)
   */
  containerType: string;

  /**
   * The estimated delivery date as provided by the carrier, in the local time zone (if available)
   */
  estDeliveryDateLocal: string;

  /**
   * The estimated delivery time as provided by the carrier, in the local time zone (if available)
   */
  estDeliveryTimeLocal: string;

  /**
   * The location from which the package originated, stringified for presentation (if available)
   */
  originLocation: string;

  /**
   * The location from which the package originated, broken down by city/state/country/zip (if available)
   */
  originTrackingLocation: TrackingLocation;

  /**
   * The location to which the package is being sent, stringified for presentation (if available)
   */
  destinationLocation: string;

  /**
   * The location to which the package is being sent, broken down by city/state/country/zip (if available)
   */
  destinationTrackingLocation: TrackingLocation;

  /**
   * The date and time the carrier guarantees the package to be delivered by (if available)
   */
  guaranteedDeliveryDate: string;

  /**
   * The alternate identifier for this package as provided by the carrier (if available)
   */
  alternateIdentifier: string;

  /**
   * The date and time of the first attempt by the carrier to deliver the package (if available)
   */
  initialDeliveryAttempt: string;
}
