export declare interface Fee {
  /**
   * The name of the category of fee. Possible types are "LabelFee", "PostageFee", "InsuranceFee", and "TrackerFee"
   */
  type: FeeType;

  /**
   * USD value with sub-cent precision
   */
  amount: string;

  /**
   * Whether EasyPost has successfully charged your account for the fee
   */
  charged: boolean;

  /**
   * Whether the Fee has been refunded successfully
   */
  refunded: boolean;
}

export type FeeType = "InsuranceFee" | "LabelFee" | "PostageFee" | "TrackerFee";
