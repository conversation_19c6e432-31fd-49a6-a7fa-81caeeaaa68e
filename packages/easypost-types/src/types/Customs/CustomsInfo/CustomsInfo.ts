import { DatedObject, ObjectWithId } from "../../base";
import { CustomsItem } from "../CustomsItem/CustomsItem";

export declare interface CustomsInfo
  extends ObjectWithId<"CustomsInfo">,
    DatedObject {
  eelPfc?: string | null;
  contentsType?: string | null;
  contentsExplanation?: string | null;
  customsCertify?: boolean | null;
  customsSigner?: string | null;
  nonDeliveryOption?: "abandon" | "return" | null;
  restrictionType?:
    | "none"
    | "other"
    | "quarantine"
    | "sanitary_phytosanitary_inspection"
    | null;
  restrictionComments?: string | null;
  customsItems: CustomsItem[];
  declaration?: string | null;
}
