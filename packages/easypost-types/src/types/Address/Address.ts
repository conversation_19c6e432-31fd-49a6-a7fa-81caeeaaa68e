import { Verifications } from "./Verifications";

export interface Address {
  /**
   * Unique identifier, begins with "adr_" / "prcl_" / "ins_" / "trk_" / "batch_" / "cstinfo_" / etc
   */
  id: string;

  /**
   * Set based on which api-key you used, either "test" or "production"
   */
  mode: "test" | "production";

  /**
   * The object name, e.g. "Address", "Rate", "Shipment", etc
   */
  object: "Address";
  /**
   * First line of the address
   */
  street1?: string | null;

  /**
   * Second line of the address
   */
  street2?: string | null;

  /**
   * City the address is located in
   */
  city?: string | null;

  /**
   * State or province the address is located in
   */
  state?: string | null;

  /**
   * ZIP or postal code the address is located in
   */
  zip: string;

  /**
   * ISO 3166 country code for the country the address is located in
   */
  country: string;

  /**
   * Whether or not this address would be considered residential
   */
  residential?: boolean | null;

  /**
   * The specific designation for the address (only relevant if the address is a carrier facility)
   */
  carrierFacility?: string | null;

  /**
   * Name of the person. Both name and company can be included
   */
  name?: string | null;

  /**
   * Name of the organization. Both name and company can be included
   */
  company?: string | null;

  /**
   * Phone number to reach the person or organization
   */
  phone?: string | null;

  /**
   * Email to reach the person or organization
   */
  email?: string | null;

  /**
   * Federal tax identifier of the person or organization
   */
  federalTaxId?: string | null;

  /**
   * State tax identifier of the person or organization
   */
  stateTaxId?: string | null;

  /**
   * The result of any verifications requested
   */
  verifications: Verifications;
}
