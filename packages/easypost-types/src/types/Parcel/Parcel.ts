export interface Parcel {
  id: string;
  mode: "test" | "production";
  object: "Parcel";
  /**
   * Required if width and/or height are present
   * float (inches)
   */
  length?: number | null;

  /**
   * Required if width and/or height are present
   * float (inches)
   */
  width?: number | null;

  /**
   * Required if width and/or height are present
   * float (inches)
   */
  height?: number | null;

  /**
   * Always required
   * float (oz)
   */
  weight: number;

  /**
   * Optional, one of our predefined_packages
   */
  predefinedPackage?: string | null;
}
