import { DatedObject, ObjectWithId } from "../../base";
import { TClaimHistory } from "./ClaimHistory";
import { TClaimPaymentMethod } from "./ClaimPaymentMethod";
import { TClaimType } from "./ClaimType";

export interface EpClaim extends ObjectWithId<"Claim">, DatedObject {
  /** The amount that has been approved for reimbursement */
  approved_amount: string | null;

  /** A list of links to attachments associated with the claim */
  attachments: string[];

  /** The address to which the reimbursement check should be sent */
  check_delivery_address: string | null;

  /** The email to contact if more information is needed on this claim */
  contact_email: string;

  /** The description of the claim */
  description: string;

  /** A list of status updates for this claim */
  history: TClaimHistory[];

  /** The amount of insurance that was purchased for the rate associated with the claim */
  insurance_amount: string;

  /** The id associated with the insurance purchased for this claim */
  insurance_id: string;

  /** The way the claim will be payed out */
  payment_method: TClaimPaymentMethod;

  /** The name of the person who was to receive the shipment */
  recipient_name: null;

  /** An optional value that may be used in place of ID when doing Retrieve calls for this claim. */
  reference: string | null;

  /** The amount the claim is for */
  requested_amount: string;

  /** The salvage value of the damaged goods */
  salvage_value: null;

  /** The shipment id that is associated with this claim */
  shipment_id: string;

  /** The current status of the claim */
  status: string;

  /** Details about the current status of the claim */
  status_detail: string;

  /** When the status was last updated */
  status_timestamp: string;

  /** The tracking_code associated with the rate this claim was made for */
  tracking_code: string;

  /** The reason the package was in an unacceptable state and that this claim was made */
  type: TClaimType;
}
