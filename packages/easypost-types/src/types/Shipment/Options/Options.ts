import { PrintCustomCode } from "./PrintCustomCode";
import { LabelFormat } from "./LabelFormat";

export declare interface Options {
  additionalHandling?: boolean | null;
  addressValidation_level?: string | null;
  alcohol?: boolean | null;
  byDrone?: boolean | null;
  carbonNeutral?: boolean | null;
  codAmount?: string | null;
  codMethod?: "CASH" | "CHECK" | "MONEY_ORDER" | null;
  codAddressId?: string | null;
  contentDescription?: string | null;
  currency?: string | null;

  deliveryConfirmation?:
    | "ADULT_SIGNATURE"
    | "SIGNATURE"
    | "NO_SIGNATURE"
    | "INDIRECT_SIGNATURE"
    | "ADULT_SIGNATURE_RESTRICTED"
    | "SIGNATURE_RESTRICTED"
    | null;

  dropoffType?:
    | "REGULAR_PICKUP"
    | "SCHEDULED_PICKUP"
    | "RETAIL_LOCATION"
    | "STATION"
    | "DROP_BOX"
    | "REQUEST_COURIER"
    | "BUSINESS_SERVICE_CENTER"
    | null;

  dryIce?: boolean | null;
  dryIceMedical?: boolean | null;
  dryIceWeight?: string | null;
  dutyPayment: object | null;

  endorsement?:
    | "ADDRESS_SERVICE_REQUESTED"
    | "FORWARDING_SERVICE_REQUESTED"
    | "CHANGE_SERVICE_REQUESTED"
    | "RETURN_SERVICE_REQUESTED"
    | "LEAVE_IF_NO_RESPONSE"
    | null;

  endShipperId: string | null;

  freightCharge?: number | null;

  handlingInstructions?: string | null;

  hazmat?:
    | "PRIMARY_CONTAINED"
    | "PRIMARY_PACKED"
    | "PRIMARY"
    | "SECONDARY_CONTAINED"
    | "SECONDARY_PACKED"
    | "SECONDARY"
    | "ORMD"
    | "LIMITED_QUANTITY"
    | "LITHIUM"
    | null;

  holdForPickup?: boolean | null;

  incoterm?:
    | "EXW"
    | "FCA"
    | "CPT"
    | "CIP"
    | "DAT"
    | "DAP"
    | "DDP"
    | "FAS"
    | "FOB"
    | "CFR"
    | "CIF"
    | null;

  invoiceNumber?: string | null;

  labelDate?: string | null;

  labelFormat?: LabelFormat | null;
  machinable?: boolean | null;

  payment: {
    type?: "SENDER" | "THIRD_PARTY" | "RECEIVER" | "COLLECT" | null;
    account?: string | null;
    country?: string | null;
    postalCode?: string | null;
  };
  pickupMinDatetime: string | null;
  pickupManDatetime: string | null;
  printCustom1?: string | null;
  printCustom2?: string | null;
  printCustom3?: string | null;
  printCustom1Barcode?: boolean | null;
  printCustom2Barcode?: boolean | null;
  printCustom3Barcode?: boolean | null;
  printCustom1Code?: PrintCustomCode | null;
  printCustom2Code?: PrintCustomCode | null;
  printCustom3Code?: PrintCustomCode | null;
  saturdayDelivery?: boolean | null;
  specialRatesEligibility?: "USPS.MEDIAMAIL" | "USPS.LIBRARYMAIL" | null;
  smartpostHub?: string | null;
  smartpostManifest?: string | null;
  billingRef?: string | null;

  /**
   * Certified Mail provides the sender with a mailing receipt and, upon request, electronic verification that an article was delivered or that a delivery attempt was made.
   */
  certifiedMail?: boolean | null;

  /**
   * Registered Mail is the most secure service that the USPS offers.
   * It incorporates a system of receipts to monitor the movement of the mail from the point of acceptance to delivery
   */
  registeredMail?: boolean | null;

  /**
   * The value of the package contents
   */
  registeredMailAmount?: number | null;

  /**
   * An electronic return receipt may be purchased at the time of mailing and provides a shipper with evidence of delivery (to whom the mail was delivered and date of delivery), and information about the recipient's actual delivery address.
   * Only applies to the USPS.
   */
  returnReceipt?: boolean | null;
}
