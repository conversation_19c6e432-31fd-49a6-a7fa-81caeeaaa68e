export interface Rate {
  id: string;
  mode: "test" | "production";
  object: "Rate";
  service: string;

  /**
   * name of carrier
   */
  carrier: string;

  /**
   * ID of the CarrierAccount record used to generate this rate
   */
  carrierAccountId: string;

  /**
   * 	ID of the Shipment this rate belongs to
   */
  shipmentId: string;

  /**
   * the actual rate quote for this service
   */
  rate: string;

  /**
   * currency for the rate
   */
  currency: string;

  /**
   * the retail rate is the in-store rate given with no account
   */
  retailRate: string;

  /**
   * currency for the retail rate
   */
  retailCurrency: string;

  /**
   * the list rate is the non-negotiated rate given for having an account with the carrier
   */
  listRate: string;

  /**
   * currency for the list rate
   */
  listCurrency: string;

  /**
   * delivery days for this service
   */
  deliveryDays: number;

  /**
   * date for delivery
   */
  deliveryDate: string;

  /**
   * indicates if delivery window is guaranteed (true) or not (false)
   */
  deliveryDateGuaranteed: boolean;
}
