import { DatedObject, ObjectWithId } from "../base";

export interface Form extends ObjectWithId<"Form">, DatedObject {
  /**
   * The type of form that we returned, can be one of "cn22", "cod_return_label", "commercial_invoice", "high_value_report", "label_qr_code", "nafta_certificate_of_origin", "order_summary", "return_packing_slip", "rma_qr_code"
   */
  formType:
    | "cn22"
    | "cod_return_label"
    | "commercial_invoice"
    | "high_value_report"
    | "label_qr_code"
    | "nafta_certificate_of_origin"
    | "order_summary"
    | "return_packing_slip"
    | "rma_qr_code";

  /**
   * The address we return the form back at
   */
  formUrl: string;

  /**
   * If we have submitted the form to the carrier on behalf of the customer
   */
  submittedElectronically: boolean;
}
