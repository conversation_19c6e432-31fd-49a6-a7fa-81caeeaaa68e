import { Address } from "../Address/Address";
import { Fee } from "../Fee/Fee";
import { Parcel } from "../Parcel/Parcel";
import { Rate } from "./Rate";
import { CustomsInfo } from "../Customs/CustomsInfo/CustomsInfo";
import { ScanForm } from "../ScanForm/ScanForm";
import { Form } from "./Form";
import { Tracker } from "../Tracker/Tracker";
import { Message } from "./Message";
import { Options } from "./Options/Options";
import { PostageLabel } from "./PostageLabel";
import { BatchStatus } from "../Batch/BatchStatus";
import { Insurance } from "../Insurance/Insurance";
import { DatedObject, ObjectWithId } from "../base";

export interface Shipment extends ObjectWithId<"Shipment">, DatedObject {
  reference?: string | null;
  toAddress: Address;
  fromAddress: Address;
  returnAddress?: Address | null;
  buyerAddress?: Address | null;
  parcel: Parcel;
  customsInfo?: CustomsInfo | null;
  scanForm: ScanForm;
  forms: Form[];
  insurance: Insurance;
  rates: Rate[];
  selectedRate: Rate;
  postageLabel: PostageLabel;
  messages: Message[];
  options?: Options | null;
  isReturn?: boolean | null;
  trackingCode: string;
  uspsZone: string;
  status: string;
  tracker: Tracker;
  fees: Fee[];
  refundStatus: "submitted" | "refunded" | "rejected";
  batchId: string;
  batchStatus: BatchStatus;
  batchMessage: string;
}
