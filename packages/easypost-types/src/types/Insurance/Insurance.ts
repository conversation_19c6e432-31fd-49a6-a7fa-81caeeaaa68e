import { DatedObject, ObjectWithId } from "../base";
import { InsuranceStatus } from "./InsuranceStatus";
import { Address } from "../Address/Address";
import { Tracker } from "../Tracker/Tracker";
import { Fee } from "../Fee/Fee";

export interface Insurance extends ObjectWithId<"Insurance">, DatedObject {
  reference?: string | null;
  amount: string;
  provider: string;
  providerId: string;
  shipmentId: string;
  trackingCode: string;
  status: InsuranceStatus;
  tracker: Tracker;
  toAddress: Address;
  fromAddress: Address;
  fee: Fee;
  messages: string[];
}
