import { Prisma, StoreProvider, StoreStatus } from "@prisma/client";
import { OrganizationCarrierPricing, Permission } from "../generated/prisma";
import { prisma } from "./client";

const permissions: Permission[] = [
  {
    id: "user_management",
    name: "User Management",
    description: "Ability to manage users, create users, and delete users.",
  },
  {
    id: "shipment",
    name: "Shipment",
    description:
      "Ability to manage shipments, create labels, return labels, void labels, and create SCAN forms.",
  },
  {
    id: "order",
    name: "Order",
    description: "View orders and shipments and modify order information.",
  },
  {
    id: "finance",
    name: "Finance",
    description: "See financial transactions and top-up wallet.",
  },
  {
    id: "configuration",
    name: "Configuration",
    description:
      "Add, remove, manage settings like marketplace, warehouses, parcel type and general settings.",
  },
];

const orgCarrierPricing = [
  {
    organizationId: "cmcd0vdaj00018zil5v1qslld",
    carrierId: "ca_d34b12a50e274a35b93e701ab4895e80",
    percentageMarkup: 20,
  },
  {
    organizationId: "cmcd0vdaj00018zil5v1qslld",
    carrierId: "ca_e98e28fcaa2a4be5ba00194ef57658d2",
    percentageMarkup: 20,
  },
  {
    organizationId: "cmcd0vdaj00018zil5v1qslld",
    carrierId: "ca_fcb1138825364cabb6374c5afc57eaad",
    percentageMarkup: 20,
  },
];

export async function main() {
  // await prisma.organizationCarrierPricing.createMany({
  //   data: orgCarrierPricing,
  // });
  // await prisma.billingAddress.create({
  //   data: {
  //     organizationId: "cmcd0vdaj00018zil5v1qslld",
  //     name: "Alyn Augsornworawat",
  //     company: "Graphio Co., Ltd.",
  //     street1: "123/11-12 Suk Kasame Rd.",
  //     street2: "Moo 2 Suthep Sub-District",
  //     city: "Chiang Mai",
  //     state: "Chiang Mai",
  //     country: "Thailand",
  //     zip: "50200",
  //     phone: "+66 81 234 5678",
  //     email: "<EMAIL>",
  //   },
  // });

  await prisma.wallet.create({
    data: {},
  });
}

main();
