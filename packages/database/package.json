{"name": "@repo/database", "version": "0.0.0", "scripts": {"build": "prisma generate && tsup ./src/index.ts --format cjs", "dev": "prisma generate && tsup ./src/index.ts --format cjs --watch", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev --skip-generate", "db:deploy": "prisma migrate deploy"}, "prisma": {"seed": "ts-node src/seed.ts"}, "devDependencies": {"@types/node": "^22.15.19", "prisma": "6.5.0", "ts-node": "^10.9.2", "tsup": "^8.4.0"}, "dependencies": {"@prisma/client": "6.5.0", "@repo/typescript-config": "workspace:*", "@swc/helpers": "^0.5.17"}, "exports": {".": {"types": "./src/index.ts", "require": "./dist/index.js", "import": "./dist/index.js"}}, "main": "./src/index.ts", "types": "./src/index.ts"}