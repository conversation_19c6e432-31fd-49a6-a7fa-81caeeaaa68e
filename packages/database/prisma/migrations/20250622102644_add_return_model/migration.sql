/*
  Warnings:

  - The `printed` column on the `Shipment` table would be dropped and recreated. This will lead to data loss if there is data in the column.

*/
-- AlterTable
ALTER TABLE "Shipment" DROP COLUMN "printed",
ADD COLUMN     "printed" TIMESTAMP(3);

-- CreateTable
CREATE TABLE "Return" (
    "id" TEXT NOT NULL,
    "dropId" TEXT NOT NULL,
    "rma" TEXT NOT NULL,
    "orderId" TEXT NOT NULL,
    "epShipmentId" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "printed" TIMESTAMP(3),
    "trackingCode" TEXT,
    "fromAddress" JSONB NOT NULL,
    "toAddress" JSONB NOT NULL,
    "returnAddress" JSONB NOT NULL,
    "parcel" JSONB NOT NULL,
    "postageLabel" JSONB NOT NULL,
    "selectedRate" JSONB,
    "fees" JSONB NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Return_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Return_orderId_key" ON "Return"("orderId");

-- CreateIndex
CREATE UNIQUE INDEX "Return_epShipmentId_key" ON "Return"("epShipmentId");

-- CreateIndex
CREATE INDEX "Return_organizationId_idx" ON "Return"("organizationId");

-- AddForeignKey
ALTER TABLE "Return" ADD CONSTRAINT "Return_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Return" ADD CONSTRAINT "Return_orderId_fkey" FOREIGN KEY ("orderId") REFERENCES "Order"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
