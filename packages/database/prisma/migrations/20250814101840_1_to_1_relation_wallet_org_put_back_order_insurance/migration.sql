/*
  Warnings:

  - A unique constraint covering the columns `[walletId]` on the table `Organization` will be added. If there are existing duplicate values, this will fail.
  - Made the column `walletId` on table `Organization` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterTable
ALTER TABLE "Order" ADD COLUMN     "insurance" DOUBLE PRECISION DEFAULT 0;

-- AlterTable
ALTER TABLE "Organization" ALTER COLUMN "walletId" SET NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "Organization_walletId_key" ON "Organization"("walletId");

-- AddForeignKey
ALTER TABLE "Organization" ADD CONSTRAINT "Organization_walletId_fkey" FOREIGN KEY ("walletId") REFERENCES "Wallet"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
