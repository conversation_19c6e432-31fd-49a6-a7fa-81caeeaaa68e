/*
  Warnings:

  - The values [manual_ach,manual_wire] on the enum `ProcessingMethod` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "ProcessingMethod_new" AS ENUM ('stripe', 'manual');
ALTER TABLE "Payment" ALTER COLUMN "processingMethod" TYPE "ProcessingMethod_new" USING ("processingMethod"::text::"ProcessingMethod_new");
ALTER TYPE "ProcessingMethod" RENAME TO "ProcessingMethod_old";
ALTER TYPE "ProcessingMethod_new" RENAME TO "ProcessingMethod";
DROP TYPE "ProcessingMethod_old";
COMMIT;

-- AlterTable
ALTER TABLE "PaymentMethod" ADD COLUMN     "authorizationVersion" TEXT;

-- AlterTable
ALTER TABLE "RechargePlan" ALTER COLUMN "enabled" SET DEFAULT true;
