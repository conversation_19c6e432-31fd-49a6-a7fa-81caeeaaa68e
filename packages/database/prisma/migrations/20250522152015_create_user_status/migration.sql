/*
  Warnings:

  - The values [ACCEPTED] on the enum `UserInviteStatus` will be removed. If these variants are still used in the database, this will fail.

*/
-- CreateEnum
CREATE TYPE "UserStatus" AS ENUM ('ACTIVE', 'DEACTIVATED');

-- AlterEnum
BEGIN;
CREATE TYPE "UserInviteStatus_new" AS ENUM ('PENDING', 'EXPIRED', 'REJECTED');
ALTER TABLE "UserInvite" ALTER COLUMN "status" DROP DEFAULT;
ALTER TABLE "UserInvite" ALTER COLUMN "status" TYPE "UserInviteStatus_new" USING ("status"::text::"UserInviteStatus_new");
ALTER TYPE "UserInviteStatus" RENAME TO "UserInviteStatus_old";
ALTER TYPE "UserInviteStatus_new" RENAME TO "UserInviteStatus";
DROP TYPE "UserInviteStatus_old";
ALTER TABLE "UserInvite" ALTER COLUMN "status" SET DEFAULT 'PENDING';
COMMIT;

-- AlterTable
ALTER TABLE "User" ADD COLUMN     "status" "UserStatus" NOT NULL DEFAULT 'ACTIVE';
