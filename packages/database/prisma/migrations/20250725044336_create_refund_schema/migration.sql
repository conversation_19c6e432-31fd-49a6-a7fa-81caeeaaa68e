/*
  Warnings:

  - Made the column `claimNo` on table `Claim` required. This step will fail if there are existing NULL values in that column.

*/
-- CreateEnum
CREATE TYPE "RefundStatus" AS ENUM ('submitted', 'refunded', 'rejected');

-- AlterTable
ALTER TABLE "Claim" ALTER COLUMN "claimNo" SET NOT NULL;

-- CreateTable
CREATE TABLE "Refund" (
    "id" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "shipmentId" TEXT,
    "returnId" TEXT,
    "epRefundId" TEXT NOT NULL,
    "trackingCode" TEXT NOT NULL,
    "status" "RefundStatus" NOT NULL,
    "carrier" TEXT NOT NULL,
    "confirmationNumber" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Refund_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Refund_shipmentId_key" ON "Refund"("shipmentId");

-- CreateIndex
CREATE UNIQUE INDEX "Refund_returnId_key" ON "Refund"("returnId");

-- AddForeignKey
ALTER TABLE "Refund" ADD CONSTRAINT "Refund_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Refund" ADD CONSTRAINT "Refund_shipmentId_fkey" FOREIGN KEY ("shipmentId") REFERENCES "Shipment"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Refund" ADD CONSTRAINT "Refund_returnId_fkey" FOREIGN KEY ("returnId") REFERENCES "Return"("id") ON DELETE NO ACTION ON UPDATE CASCADE;
