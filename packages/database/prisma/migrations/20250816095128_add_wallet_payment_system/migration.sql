/*
  Warnings:

  - The values [top_up,payment,refund,adjustment,fee] on the enum `WalletTransactionType` will be removed. If these variants are still used in the database, this will fail.
  - You are about to drop the column `paymentMethodId` on the `RechargePlan` table. All the data in the column will be lost.
  - You are about to drop the column `walletId` on the `RechargePlan` table. All the data in the column will be lost.
  - You are about to drop the column `amount` on the `WalletTransaction` table. All the data in the column will be lost.
  - You are about to drop the column `previewBalance` on the `WalletTransaction` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[organizationId]` on the table `RechargePlan` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `organizationId` to the `RechargePlan` table without a default value. This is not possible if the table is not empty.
  - Added the required column `balanceChange` to the `WalletTransaction` table without a default value. This is not possible if the table is not empty.
  - Added the required column `previousBalance` to the `WalletTransaction` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "WalletTransactionCategory" AS ENUM ('settled', 'reserved', 'released');

-- CreateEnum
CREATE TYPE "PaymentType" AS ENUM ('credit_card', 'credit_card_auto', 'ach', 'ach_auto', 'wire_transfer', 'paypal');

-- CreateEnum
CREATE TYPE "PaymentStatus" AS ENUM ('pending', 'processing', 'completed', 'failed', 'refunded');

-- CreateEnum
CREATE TYPE "ProcessingMethod" AS ENUM ('stripe', 'manual_ach', 'manual_wire');

-- CreateEnum
CREATE TYPE "PaymentMethodStatus" AS ENUM ('pending', 'verified', 'failed', 'expired', 'inactive');

-- CreateEnum
CREATE TYPE "PaymentMethodType" AS ENUM ('credit_card', 'ach');

-- AlterEnum
BEGIN;
CREATE TYPE "WalletTransactionType_new" AS ENUM ('topup_payment', 'topup_auto', 'refund_cancelled_label', 'label_purchase', 'label_adjustment', 'service_fee', 'adjustment_credit', 'adjustment_debit');
ALTER TABLE "WalletTransaction" ALTER COLUMN "type" TYPE "WalletTransactionType_new" USING ("type"::text::"WalletTransactionType_new");
ALTER TYPE "WalletTransactionType" RENAME TO "WalletTransactionType_old";
ALTER TYPE "WalletTransactionType_new" RENAME TO "WalletTransactionType";
DROP TYPE "WalletTransactionType_old";
COMMIT;

-- DropForeignKey
ALTER TABLE "RechargePlan" DROP CONSTRAINT "RechargePlan_walletId_fkey";

-- DropIndex
DROP INDEX "RechargePlan_walletId_key";

-- AlterTable
ALTER TABLE "RechargePlan" DROP COLUMN "paymentMethodId",
DROP COLUMN "walletId",
ADD COLUMN     "failureCount" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "lastTriggeredAt" TIMESTAMP(3),
ADD COLUMN     "organizationId" TEXT NOT NULL,
ALTER COLUMN "threshold" SET DATA TYPE DECIMAL(12,4),
ALTER COLUMN "amount" SET DATA TYPE DECIMAL(12,4);

-- AlterTable
ALTER TABLE "Wallet" ALTER COLUMN "balance" SET DATA TYPE DECIMAL(12,4),
ALTER COLUMN "reserved" SET DATA TYPE DECIMAL(12,4);

-- AlterTable
ALTER TABLE "WalletTransaction" DROP COLUMN "amount",
DROP COLUMN "previewBalance",
ADD COLUMN     "balanceChange" DECIMAL(12,4) NOT NULL,
ADD COLUMN     "category" "WalletTransactionCategory" NOT NULL DEFAULT 'settled',
ADD COLUMN     "description" TEXT,
ADD COLUMN     "newReserved" DECIMAL(12,4),
ADD COLUMN     "paymentId" TEXT,
ADD COLUMN     "previousBalance" DECIMAL(12,4) NOT NULL,
ADD COLUMN     "previousReserved" DECIMAL(12,4),
ADD COLUMN     "referenceId" TEXT,
ADD COLUMN     "referenceType" TEXT,
ADD COLUMN     "reservedChange" DECIMAL(12,4),
ALTER COLUMN "newBalance" DROP DEFAULT,
ALTER COLUMN "newBalance" SET DATA TYPE DECIMAL(12,4);

-- CreateTable
CREATE TABLE "Payment" (
    "id" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "type" "PaymentType" NOT NULL,
    "status" "PaymentStatus" NOT NULL DEFAULT 'pending',
    "processingMethod" "ProcessingMethod" NOT NULL,
    "paymentMethodId" TEXT,
    "amount" DECIMAL(12,4) NOT NULL DEFAULT 0.00,
    "fee" DECIMAL(12,4) NOT NULL DEFAULT 0.00,
    "stripePaymentIntentId" TEXT,
    "wireReceivedAmount" DECIMAL(12,4) DEFAULT 0.00,
    "wireFeeAmount" DECIMAL(12,4) DEFAULT 0.00,
    "achTraceNo" TEXT,
    "metadata" JSONB,
    "description" TEXT,
    "initiatedAt" TIMESTAMP(3),
    "completedAt" TIMESTAMP(3),
    "failedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Payment_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PaymentMethod" (
    "id" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "type" "PaymentMethodType" NOT NULL,
    "status" "PaymentMethodStatus" NOT NULL,
    "stripeCustomerId" TEXT,
    "stripePaymentMethodId" TEXT,
    "cardLast4" TEXT,
    "cardBrand" TEXT,
    "cardExpMonth" INTEGER,
    "cardExpYear" INTEGER,
    "customerName" TEXT,
    "customerCompany" TEXT,
    "customerEmail" TEXT,
    "customerPhone" TEXT,
    "customerAddress" TEXT,
    "bankName" TEXT,
    "bankLast4" TEXT,
    "bankAccountName" TEXT,
    "bankAccountNo" TEXT,
    "bankRoutingNo" TEXT,
    "bankAccountType" TEXT,
    "bankHolderType" TEXT,
    "bankBillingAddress" TEXT,
    "authorizationText" TEXT,
    "authorizedAt" TIMESTAMP(3),
    "signatureFile" TEXT,
    "isDefault" BOOLEAN NOT NULL DEFAULT false,
    "lastUsedAt" TIMESTAMP(3),
    "failureCount" INTEGER NOT NULL DEFAULT 0,
    "verifiedBy" TEXT,
    "verifiedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PaymentMethod_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "Payment_organizationId_idx" ON "Payment"("organizationId");

-- CreateIndex
CREATE INDEX "Payment_status_createdAt_idx" ON "Payment"("status", "createdAt");

-- CreateIndex
CREATE INDEX "Payment_organizationId_status_createdAt_idx" ON "Payment"("organizationId", "status", "createdAt");

-- CreateIndex
CREATE INDEX "PaymentMethod_organizationId_isDefault_idx" ON "PaymentMethod"("organizationId", "isDefault");

-- CreateIndex
CREATE INDEX "PaymentMethod_organizationId_type_status_idx" ON "PaymentMethod"("organizationId", "type", "status");

-- CreateIndex
CREATE UNIQUE INDEX "RechargePlan_organizationId_key" ON "RechargePlan"("organizationId");

-- CreateIndex
CREATE INDEX "RechargePlan_organizationId_idx" ON "RechargePlan"("organizationId");

-- CreateIndex
CREATE INDEX "WalletTransaction_paymentId_idx" ON "WalletTransaction"("paymentId");

-- CreateIndex
CREATE INDEX "WalletTransaction_walletId_createdAt_idx" ON "WalletTransaction"("walletId", "createdAt");

-- CreateIndex
CREATE INDEX "WalletTransaction_walletId_category_idx" ON "WalletTransaction"("walletId", "category");

-- AddForeignKey
ALTER TABLE "WalletTransaction" ADD CONSTRAINT "WalletTransaction_paymentId_fkey" FOREIGN KEY ("paymentId") REFERENCES "Payment"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Payment" ADD CONSTRAINT "Payment_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Payment" ADD CONSTRAINT "Payment_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PaymentMethod" ADD CONSTRAINT "PaymentMethod_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RechargePlan" ADD CONSTRAINT "RechargePlan_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
