/*
  Warnings:

  - Made the column `carrierAccountId` on table `Refund` required. This step will fail if there are existing NULL values in that column.
  - Made the column `rate` on table `Refund` required. This step will fail if there are existing NULL values in that column.
  - Made the column `service` on table `Refund` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterTable
ALTER TABLE "Refund" ALTER COLUMN "carrierAccountId" SET NOT NULL,
ALTER COLUMN "rate" SET NOT NULL,
ALTER COLUMN "service" SET NOT NULL;

-- AlterTable
ALTER TABLE "Return" ADD COLUMN     "isLabelRefunded" BOOLEAN NOT NULL DEFAULT false;

-- AlterTable
ALTER TABLE "Shipment" ADD COLUMN     "isLabelRefunded" BOOLEAN NOT NULL DEFAULT false;

-- CreateIndex
CREATE INDEX "Refund_organizationId_shipmentId_idx" ON "Refund"("organizationId", "shipmentId");
