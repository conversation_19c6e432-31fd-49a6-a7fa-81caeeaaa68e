/*
  Warnings:

  - Made the column `organizationId` on table `Shipment` required. This step will fail if there are existing NULL values in that column.
  - Made the column `updatedAt` on table `Shipment` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterTable
ALTER TABLE "Shipment" ADD COLUMN     "fees" JSONB,
ADD COLUMN     "fromAddress" JSONB,
ADD COLUMN     "insurance" TEXT,
ADD COLUMN     "parcel" JSONB,
ADD COLUMN     "postageLabel" JSONB,
ADD COLUMN     "returnAddress" JSONB,
ADD COLUMN     "selectedRate" JSONB,
ADD COLUMN     "toAddress" JSONB,
ADD COLUMN     "trackingCode" TEXT,
ALTER COLUMN "organizationId" SET NOT NULL,
ALTER COLUMN "updatedAt" SET NOT NULL;
