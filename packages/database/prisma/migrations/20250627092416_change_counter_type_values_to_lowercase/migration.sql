/*
  Warnings:

  - The values [DROP_ID,ORDER,BATCH,SHIPMENT] on the enum `CounterType` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "CounterType_new" AS ENUM ('order', 'batch', 'shipment', 'return');
ALTER TABLE "Counter" ALTER COLUMN "type" TYPE "CounterType_new" USING ("type"::text::"CounterType_new");
ALTER TYPE "CounterType" RENAME TO "CounterType_old";
ALTER TYPE "CounterType_new" RENAME TO "CounterType";
DROP TYPE "CounterType_old";
COMMIT;
