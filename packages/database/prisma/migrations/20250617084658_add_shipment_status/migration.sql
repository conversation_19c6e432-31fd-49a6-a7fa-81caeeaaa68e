/*
  Warnings:

  - A unique constraint covering the columns `[epShipmentId]` on the table `Shipment` will be added. If there are existing duplicate values, this will fail.

*/
-- CreateEnum
CREATE TYPE "ShipmentStatus" AS ENUM ('unknown', 'pre_transit', 'in_transit', 'out_for_delivery', 'delivered', 'available_for_pickup', 'return_to_sender', 'failure', 'cancelled', 'error');

-- AlterTable
ALTER TABLE "Shipment" ADD COLUMN     "status" "ShipmentStatus" NOT NULL DEFAULT 'unknown';

-- CreateIndex
CREATE UNIQUE INDEX "Shipment_epShipmentId_key" ON "Shipment"("epShipmentId");
