-- CreateEnum
CREATE TYPE "ClaimStatus" AS ENUM ('submitted', 'in_review', 'approved', 'rejected', 'approved_partial', 'cancelled', 'needs_action');

-- CreateEnum
CREATE TYPE "ClaimType" AS ENUM ('loss', 'theft', 'damage');

-- CreateEnum
CREATE TYPE "ClaimAttachmentType" AS ENUM ('email_evidence', 'invoice', 'supporting_documents');

-- CreateTable
CREATE TABLE "Claim" (
    "id" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "shipmentId" TEXT,
    "returnId" TEXT,
    "userId" TEXT NOT NULL,
    "epClaimId" TEXT NOT NULL,
    "epInsuranceId" TEXT NOT NULL,
    "trackingCode" TEXT NOT NULL,
    "status" "ClaimStatus" NOT NULL,
    "type" "ClaimType" NOT NULL,
    "description" TEXT,
    "requestAmount" INTEGER NOT NULL,
    "insuranceAmount" INTEGER NOT NULL,
    "approvedAmount" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Claim_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ClaimAttachment" (
    "id" TEXT NOT NULL,
    "claimId" TEXT NOT NULL,
    "type" "ClaimAttachmentType" NOT NULL,
    "fileUrl" TEXT NOT NULL,
    "mimeType" TEXT NOT NULL,
    "fileName" TEXT,
    "fileSize" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ClaimAttachment_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Claim_shipmentId_key" ON "Claim"("shipmentId");

-- CreateIndex
CREATE UNIQUE INDEX "Claim_returnId_key" ON "Claim"("returnId");

-- CreateIndex
CREATE UNIQUE INDEX "Claim_epClaimId_key" ON "Claim"("epClaimId");

-- CreateIndex
CREATE UNIQUE INDEX "Claim_epInsuranceId_key" ON "Claim"("epInsuranceId");

-- CreateIndex
CREATE INDEX "Claim_organizationId_idx" ON "Claim"("organizationId");

-- CreateIndex
CREATE INDEX "ClaimAttachment_claimId_idx" ON "ClaimAttachment"("claimId");

-- AddForeignKey
ALTER TABLE "Claim" ADD CONSTRAINT "Claim_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Claim" ADD CONSTRAINT "Claim_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Claim" ADD CONSTRAINT "Claim_shipmentId_fkey" FOREIGN KEY ("shipmentId") REFERENCES "Shipment"("id") ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Claim" ADD CONSTRAINT "Claim_returnId_fkey" FOREIGN KEY ("returnId") REFERENCES "Return"("id") ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ClaimAttachment" ADD CONSTRAINT "ClaimAttachment_claimId_fkey" FOREIGN KEY ("claimId") REFERENCES "Claim"("id") ON DELETE CASCADE ON UPDATE CASCADE;
