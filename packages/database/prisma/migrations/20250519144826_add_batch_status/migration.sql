/*
  Warnings:

  - You are about to drop the column `numberOfOrders` on the `Batch` table. All the data in the column will be lost.

*/
-- CreateEnum
CREATE TYPE "BatchStatus" AS ENUM ('OPEN', 'SHIPPED', 'CANCELLED');

-- AlterTable
ALTER TABLE "Batch" DROP COLUMN "numberOfOrders",
ADD COLUMN     "status" "BatchStatus" NOT NULL DEFAULT 'OPEN';

-- CreateTable
CREATE TABLE "UserInviteToken" (
    "id" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "token" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "expires" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "UserInviteToken_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "UserInviteToken_token_key" ON "UserInviteToken"("token");

-- CreateIndex
CREATE UNIQUE INDEX "UserInviteToken_email_token_key" ON "UserInviteToken"("email", "token");
