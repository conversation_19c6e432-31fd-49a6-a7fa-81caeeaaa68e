/*
  Warnings:

  - Changed the type of `reason` on the `ReturnItem` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.

*/
-- CreateEnum
CREATE TYPE "ReturnReason" AS ENUM ('courtesy_return', 'ordered_wrong_item', 'warranty', 'changed_mind', 'received_wrong_item', 'rental', 'damaged', 'defective', 'arrived_too_late', 'missing_parts', 'not_as_described', 'other', 'exchange');

-- AlterTable
ALTER TABLE "Return" ADD COLUMN     "notes" TEXT;

-- AlterTable
ALTER TABLE "ReturnItem" DROP COLUMN "reason",
ADD COLUMN     "reason" "ReturnReason" NOT NULL;
