/*
  Warnings:

  - Added the required column `organizationId` to the `Address` table without a default value. This is not possible if the table is not empty.
  - Added the required column `organizationId` to the `Parcel` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "Store" AS ENUM ('DROPRIGHT');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "OrderStatus" AS ENUM ('OPEN', 'PROCESSING', 'SHIPPED', 'CANCELLED');

-- AlterTable
ALTER TABLE "Address" ADD COLUMN     "organizationId" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "Parcel" ADD COLUMN     "organizationId" TEXT NOT NULL;

-- CreateTable
CREATE TABLE "Order" (
    "id" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "batchId" TEXT,
    "orderNo" TEXT,
    "reference" TEXT,
    "status" "OrderStatus" NOT NULL DEFAULT 'OPEN',
    "fromAddress" JSONB NOT NULL,
    "toAddress" JSONB NOT NULL,
    "parcel" JSONB NOT NULL,
    "insurance" DOUBLE PRECISION DEFAULT 0,
    "items" JSONB NOT NULL,
    "tax" DOUBLE PRECISION DEFAULT 0,
    "notes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "store" "Store" NOT NULL DEFAULT 'DROPRIGHT',

    CONSTRAINT "Order_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Order_orderNo_key" ON "Order"("orderNo");

-- CreateIndex
CREATE INDEX "Order_organizationId_idx" ON "Order"("organizationId");

-- CreateIndex
CREATE INDEX "Order_organizationId_batchId_idx" ON "Order"("organizationId", "batchId");

-- CreateIndex
CREATE INDEX "Address_organizationId_idx" ON "Address"("organizationId");

-- CreateIndex
CREATE INDEX "Parcel_organizationId_idx" ON "Parcel"("organizationId");

-- AddForeignKey
ALTER TABLE "Parcel" ADD CONSTRAINT "Parcel_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Address" ADD CONSTRAINT "Address_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Order" ADD CONSTRAINT "Order_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;
