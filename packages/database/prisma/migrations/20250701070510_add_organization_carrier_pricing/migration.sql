/*
  Warnings:

  - You are about to drop the column `returnAddress` on the `Return` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "Return" DROP COLUMN "returnAddress",
ALTER COLUMN "epShipmentId" DROP NOT NULL,
ALTER COLUMN "trackingCode" DROP NOT NULL,
ALTER COLUMN "postageLabel" DROP NOT NULL,
ALTER COLUMN "carrier" DROP NOT NULL,
ALTER COLUMN "carrierAccountId" DROP NOT NULL,
ALTER COLUMN "rate" DROP NOT NULL,
ALTER COLUMN "service" DROP NOT NULL;

-- CreateTable
CREATE TABLE "OrganizationCarrierPricing" (
    "id" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "carrierId" TEXT NOT NULL,
    "percentageMarkup" DOUBLE PRECISION NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "OrganizationCarrierPricing_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "OrganizationCarrierPricing_organizationId_idx" ON "OrganizationCarrierPricing"("organizationId");

-- AddForeignKey
ALTER TABLE "OrganizationCarrierPricing" ADD CONSTRAINT "OrganizationCarrierPricing_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;
