/*
  Warnings:

  - You are about to drop the column `stripeCustomerId` on the `PaymentMethod` table. All the data in the column will be lost.
  - The `cardBrand` column on the `PaymentMethod` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - A unique constraint covering the columns `[stripeCustomerId]` on the table `Organization` will be added. If there are existing duplicate values, this will fail.

*/
-- CreateEnum
CREATE TYPE "CreditCardBrand" AS ENUM ('amex', 'cartes_bancaires', 'diners', 'discover', 'eftpos_au', 'jcb', 'link', 'mastercard', 'unionpay', 'visa', 'unknown');

-- AlterTable
ALTER TABLE "PaymentMethod" DROP COLUMN "stripeCustomerId",
ADD COLUMN     "cardFingerprint" TEXT,
DROP COLUMN "cardBrand",
ADD COLUMN     "cardBrand" "CreditCardBrand";

-- CreateIndex
CREATE UNIQUE INDEX "Organization_stripeCustomerId_key" ON "Organization"("stripeCustomerId");
