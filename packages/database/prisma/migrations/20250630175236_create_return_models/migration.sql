/*
  Warnings:

  - You are about to drop the column `dropId` on the `Return` table. All the data in the column will be lost.
  - You are about to drop the column `fromAddress` on the `Return` table. All the data in the column will be lost.
  - You are about to drop the column `printed` on the `Return` table. All the data in the column will be lost.
  - You are about to drop the column `selectedRate` on the `Return` table. All the data in the column will be lost.
  - Added the required column `carrier` to the `Return` table without a default value. This is not possible if the table is not empty.
  - Added the required column `carrierAccountId` to the `Return` table without a default value. This is not possible if the table is not empty.
  - Added the required column `fromAddressId` to the `Return` table without a default value. This is not possible if the table is not empty.
  - Added the required column `rate` to the `Return` table without a default value. This is not possible if the table is not empty.
  - Added the required column `service` to the `Return` table without a default value. This is not possible if the table is not empty.
  - Made the column `trackingCode` on table `Return` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterTable
ALTER TABLE "Return" DROP COLUMN "dropId",
DROP COLUMN "fromAddress",
DROP COLUMN "printed",
DROP COLUMN "selectedRate",
ADD COLUMN     "carrier" TEXT NOT NULL,
ADD COLUMN     "carrierAccountId" TEXT NOT NULL,
ADD COLUMN     "fromAddressId" TEXT NOT NULL,
ADD COLUMN     "insurance" TEXT,
ADD COLUMN     "labelPrinted" TIMESTAMP(3),
ADD COLUMN     "rate" TEXT NOT NULL,
ADD COLUMN     "service" TEXT NOT NULL,
ADD COLUMN     "shipDate" TIMESTAMP(3),
ADD COLUMN     "status" "ShipmentStatus" NOT NULL DEFAULT 'pre_transit',
ALTER COLUMN "trackingCode" SET NOT NULL,
ALTER COLUMN "fees" DROP NOT NULL;

-- CreateTable
CREATE TABLE "ReturnItem" (
    "id" SERIAL NOT NULL,
    "returnId" TEXT NOT NULL,
    "productId" TEXT NOT NULL,
    "quantity" INTEGER NOT NULL,
    "price" DOUBLE PRECISION NOT NULL,
    "reason" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ReturnItem_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "Return" ADD CONSTRAINT "Return_fromAddressId_fkey" FOREIGN KEY ("fromAddressId") REFERENCES "Address"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ReturnItem" ADD CONSTRAINT "ReturnItem_returnId_fkey" FOREIGN KEY ("returnId") REFERENCES "Return"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ReturnItem" ADD CONSTRAINT "ReturnItem_productId_fkey" FOREIGN KEY ("productId") REFERENCES "Product"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
