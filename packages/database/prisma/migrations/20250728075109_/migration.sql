/*
  Warnings:

  - You are about to drop the column `labelRefunded` on the `Return` table. All the data in the column will be lost.
  - You are about to drop the column `labelRefunded` on the `Shipment` table. All the data in the column will be lost.
  - You are about to drop the column `labelRefundedAt` on the `Shipment` table. All the data in the column will be lost.
  - You are about to drop the column `ratesRefetchedAt` on the `Shipment` table. All the data in the column will be lost.
  - You are about to drop the `Refund` table. If the table is not empty, all the data it contains will be lost.

*/
-- CreateEnum
CREATE TYPE "LabelRefundStatus" AS ENUM ('submitted', 'refunded', 'rejected');

-- AlterEnum
ALTER TYPE "ShipmentStatus" ADD VALUE 'voided';

-- DropForeignKey
ALTER TABLE "Refund" DROP CONSTRAINT "Refund_organizationId_fkey";

-- DropForeignKey
ALTER TABLE "Refund" DROP CONSTRAINT "Refund_returnId_fkey";

-- DropForeignKey
ALTER TABLE "Refund" DROP CONSTRAINT "Refund_shipmentId_fkey";

-- AlterTable
ALTER TABLE "Return" DROP COLUMN "labelRefunded",
ADD COLUMN     "replacementForId" TEXT,
ADD COLUMN     "voidedAt" TIMESTAMP(3);

-- AlterTable
ALTER TABLE "Shipment" DROP COLUMN "labelRefunded",
DROP COLUMN "labelRefundedAt",
DROP COLUMN "ratesRefetchedAt",
ADD COLUMN     "replacementForId" TEXT,
ADD COLUMN     "voidedAt" TIMESTAMP(3);

-- DropTable
DROP TABLE "Refund";

-- DropEnum
DROP TYPE "RefundStatus";

-- CreateTable
CREATE TABLE "LabelRefund" (
    "id" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "shipmentId" TEXT,
    "returnId" TEXT,
    "epRefundId" TEXT NOT NULL,
    "epShipmentId" TEXT NOT NULL,
    "trackingCode" TEXT NOT NULL,
    "status" "LabelRefundStatus" NOT NULL,
    "confirmationNumber" TEXT,
    "carrier" TEXT NOT NULL,
    "carrierAccountId" TEXT NOT NULL,
    "service" TEXT NOT NULL,
    "rate" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "LabelRefund_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "LabelRefund_organizationId_shipmentId_idx" ON "LabelRefund"("organizationId", "shipmentId");

-- AddForeignKey
ALTER TABLE "LabelRefund" ADD CONSTRAINT "LabelRefund_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LabelRefund" ADD CONSTRAINT "LabelRefund_shipmentId_fkey" FOREIGN KEY ("shipmentId") REFERENCES "Shipment"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LabelRefund" ADD CONSTRAINT "LabelRefund_returnId_fkey" FOREIGN KEY ("returnId") REFERENCES "Return"("id") ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Shipment" ADD CONSTRAINT "Shipment_replacementForId_fkey" FOREIGN KEY ("replacementForId") REFERENCES "Shipment"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Return" ADD CONSTRAINT "Return_replacementForId_fkey" FOREIGN KEY ("replacementForId") REFERENCES "Return"("id") ON DELETE SET NULL ON UPDATE CASCADE;
