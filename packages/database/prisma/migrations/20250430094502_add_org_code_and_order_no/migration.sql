-- Custom migration for your specific schema
-- This handles the orgCode (Int) and orderNo fields for existing data

-- Step 1: Add the orgCode column to Organization table as SERIAL
-- SERIAL is PostgreSQL's auto-incrementing integer type
ALTER TABLE "Organization" ADD COLUMN "orgCode" SERIAL;

-- Step 2: Make orgCode unique
ALTER TABLE "Organization" ADD CONSTRAINT "Organization_orgCode_key" UNIQUE ("orgCode");

-- Step 3: For orderNo, add it as nullable first
ALTER TABLE "Order" ADD COLUMN "orderNo" TEXT;

-- Step 4: Set a simple unique value for each existing order
-- Just using 'ORD-' followed by the internal ID
UPDATE "Order"
SET "orderNo" = 'ORD-' || id;

-- Step 5: Make orderNo required
ALTER TABLE "Order" ALTER COLUMN "orderNo" SET NOT NULL;

-- Step 6: Add an index on orderNo for better lookups
CREATE INDEX "Order_orderNo_idx" ON "Order"("orderNo");