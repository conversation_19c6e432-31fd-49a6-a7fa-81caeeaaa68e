/*
  Warnings:

  - A unique constraint covering the columns `[organizationId,type,date]` on the table `Counter` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `type` to the `Counter` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "CounterType" AS ENUM ('DROP_ID', 'ORDER');

-- AlterTable
ALTER TABLE "Counter" ADD COLUMN     "date" TIMESTAMP(3),
ADD COLUMN     "organizationId" TEXT,
ADD COLUMN     "type" "CounterType" NOT NULL;

-- CreateIndex
CREATE INDEX "Counter_organizationId_idx" ON "Counter"("organizationId");

-- CreateIndex
CREATE UNIQUE INDEX "Counter_organizationId_type_date_key" ON "Counter"("organizationId", "type", "date");

-- AddForeignKey
ALTER TABLE "Counter" ADD CONSTRAINT "Counter_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE SET NULL ON UPDATE CASCADE;
