/*
  Warnings:

  - A unique constraint covering the columns `[orderId]` on the table `Shipment` will be added. If there are existing duplicate values, this will fail.
  - Made the column `name` on table `Address` required. This step will fail if there are existing NULL values in that column.
  - Made the column `epAddressId` on table `Address` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterTable
ALTER TABLE "Address" ALTER COLUMN "name" SET NOT NULL,
ALTER COLUMN "epAddressId" SET NOT NULL;

-- AlterTable
ALTER TABLE "Shipment" ADD COLUMN     "printed" BOOLEAN NOT NULL DEFAULT false;

-- CreateIndex
CREATE UNIQUE INDEX "Shipment_orderId_key" ON "Shipment"("orderId");
