/*
  Warnings:

  - The values [<PERSON><PERSON><PERSON>,<PERSON><PERSON>PED,<PERSON><PERSON><PERSON>LED] on the enum `BatchStatus` will be removed. If these variants are still used in the database, this will fail.
  - The values [OPEN,PROCESSING,SHIPPED,CANC<PERSON>LED] on the enum `OrderStatus` will be removed. If these variants are still used in the database, this will fail.
  - The values [PENDING,EXPIRED,REJECTED] on the enum `UserInviteStatus` will be removed. If these variants are still used in the database, this will fail.
  - The values [ACTIVE,DEACTIVATED] on the enum `UserStatus` will be removed. If these variants are still used in the database, this will fail.
  - You are about to drop the column `batchName` on the `Order` table. All the data in the column will be lost.
  - You are about to drop the column `carrier` on the `Order` table. All the data in the column will be lost.
  - You are about to drop the column `dropId` on the `Order` table. All the data in the column will be lost.
  - You are about to drop the column `fromAddress` on the `Order` table. All the data in the column will be lost.
  - You are about to drop the column `items` on the `Order` table. All the data in the column will be lost.
  - You are about to drop the column `orderRef` on the `Order` table. All the data in the column will be lost.
  - You are about to drop the column `packageType` on the `Order` table. All the data in the column will be lost.
  - You are about to drop the column `store` on the `Order` table. All the data in the column will be lost.
  - You are about to drop the column `dropId` on the `Shipment` table. All the data in the column will be lost.
  - You are about to drop the column `fromAddress` on the `Shipment` table. All the data in the column will be lost.
  - You are about to drop the column `printed` on the `Shipment` table. All the data in the column will be lost.
  - You are about to drop the column `selectedRate` on the `Shipment` table. All the data in the column will be lost.
  - You are about to drop the column `role` on the `User` table. All the data in the column will be lost.
  - You are about to drop the column `invitedBy` on the `UserInvite` table. All the data in the column will be lost.
  - You are about to drop the column `permissionIds` on the `UserInvite` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[email,token]` on the table `PasswordResetToken` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `fromAddressId` to the `Order` table without a default value. This is not possible if the table is not empty.
  - Added the required column `storeId` to the `Order` table without a default value. This is not possible if the table is not empty.
  - Added the required column `carrier` to the `Shipment` table without a default value. This is not possible if the table is not empty.
  - Added the required column `carrierAccountId` to the `Shipment` table without a default value. This is not possible if the table is not empty.
  - Added the required column `fromAddressId` to the `Shipment` table without a default value. This is not possible if the table is not empty.
  - Added the required column `rate` to the `Shipment` table without a default value. This is not possible if the table is not empty.
  - Added the required column `service` to the `Shipment` table without a default value. This is not possible if the table is not empty.
  - Made the column `parcel` on table `Shipment` required. This step will fail if there are existing NULL values in that column.
  - Made the column `postageLabel` on table `Shipment` required. This step will fail if there are existing NULL values in that column.
  - Made the column `returnAddress` on table `Shipment` required. This step will fail if there are existing NULL values in that column.
  - Made the column `toAddress` on table `Shipment` required. This step will fail if there are existing NULL values in that column.
  - Made the column `trackingCode` on table `Shipment` required. This step will fail if there are existing NULL values in that column.
  - Added the required column `invitedByUserId` to the `UserInvite` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "StoreProvider" AS ENUM ('dropright', 'shipstation', 'shopify', 'woocommerce', 'etsy', 'amazon', 'ebay', 'tiktok');

-- CreateEnum
CREATE TYPE "StoreStatus" AS ENUM ('active', 'inactive');

-- AlterEnum
BEGIN;
CREATE TYPE "BatchStatus_new" AS ENUM ('open', 'shipped', 'cancelled');
ALTER TABLE "Batch" ALTER COLUMN "status" DROP DEFAULT;
ALTER TABLE "Batch" ALTER COLUMN "status" TYPE "BatchStatus_new" USING ("status"::text::"BatchStatus_new");
ALTER TYPE "BatchStatus" RENAME TO "BatchStatus_old";
ALTER TYPE "BatchStatus_new" RENAME TO "BatchStatus";
DROP TYPE "BatchStatus_old";
ALTER TABLE "Batch" ALTER COLUMN "status" SET DEFAULT 'open';
COMMIT;

-- AlterEnum
BEGIN;
CREATE TYPE "OrderStatus_new" AS ENUM ('open', 'processing', 'shipped', 'cancelled');
ALTER TABLE "Order" ALTER COLUMN "status" DROP DEFAULT;
ALTER TABLE "Order" ALTER COLUMN "status" TYPE "OrderStatus_new" USING ("status"::text::"OrderStatus_new");
ALTER TYPE "OrderStatus" RENAME TO "OrderStatus_old";
ALTER TYPE "OrderStatus_new" RENAME TO "OrderStatus";
DROP TYPE "OrderStatus_old";
ALTER TABLE "Order" ALTER COLUMN "status" SET DEFAULT 'open';
COMMIT;

-- AlterEnum
BEGIN;
CREATE TYPE "UserInviteStatus_new" AS ENUM ('pending', 'rejected');
ALTER TABLE "UserInvite" ALTER COLUMN "status" DROP DEFAULT;
ALTER TABLE "UserInvite" ALTER COLUMN "status" TYPE "UserInviteStatus_new" USING ("status"::text::"UserInviteStatus_new");
ALTER TYPE "UserInviteStatus" RENAME TO "UserInviteStatus_old";
ALTER TYPE "UserInviteStatus_new" RENAME TO "UserInviteStatus";
DROP TYPE "UserInviteStatus_old";
ALTER TABLE "UserInvite" ALTER COLUMN "status" SET DEFAULT 'pending';
COMMIT;

-- AlterEnum
BEGIN;
CREATE TYPE "UserStatus_new" AS ENUM ('active', 'deactivated');
ALTER TABLE "User" ALTER COLUMN "status" DROP DEFAULT;
ALTER TABLE "User" ALTER COLUMN "status" TYPE "UserStatus_new" USING ("status"::text::"UserStatus_new");
ALTER TYPE "UserStatus" RENAME TO "UserStatus_old";
ALTER TYPE "UserStatus_new" RENAME TO "UserStatus";
DROP TYPE "UserStatus_old";
ALTER TABLE "User" ALTER COLUMN "status" SET DEFAULT 'active';
COMMIT;

-- DropForeignKey
ALTER TABLE "Parcel" DROP CONSTRAINT "Parcel_organizationId_fkey";

-- DropIndex
DROP INDEX "Parcel_organizationId_idx";

-- DropIndex
DROP INDEX "Shipment_shipmentNo_key";

-- AlterTable
ALTER TABLE "Batch" ALTER COLUMN "status" SET DEFAULT 'open';

-- AlterTable
ALTER TABLE "Order" DROP COLUMN "batchName",
DROP COLUMN "carrier",
DROP COLUMN "dropId",
DROP COLUMN "fromAddress",
DROP COLUMN "items",
DROP COLUMN "orderRef",
DROP COLUMN "packageType",
DROP COLUMN "store",
ADD COLUMN     "fromAddressId" TEXT NOT NULL,
ADD COLUMN     "storeId" TEXT NOT NULL,
ALTER COLUMN "status" SET DEFAULT 'open',
ALTER COLUMN "tax" DROP DEFAULT,
ALTER COLUMN "signature" DROP NOT NULL,
ALTER COLUMN "signature" DROP DEFAULT,
ALTER COLUMN "signature" SET DATA TYPE TEXT;

-- AlterTable
ALTER TABLE "Parcel" ALTER COLUMN "length" DROP NOT NULL,
ALTER COLUMN "width" DROP NOT NULL,
ALTER COLUMN "height" DROP NOT NULL;

-- AlterTable
ALTER TABLE "Shipment" DROP COLUMN "dropId",
DROP COLUMN "fromAddress",
DROP COLUMN "printed",
DROP COLUMN "selectedRate",
ADD COLUMN     "carrier" TEXT NOT NULL,
ADD COLUMN     "carrierAccountId" TEXT NOT NULL,
ADD COLUMN     "fromAddressId" TEXT NOT NULL,
ADD COLUMN     "labelPrinted" TIMESTAMP(3),
ADD COLUMN     "rate" TEXT NOT NULL,
ADD COLUMN     "service" TEXT NOT NULL,
ADD COLUMN     "shipDate" TIMESTAMP(3),
ALTER COLUMN "parcel" SET NOT NULL,
ALTER COLUMN "postageLabel" SET NOT NULL,
ALTER COLUMN "returnAddress" SET NOT NULL,
ALTER COLUMN "toAddress" SET NOT NULL,
ALTER COLUMN "trackingCode" SET NOT NULL,
ALTER COLUMN "status" SET DEFAULT 'pre_transit';

-- AlterTable
ALTER TABLE "User" DROP COLUMN "role",
ALTER COLUMN "status" SET DEFAULT 'active';

-- AlterTable
ALTER TABLE "UserInvite" DROP COLUMN "invitedBy",
DROP COLUMN "permissionIds",
ADD COLUMN     "invitedByUserId" TEXT NOT NULL,
ALTER COLUMN "status" SET DEFAULT 'pending';

-- DropEnum
DROP TYPE "Store";

-- DropEnum
DROP TYPE "UserRole";

-- CreateTable
CREATE TABLE "Store" (
    "id" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "provider" "StoreProvider" NOT NULL,
    "status" "StoreStatus" NOT NULL,
    "storeUrl" TEXT,
    "apiKey" TEXT,
    "apiSecret" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Store_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Product" (
    "id" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "storeId" TEXT,
    "sku" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "basePrice" DOUBLE PRECISION,
    "weight" DOUBLE PRECISION,
    "imageUrl" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Product_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "StoreProduct" (
    "id" TEXT NOT NULL,
    "storeId" TEXT NOT NULL,
    "productId" TEXT NOT NULL,
    "externalId" TEXT NOT NULL,
    "externalSku" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "StoreProduct_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "OrderItem" (
    "id" SERIAL NOT NULL,
    "orderId" TEXT NOT NULL,
    "productId" TEXT NOT NULL,
    "quantity" INTEGER NOT NULL,
    "price" DOUBLE PRECISION NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "OrderItem_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BillingAddress" (
    "id" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "name" TEXT,
    "company" TEXT,
    "street1" TEXT NOT NULL,
    "street2" TEXT,
    "city" TEXT NOT NULL,
    "state" TEXT,
    "country" TEXT NOT NULL,
    "zip" TEXT NOT NULL,
    "phone" TEXT,
    "email" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "BillingAddress_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UserInvitePermission" (
    "id" TEXT NOT NULL,
    "userInviteId" TEXT NOT NULL,
    "permissionId" TEXT NOT NULL,

    CONSTRAINT "UserInvitePermission_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "Store_organizationId_idx" ON "Store"("organizationId");

-- CreateIndex
CREATE INDEX "Product_organizationId_idx" ON "Product"("organizationId");

-- CreateIndex
CREATE UNIQUE INDEX "BillingAddress_organizationId_key" ON "BillingAddress"("organizationId");

-- CreateIndex
CREATE UNIQUE INDEX "UserInvitePermission_userInviteId_permissionId_key" ON "UserInvitePermission"("userInviteId", "permissionId");

-- CreateIndex
CREATE INDEX "PasswordResetToken_expires_idx" ON "PasswordResetToken"("expires");

-- CreateIndex
CREATE UNIQUE INDEX "PasswordResetToken_email_token_key" ON "PasswordResetToken"("email", "token");

-- CreateIndex
CREATE INDEX "User_organizationId_idx" ON "User"("organizationId");

-- CreateIndex
CREATE INDEX "User_email_idx" ON "User"("email");

-- CreateIndex
CREATE INDEX "VerificationToken_expires_idx" ON "VerificationToken"("expires");

-- AddForeignKey
ALTER TABLE "Store" ADD CONSTRAINT "Store_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Product" ADD CONSTRAINT "Product_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "StoreProduct" ADD CONSTRAINT "StoreProduct_storeId_fkey" FOREIGN KEY ("storeId") REFERENCES "Store"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "StoreProduct" ADD CONSTRAINT "StoreProduct_productId_fkey" FOREIGN KEY ("productId") REFERENCES "Product"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Order" ADD CONSTRAINT "Order_storeId_fkey" FOREIGN KEY ("storeId") REFERENCES "Store"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Order" ADD CONSTRAINT "Order_fromAddressId_fkey" FOREIGN KEY ("fromAddressId") REFERENCES "Address"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OrderItem" ADD CONSTRAINT "OrderItem_orderId_fkey" FOREIGN KEY ("orderId") REFERENCES "Order"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OrderItem" ADD CONSTRAINT "OrderItem_productId_fkey" FOREIGN KEY ("productId") REFERENCES "Product"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BillingAddress" ADD CONSTRAINT "BillingAddress_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Parcel" ADD CONSTRAINT "Parcel_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Shipment" ADD CONSTRAINT "Shipment_fromAddressId_fkey" FOREIGN KEY ("fromAddressId") REFERENCES "Address"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserInvite" ADD CONSTRAINT "UserInvite_invitedByUserId_fkey" FOREIGN KEY ("invitedByUserId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserInvitePermission" ADD CONSTRAINT "UserInvitePermission_userInviteId_fkey" FOREIGN KEY ("userInviteId") REFERENCES "UserInvite"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserInvitePermission" ADD CONSTRAINT "UserInvitePermission_permissionId_fkey" FOREIGN KEY ("permissionId") REFERENCES "Permission"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
