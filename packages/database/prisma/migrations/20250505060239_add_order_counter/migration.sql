/*
  Warnings:

  - A unique constraint covering the columns `[organizationId,orderNo]` on the table `Order` will be added. If there are existing duplicate values, this will fail.

*/
-- CreateTable
CREATE TABLE "OrderCounter" (
    "id" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "date" TIMESTAMP(3) NOT NULL,
    "count" INTEGER NOT NULL,

    CONSTRAINT "OrderCounter_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "OrderCounter_organizationId_idx" ON "OrderCounter"("organizationId");

-- CreateIndex
CREATE UNIQUE INDEX "OrderCounter_organizationId_date_key" ON "OrderCounter"("organizationId", "date");

-- CreateIndex
CREATE UNIQUE INDEX "Order_organizationId_orderNo_key" ON "Order"("organizationId", "orderNo");

-- AddForeignKey
ALTER TABLE "OrderCounter" ADD CONSTRAINT "OrderCounter_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
