/*
  Warnings:

  - The values [shipped,cancelled] on the enum `BatchStatus` will be removed. If these variants are still used in the database, this will fail.
  - A unique constraint covering the columns `[organizationId,carrierId]` on the table `OrganizationCarrierPricing` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "BatchStatus_new" AS ENUM ('open', 'processed', 'archived');
ALTER TABLE "Batch" ALTER COLUMN "status" DROP DEFAULT;
ALTER TABLE "Batch" ALTER COLUMN "status" TYPE "BatchStatus_new" USING ("status"::text::"BatchStatus_new");
ALTER TYPE "BatchStatus" RENAME TO "BatchStatus_old";
ALTER TYPE "BatchStatus_new" RENAME TO "BatchStatus";
DROP TYPE "BatchStatus_old";
ALTER TABLE "Batch" ALTER COLUMN "status" SET DEFAULT 'open';
COMMIT;

-- DropIndex
DROP INDEX "Order_organizationId_orderNo_key";

-- AlterTable
ALTER TABLE "OrganizationCarrierPricing" ADD COLUMN     "enabled" BOOLEAN NOT NULL DEFAULT true;

-- CreateIndex
CREATE UNIQUE INDEX "OrganizationCarrierPricing_organizationId_carrierId_key" ON "OrganizationCarrierPricing"("organizationId", "carrierId");
