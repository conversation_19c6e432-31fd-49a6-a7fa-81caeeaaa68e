/*
  Warnings:

  - You are about to drop the column `fromAddressId` on the `Return` table. All the data in the column will be lost.
  - You are about to drop the column `toAddress` on the `Return` table. All the data in the column will be lost.
  - Added the required column `fromAddress` to the `Return` table without a default value. This is not possible if the table is not empty.
  - Added the required column `toAddressId` to the `Return` table without a default value. This is not possible if the table is not empty.
  - Made the column `trackingCode` on table `Return` required. This step will fail if there are existing NULL values in that column.
  - Made the column `postageLabel` on table `Return` required. This step will fail if there are existing NULL values in that column.
  - Made the column `carrier` on table `Return` required. This step will fail if there are existing NULL values in that column.
  - Made the column `carrierAccountId` on table `Return` required. This step will fail if there are existing NULL values in that column.
  - Made the column `rate` on table `Return` required. This step will fail if there are existing NULL values in that column.
  - Made the column `service` on table `Return` required. This step will fail if there are existing NULL values in that column.

*/
-- DropForeignKey
ALTER TABLE "Return" DROP CONSTRAINT "Return_fromAddressId_fkey";

-- AlterTable
ALTER TABLE "Return" DROP COLUMN "fromAddressId",
DROP COLUMN "toAddress",
ADD COLUMN     "fromAddress" JSONB NOT NULL,
ADD COLUMN     "toAddressId" TEXT NOT NULL,
ALTER COLUMN "trackingCode" SET NOT NULL,
ALTER COLUMN "postageLabel" SET NOT NULL,
ALTER COLUMN "carrier" SET NOT NULL,
ALTER COLUMN "carrierAccountId" SET NOT NULL,
ALTER COLUMN "rate" SET NOT NULL,
ALTER COLUMN "service" SET NOT NULL;

-- AddForeignKey
ALTER TABLE "Return" ADD CONSTRAINT "Return_toAddressId_fkey" FOREIGN KEY ("toAddressId") REFERENCES "Address"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
