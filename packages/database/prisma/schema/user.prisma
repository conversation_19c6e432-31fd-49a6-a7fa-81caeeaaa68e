enum UserStatus {
    active
    deactivated
}

enum UserInviteStatus {
    pending
    rejected
}

model Organization {
    id             String          @id @default(cuid())
    orgCode        Int             @unique @default(autoincrement())
    name           String
    phone          String
    shipmentVolume String
    billingAddress BillingAddress?

    // Payment
    stripeCustomerId String? @unique

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    // Required 1 to 1. Created along with organization
    walletId String @unique
    wallet   Wallet @relation(fields: [walletId], references: [id])

    users                      User[]
    Order                      Order[]
    Shipment                   Shipment[]
    Return                     Return[]
    Batch                      Batch[]
    Address                    Address[]
    Parcel                     Parcel[]
    Counter                    Counter[]
    UserInvite                 UserInvite[]
    Product                    Product[]
    Store                      Store[]
    OrganizationCarrierPricing OrganizationCarrierPricing[]
    Claim                      Claim[]
    LabelRefund                LabelRefund[]
    payments                   Payment[]
    paymentMethods             PaymentMethod[]
    rechargePlan               RechargePlan?
}

model User {
    id             String     @id @default(cuid())
    organizationId String
    firstName      String
    lastName       String
    email          String     @unique
    emailVerified  DateTime?
    image          String?
    password       String?
    status         UserStatus @default(active)

    accounts    Account[]
    permissions UserPermission[]
    userInvites UserInvite[]
    claims      Claim[]
    Payments    Payment[]

    // Optional for WebAuthn support
    // Authenticator Authenticator[]
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    organization Organization @relation(fields: [organizationId], references: [id])

    @@index([organizationId])
    @@index([email])
}

model Account {
    userId            String
    type              String
    provider          String
    providerAccountId String
    refresh_token     String?
    access_token      String?
    expires_at        Int?
    token_type        String?
    scope             String?
    id_token          String?
    session_state     String?

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    user User @relation(fields: [userId], references: [id], onDelete: Cascade)

    @@id([provider, providerAccountId])
}

model Permission {
    id          String  @id @default(cuid())
    name        String  @unique //ex order, shipment, configuration, finance
    description String?

    users       UserPermission[]
    userInvites UserInvitePermission[]
}

model UserPermission {
    id           String @id @default(cuid())
    userId       String
    permissionId String

    user       User       @relation(fields: [userId], references: [id], onDelete: Cascade)
    permission Permission @relation(fields: [permissionId], references: [id])

    @@unique([userId, permissionId])
}

model UserInvite {
    id              String           @id @default(cuid())
    organizationId  String
    email           String
    token           String           @unique
    expires         DateTime
    status          UserInviteStatus @default(pending)
    invitedByUserId String

    permissions UserInvitePermission[]

    createdAt DateTime @default(now())

    organization Organization @relation(fields: [organizationId], references: [id])
    user         User         @relation(fields: [invitedByUserId], references: [id])

    @@unique([organizationId, email])
}

model UserInvitePermission {
    id           String @id @default(cuid())
    userInviteId String
    permissionId String

    userInvite UserInvite @relation(fields: [userInviteId], references: [id], onDelete: Cascade)
    permission Permission @relation(fields: [permissionId], references: [id])

    @@unique([userInviteId, permissionId])
}

model VerificationToken {
    id      String   @id @default(cuid())
    email   String
    token   String   @unique
    expires DateTime

    @@unique([email, token])
    @@index([expires]) // For cleanup
}

model PasswordResetToken {
    id      String   @id @default(cuid())
    email   String
    token   String   @unique
    expires DateTime

    @@unique([email, token])
    @@index([expires]) // For cleanup
}

model OrganizationCarrierPricing {
    id               String  @id @default(cuid())
    organizationId   String
    carrierId        String
    percentageMarkup Float
    enabled          Boolean @default(true)

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

    @@unique([organizationId, carrierId])
    @@index([organizationId])
}
