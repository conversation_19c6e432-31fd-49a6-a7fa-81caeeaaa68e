enum CounterType {
    order
    batch
    shipment
    return
    claim
}

// For all counters
model Counter {
    id             String      @id @default(cuid())
    organizationId String?
    // For order, shipment, return type, date is set to start of day (daily reset)
    // For batch and claim type, date is set to first day of month (monthly reset)
    date           DateTime?
    count          Int
    type           CounterType

    organization Organization? @relation(fields: [organizationId], references: [id])

    @@unique([organizationId, type, date])
    @@index([organizationId])
}
