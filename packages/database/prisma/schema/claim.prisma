enum ClaimStatus {
    submitted
    in_review
    approved
    rejected
    approved_partial
    cancelled
    needs_action
}

enum ClaimType {
    loss
    theft
    damage
}

enum ClaimAttachmentType {
    email_evidence
    invoice
    supporting_documents
}

model Claim {
    id             String  @id @default(cuid())
    organizationId String
    claimNo        String
    userId         String
    shipmentId     String?
    returnId       String?
    reference      String?

    epClaimId     String @unique
    epInsuranceId String

    trackingCode String
    status       ClaimStatus
    type         ClaimType
    description  String

    requestAmount   String
    insuranceAmount String
    approvedAmount  String?

    attachments ClaimAttachment[]

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    organization Organization @relation(fields: [organizationId], references: [id])
    user         User         @relation(fields: [userId], references: [id])

    shipment Shipment? @relation(fields: [shipmentId], references: [id], onDelete: NoAction)
    return   Return?   @relation(fields: [returnId], references: [id], onDelete: NoAction)

    @@index([organizationId])
    @@index([shipmentId, status])
    @@index([returnId, status])
}

model ClaimAttachment {
    id       String              @id @default(cuid())
    claimId  String
    type     ClaimAttachmentType
    fileUrl  String
    mimeType String
    fileName String?
    fileSize Int?
    key      String

    createdAt DateTime @default(now())

    claim Claim @relation(fields: [claimId], references: [id], onDelete: Cascade)

    @@index([claimId])
}
