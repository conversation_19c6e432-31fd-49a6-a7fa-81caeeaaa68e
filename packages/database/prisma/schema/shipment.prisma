enum ShipmentStatus {
  unknown
  pre_transit
  in_transit
  out_for_delivery
  delivered
  available_for_pickup
  return_to_sender
  failure
  cancelled
  error

  voided
}

model Shipment {
  id             String         @id @default(cuid())
  organizationId String
  shipmentNo     String
  orderId        String
  epShipmentId   String         @unique
  status         ShipmentStatus @default(pre_transit)

  fromAddressId    String
  toAddress        Json
  returnAddress    Json
  parcel           Json
  carrier          String
  carrierAccountId String
  service          String

  rate         String
  labelPrinted DateTime?
  trackingCode String
  shipDate     DateTime?
  postageLabel Json

  replacementForId String? // If shipment is voided, this is the id of the shipment that replaced it
  voidedAt         DateTime?

  insurance String?
  fees      Json?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Self-referential relationship for voided shipments
  replacementFor  Shipment?  @relation("ShipmentReplacement", fields: [replacementForId], references: [id])
  voidedShipments Shipment[] @relation("ShipmentReplacement")

  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  order        Order        @relation(fields: [orderId], references: [id])
  fromAddress  Address      @relation(fields: [fromAddressId], references: [id])

  claims       Claim[]
  labelRefunds LabelRefund[]

  @@index([organizationId])
}

model Return {
  id             String         @id @default(cuid())
  organizationId String
  rma            String
  orderId        String
  epShipmentId   String         @unique
  status         ShipmentStatus @default(pre_transit)
  orderItems     ReturnItem[] // TODO: Change to returnItems
  notes          String?

  fromAddress      Json // Customer address
  toAddressId      String // Warehouse address
  parcel           Json
  carrier          String
  carrierAccountId String
  service          String

  rate         String
  labelPrinted DateTime?
  trackingCode String
  shipDate     DateTime?
  postageLabel Json

  // Add void/recreate pattern
  replacementForId String?
  voidedAt         DateTime?

  insurance String?
  fees      Json? // Do we need this? Price breakdown (ex. postage, label fee, insurance)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Self-referential relationship  
  replacementFor Return?  @relation("ReturnReplacement", fields: [replacementForId], references: [id])
  voidedReturns  Return[] @relation("ReturnReplacement")

  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  order        Order        @relation(fields: [orderId], references: [id])
  toAddress    Address      @relation(fields: [toAddressId], references: [id])

  claims       Claim[]
  labelRefunds LabelRefund[]

  @@index([organizationId])
}

enum ReturnReason {
  courtesy_return
  ordered_wrong_item
  warranty
  changed_mind
  received_wrong_item
  rental
  damaged
  defective
  arrived_too_late
  missing_parts
  not_as_described
  other
  exchange
}

model ReturnItem {
  id        Int          @id @default(autoincrement())
  returnId  String
  productId String
  quantity  Int
  price     Float
  reason    ReturnReason

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  return  Return  @relation(fields: [returnId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id])
}
