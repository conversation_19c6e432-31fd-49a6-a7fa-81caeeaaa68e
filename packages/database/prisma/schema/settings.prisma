model Address {
    id             String @id @default(cuid())
    epAddressId    String // EasyPost address ID. Retrieved when address is created
    organizationId String

    name        String
    company     String?
    street1     String
    street2     String?
    city        String
    state       String
    country     String
    zip         String
    phone       String
    email       String
    residential Boolean @default(false)
    verified    <PERSON><PERSON><PERSON> @default(false)

    organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    orders       Order[]
    shipments    Shipment[]
    returns      Return[]

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@index([organizationId])
}

model BillingAddress {
    id             String  @id @default(cuid())
    organizationId String  @unique
    name           String?
    company        String?
    street1        String
    street2        String?
    city           String
    state          String?
    country        String
    zip            String
    phone          String?
    email          String?

    organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt
}

model Parcel {
    id                String  @id @default(cuid())
    organizationId    String
    name              String
    length            Float?
    width             Float?
    height            Float?
    weight            Float
    predefinedPackage String?

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    Organization Organization @relation(fields: [organizationId], references: [id])
}
