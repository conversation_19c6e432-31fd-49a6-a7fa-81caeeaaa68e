enum LabelRefundStatus {
    submitted
    refunded
    rejected
}

// Might not need anymore because one shipment is refunded, you need to create a new shipment
model LabelRefund {
    id             String  @id @default(cuid())
    organizationId String
    shipmentId     String?
    returnId       String?

    epRefundId   String
    epShipmentId String // just for reference

    trackingCode       String
    status             LabelRefundStatus
    confirmationNumber String?

    carrier          String
    carrierAccountId String
    service          String
    rate             String

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    organization Organization @relation(fields: [organizationId], references: [id])
    shipment     Shipment?    @relation(fields: [shipmentId], references: [id], onDelete: Cascade)
    return       Return?      @relation(fields: [returnId], references: [id], onDelete: NoAction)

    @@index([organizationId, shipmentId])
}
