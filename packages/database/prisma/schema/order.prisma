enum OrderStatus {
    open
    processing
    shipped
    cancelled
}

/**
 * If batch is open, can be deleted
 * If batch is processed, can be archived (cannot be deleted)
 * If batch is archived, batch cannot be modified or perform batch level actions
 */
enum BatchStatus {
    open
    processed
    archived
}

model Order {
    id             String      @id @default(cuid())
    orderNo        String
    organizationId String
    epShipmentId   String? // EasyPost shipment ID. Retrieved when order is created to get rates
    batchId        String?
    storeId        String
    orderDate      DateTime    @default(now())
    status         OrderStatus @default(open)
    carrier        String? // TODO: Change to enum
    service        String?
    fromAddressId  String
    toAddress      Json // One-off destination
    notes          String?
    parcel         Json
    // insurance      Decimal?    @default(0.00) @db.Decimal(6, 2)
    insurance      Float?      @default(0)
    signature      String?
    // tax            Float? // ?????

    // For handling split orders
    isSplit       Boolean @default(false)
    parentOrderId String?

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    orderItems OrderItem[]

    // Self-referential relationship for split orders
    parentOrder Order?  @relation("SplitOrders", fields: [parentOrderId], references: [id])
    childOrders Order[] @relation("SplitOrders")

    organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    store        Store        @relation(fields: [storeId], references: [id])
    batch        Batch?       @relation(fields: [batchId], references: [id], onDelete: SetNull)
    fromAddress  Address      @relation(fields: [fromAddressId], references: [id])

    shipment Shipment[]
    return   Return[]

    // @@unique([organizationId, orderNo])
    @@index([organizationId])
    @@index([organizationId, batchId])
}

model OrderItem {
    id        Int    @id @default(autoincrement())
    orderId   String
    productId String
    quantity  Int
    price     Float

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    order   Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
    product Product @relation(fields: [productId], references: [id])
}

model Batch {
    id             String      @id @default(cuid())
    organizationId String
    batchNo        String
    name           String
    notes          String?
    status         BatchStatus @default(open)

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    orders       Order[]

    @@unique([organizationId, batchNo])
    @@index([organizationId])
}
