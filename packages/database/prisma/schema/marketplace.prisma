enum StoreProvider {
    dropright
    shipstation
    shopify
    woocommerce
    etsy
    amazon
    ebay
    tiktok
}

enum StoreStatus {
    active
    inactive
}

model Store {
    id             String        @id @default(cuid())
    organizationId String
    name           String
    provider       StoreProvider
    status         StoreStatus   @default(active)
    storeUrl       String?

    apiKey    String?
    apiSecret String?
    // TODO: Other AUTHENTICATION KEYS

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    organization  Organization   @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    orders        Order[]
    storeProducts StoreProduct[]

    @@index([organizationId])
}

model Product {
    id             String  @id @default(cuid())
    organizationId String
    sku            String
    name           String
    description    String?
    basePrice      Float?
    weight         Float?
    imageUrl       String?

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    organization  Organization   @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    storeProducts StoreProduct[]
    orderItems    OrderItem[]
    returnItems   ReturnItem[]

    @@unique([organizationId, sku])
    @@index([organizationId])
}

model StoreProduct {
    id          String @id @default(cuid())
    storeId     String
    productId   String
    externalId  String
    externalSku String

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    store   Store   @relation(fields: [storeId], references: [id])
    product Product @relation(fields: [productId], references: [id])
}
