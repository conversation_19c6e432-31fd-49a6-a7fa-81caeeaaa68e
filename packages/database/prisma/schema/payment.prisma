enum Currency {
    usd
}

model Wallet {
    id       String   @id @default(cuid())
    balance  Decimal  @default(0.00) @db.Decimal(12, 4)
    reserved Decimal  @default(0.00) @db.Decimal(12, 4)
    currency Currency @default(usd)

    // Relations
    organization Organization?
    transactions WalletTransaction[]

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt
}

enum WalletTransactionType {
    // CREDITS
    topup_payment // manual wallet recharge
    topup_auto // automatic recharge via recharge plan
    refund_cancelled_label // unused/cancelled shipping labels
    adjustment_credit // manual credit adjustments (admin corrections)
    // DEBIT
    label_purchase
    label_adjustment
    service_fee
    adjustment_debit // manual debit adjustment(admin corrections)
    // FUTURE
    // subscription_fee
    // chargeback
}

enum WalletTransactionCategory {
    settled // Final money movement (credits/debits)
    reserved // Holding funds temporarily  
    released // Releasing previously held funds
    returned // Returning served funds back to the wallet
}

model WalletTransaction {
    id        String  @id @default(cuid())
    walletId  String
    paymentId String?

    type     WalletTransactionType
    category WalletTransactionCategory @default(settled)

    // Balance tracking
    balanceChange    Decimal? @db.Decimal(12, 4) // Balance change (+/-)
    previousBalance  Decimal? @db.Decimal(12, 4) // Balance before
    newBalance       Decimal? @db.Decimal(12, 4) // Balance after
    // Reserved tracking (nullable - not all transactions affect reserves)
    reservedChange   Decimal? @db.Decimal(12, 4) // Reserved change (+/-)
    previousReserved Decimal? @db.Decimal(12, 4) // Reserved before  
    newReserved      Decimal? @db.Decimal(12, 4) // Reserved after

    referenceType String? // 'payment', 'label', etc.
    referenceId   String? // flexible reference
    description   String?

    // Relations
    wallet  Wallet   @relation(fields: [walletId], references: [id])
    payment Payment? @relation(fields: [paymentId], references: [id])

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@index([paymentId])
    @@index([walletId, createdAt])
    @@index([walletId, category])
}

enum PaymentType {
    credit_card
    credit_card_auto
    ach
    ach_auto
    wire_transfer
    paypal
}

enum PaymentStatus {
    pending
    processing
    completed
    failed
    refunded
}

enum ProcessingMethod {
    stripe
    manual
}

model Payment {
    id             String @id @default(cuid())
    organizationId String
    userId         String

    type             PaymentType
    status           PaymentStatus    @default(pending)
    processingMethod ProcessingMethod
    paymentMethodId  String?

    amount Decimal @default(0.00) @db.Decimal(12, 4)
    fee    Decimal @default(0.00) @db.Decimal(12, 4)

    // STRIPE
    stripePaymentIntentId String?
    // WIRE TRANSFER
    wireReceivedAmount    Decimal? @default(0.00) @db.Decimal(12, 4)
    wireFeeAmount         Decimal? @default(0.00) @db.Decimal(12, 4)
    // ACH
    achTraceNo            String?

    metadata    Json?
    /**
     * ex: wire transfer metadata
     * {
     * proofDocuments: ["file1.pdf", "file2.jpg"],
     * submittedAmount: 1000.00,
     * wireTransferDate: "2025-08-16",
     * bankReference: "<EMAIL>",
     * }
     */
    description String?

    initiatedAt DateTime?
    completedAt DateTime?
    failedAt    DateTime?

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    walletTransactions WalletTransaction[]

    // Relations
    organization  Organization   @relation(fields: [organizationId], references: [id])
    user          User           @relation(fields: [userId], references: [id])
    paymentMethod PaymentMethod? @relation(fields: [paymentMethodId], references: [id])

    @@index([organizationId])
    @@index([status, createdAt]) // Pending payments by date
    @@index([organizationId, status, createdAt]) // Org's payment status
}

enum PaymentMethodStatus {
    pending // awaiting verification
    verified // ready to use
    failed // verification failed
    expired // credit card expired
    inactive // manually disabled
}

enum PaymentMethodType {
    credit_card
    ach
}

enum CreditCardBrand {
    amex
    cartes_bancaires
    diners
    discover
    eftpos_au
    jcb
    link
    mastercard
    unionpay
    visa
    unknown
}

model PaymentMethod {
    id             String              @id @default(cuid())
    organizationId String
    type           PaymentMethodType
    status         PaymentMethodStatus

    // Stripe credit card
    stripePaymentMethodId String?
    cardFingerprint       String?
    cardLast4             String?
    cardBrand             CreditCardBrand?
    cardExpMonth          Int?
    cardExpYear           Int?

    // ACH
    // Customer Info
    customerName         String?
    customerCompany      String?
    customerEmail        String?
    customerPhone        String?
    customerAddress      String?
    // Bank Info
    bankName             String?
    bankLast4            String?
    bankAccountName      String?
    bankAccountNo        String? // 🔐 encrypt
    bankRoutingNo        String?
    bankAccountType      String? // ['checking', 'saving']
    bankHolderType       String? // ['personal', 'business']
    bankBillingAddress   String?
    // Online Payment Authorization
    authorizationText    String?
    authorizationVersion String?
    authorizedAt         DateTime?
    signatureFile        String? // ex "signature-2025-08-04-0001.png"

    isDefault    Boolean   @default(false) // default for auto-recharge
    lastUsedAt   DateTime? // track usage
    failureCount Int       @default(0)

    verifiedBy String?
    verifiedAt DateTime?

    payment Payment[]

    organization Organization @relation(fields: [organizationId], references: [id])

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@index([organizationId, isDefault])
    @@index([organizationId, type, status])
}

model RechargePlan {
    id             String @id @default(cuid())
    organizationId String @unique

    enabled   Boolean @default(true)
    threshold Decimal @default(0.00) @db.Decimal(12, 4)
    amount    Decimal @default(0.00) @db.Decimal(12, 4)
    maxPerDay Int     @default(5) // number of recharges per day

    lastTriggeredAt DateTime?
    failureCount    Int       @default(0)

    organization Organization @relation(fields: [organizationId], references: [id])

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@index([organizationId])
}
