export type CarrierId = "USPS" | "FedExDefault" | "DHL" | "UPS";

export type ServiceLevel = {
  id: string;
  label: string;
  transitTime?: string;
};

export type Carrier = {
  id: CarrierId;
  fullName: string;
  shortName: string;
  serviceLevels: ServiceLevel[];
  predefinedPackages: {
    id: string;
    label: string;
    dimensions?: string;
    description?: string;
  }[];
};

export type CarrierBasicInfo = {
  id: string;
  fullName: string;
  shortName: string;
};

export interface CarrierAccountMapping {
  [carrierId: string]: {
    default: string; // Default carrier account ID
    accounts?: {
      [accountKey: string]: {
        id: string;
        condition: string; // Description of when to use this account
      };
    };
  };
}
