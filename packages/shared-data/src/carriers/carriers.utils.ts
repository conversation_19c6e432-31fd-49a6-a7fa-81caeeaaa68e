import { CARRIER_ACCOUNT_MAP } from "./carrier-account-map";
import { CARRIERS_DATA } from "./carriers-data";
import { CarrierBasicInfo, CarrierId } from "src/types/carriers";

/*
Get all carriers 
*/
export const getAllCarriers = () => {
  return CARRIERS_DATA;
};

/*
  Get full carrier by ID
  */
export const getCarrierById = (carrierId: CarrierId) => {
  return CARRIERS_DATA.find(carrier => carrier.id === carrierId);
};

/*
  Get basic carrier info by ID
  */
export const getCarrierBasicInfo = (
  carrierId: CarrierId
): CarrierBasicInfo | null => {
  const carrier = getCarrierById(carrierId);
  if (!carrier) return null;

  return {
    id: carrier.id,
    fullName: carrier.fullName,
    shortName: carrier.shortName,
  };
};

/*
Get carrier ID by carrier shortName
*/
export const getCarrierIdByShortName = (shortName: string) => {
  return CARRIERS_DATA.find(
    carrier => carrier.shortName.toLowerCase() === shortName.toLowerCase()
  )?.id;
};

/*
  Get service levels for a specific carrier
  */
export const getCarrierServiceLevels = (carrierId: CarrierId) => {
  const carrier = getCarrierById(carrierId);
  return carrier?.serviceLevels || [];
};

/* 
  Get predefined packages for a specific carrier
  */
export const getCarrierPredefinedPackages = (carrierId: CarrierId) => {
  const carrier = getCarrierById(carrierId);
  return carrier?.predefinedPackages || [];
};

/*
  Get a specific service level by carrier ID and service ID
  */
export const getServiceLevel = (
  carrierId: CarrierId,
  serviceLevelId: string
) => {
  const carrier = getCarrierById(carrierId);
  return carrier?.serviceLevels.find(service => service.id === serviceLevelId);
};

/*
  Find carrier by service level ID
  */
export const getCarrierByServiceId = (serviceId: string) => {
  return CARRIERS_DATA.find(carrier =>
    carrier.serviceLevels.some(service => service.id === serviceId)
  );
};

export const getCarrierBasicInfoByServiceId = (serviceId: string) => {
  const carrier = getCarrierByServiceId(serviceId);
  if (!carrier) return null;
  return {
    id: carrier.id,
    fullName: carrier.fullName,
    shortName: carrier.shortName,
  };
};

export const getCarrierAccountId = (carrierId: string) => {
  return CARRIER_ACCOUNT_MAP[carrierId].default;
};

export const getCarrierAccountIdByServiceId = (serviceId: string) => {
  const carrier = getCarrierByServiceId(serviceId);
  if (!carrier) return null;
  return carrier.id;
};

/*
  Find carrier by predefined package ID
  */
export const getCarrierByPackageId = (packageId: string) => {
  return CARRIERS_DATA.find(carrier =>
    carrier.predefinedPackages.some(pkg => pkg.id === packageId)
  );
};

/*
  Get all service IDs of all carriers in a single array
  */
export const getAllServiceIds = () => {
  return CARRIERS_DATA.flatMap(carrier =>
    carrier.serviceLevels.map(service => service.id)
  );
};

/*
  Get all predefined packages for a specific service ID
  */
export const filterPredefinedPackagesByServiceId = (serviceId?: string) => {
  // if no service ID, return all predefined packages
  if (!serviceId)
    return CARRIERS_DATA.map(({ id, predefinedPackages, shortName }) => ({
      id,
      predefinedPackages,
      shortName,
    }));

  const carrier = getCarrierByServiceId(serviceId);
  if (!carrier) return [];

  return CARRIERS_DATA.filter(c => c.id === carrier.id).map(
    ({ id, predefinedPackages, shortName }) => ({
      id,
      predefinedPackages,
      shortName,
    })
  );
};
