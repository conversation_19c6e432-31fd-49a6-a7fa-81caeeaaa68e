// TODO: remove this file and use carriers-data.ts instead

export type CarrierId =
  | "ca_086d820568284ce789098d2f1bcfe15b"
  | "ca_11d88125bbe84e719502276533e624d5"
  | "ca_e1d261a3eb254a12868be06ccc8fcbc1";

export type CarrierCode = "USPS" | "FEDEX" | "DHL";

export type Carrier = {
  id: string;
  fullName: string;
  shortName: string;
  code: string;
  serviceLevels: {
    id: string;
    label: string;
    transitTime?: string;
  }[];
  predefinedPackages: {
    id: string;
    label: string;
    dimensions?: string;
    description?: string;
  }[];
};

export const CARRIERS: Carrier[] = [
  {
    id: "ca_086d820568284ce789098d2f1bcfe15b",
    fullName: "United States Postal Service",
    shortName: "USPS",
    code: "USPS",
    serviceLevels: [
      { id: "First", label: "First", transitTime: "1-5 business days" },
      { id: "Priority", label: "Priority", transitTime: "1-3 business days" },
      { id: "Express", label: "Express", transitTime: "1-2 days" },
      {
        id: "GroundAdvantage",
        label: "Ground Advantage",
        transitTime: "2-5 days",
      },
    ],
    predefinedPackages: [
      {
        id: "Card",
        label: "Card",
        dimensions: "6in x 4.5in x 0.016in",
      },
      {
        id: "Letter",
        label: "Letter",
        dimensions: "11.5in x 6.125in x 0.25in",
      },
      { id: "Flat", label: "Flat", dimensions: "11.5in x 6.125in x 0.75in" },
    ],
  },
  {
    id: "ca_11d88125bbe84e719502276533e624d5",
    fullName: "FedEx",
    shortName: "FedEx",
    code: "FEDEX",
    serviceLevels: [
      {
        id: "FEDEX_2_DAY",
        label: "FedEx 2 Day",
        transitTime: "2 business days by 4:30pm, Saturday delivery",
      },
      {
        id: "FEDEX_2_DAY_AM",
        label: "FedEx 2 Day AM",
        transitTime: "2 business days by 10:30am",
      },
      {
        id: "FEDEX_EXPRESS_SAVER",
        label: "FedEx Express Saver",
        transitTime: "3 business days by 4:30pm",
      },
    ],
    predefinedPackages: [
      {
        id: "FedExEnvelope",
        label: "FedEx Envelope",
        dimensions: "9.5in x 12.5in",
        description: "Up to approximately 60 pages",
      },
      { id: "FedExBox", label: "FedEx Box" },
      { id: "FedExPak", label: "FedEx Pak", dimensions: "12in x 15.5in" },
      {
        id: "FedExTube",
        label: "FedEx Tube",
        dimensions: "38in x 6in x 6in x 6in",
        description:
          "Triangular box for plans, posters, fabric rolls, charts and blueprints",
      },
    ],
  },
  {
    id: "ca_e1d261a3eb254a12868be06ccc8fcbc1",
    fullName: "DHL Express",
    shortName: "DHL",
    code: "DHL",
    serviceLevels: [
      { id: "BreakBulkEconomy", label: "Break Bulk Economy" },
      { id: "BreakBulkExpress", label: "Break Bulk Express" },
    ],
    predefinedPackages: [
      { id: "JumboDocument", label: "Jumbo Document" },
      { id: "JumboParcel", label: "Jumbo Parcel" },
    ],
  },
  {
    id: "ca_d34b12a50e274a35b93e701ab4895e80",
    fullName: "UPS",
    shortName: "UPS",
    code: "UPS",
    serviceLevels: [
      { id: "Ground", label: "Ground" },
      { id: "UPSStandard", label: "UPS Standard" },
      { id: "UPSSaver", label: "UPS Saver" },
      { id: "Express", label: "Express" },
      { id: "ExpressPlus", label: "ExpressPlus" },
      { id: "Expedited", label: "Expedited" },
      { id: "NextDayAir", label: "Next Day Air" },
    ],
    predefinedPackages: [],
  },
];

/*
Get all carriers 
*/
export const getAllCarriers = () => {
  return CARRIERS;
};

/*
Get full carrier by ID
*/
export const getCarrierById = (carrierId: CarrierId) => {
  return CARRIERS.find(carrier => carrier.id === carrierId);
};

/*
Get full carrier by code
*/
export const getCarrierByCode = (code: string) => {
  return CARRIERS.find(carrier => carrier.code === code);
};

/*
Get basic carrier info by ID
*/
export const getCarrierBasicInfo = (carrierId: CarrierId) => {
  const carrier = getCarrierById(carrierId);
  if (!carrier) return null;

  return {
    id: carrier.id,
    fullName: carrier.fullName,
    shortName: carrier.shortName,
    code: carrier.code,
  };
};

/*
Get basic carrier info by code
*/
export const getCarrierBasicInfoByCode = (code: string) => {
  const carrier = getCarrierByCode(code);
  if (!carrier) return null;
  return {
    id: carrier.id,
    fullName: carrier.fullName,
    shortName: carrier.shortName,
    code: carrier.code,
  };
};

/*
Get service levels for a specific carrier
*/
export const getCarrierServiceLevels = (carrierId: CarrierId) => {
  const carrier = getCarrierById(carrierId);
  return carrier?.serviceLevels || [];
};

/* 
Get predefined packages for a specific carrier
*/
export const getCarrierPredefinedPackages = (carrierId: CarrierId) => {
  const carrier = getCarrierById(carrierId);
  return carrier?.predefinedPackages || [];
};

/*
Get a specific service level by carrier ID and service ID
*/
export const getServiceLevel = (
  carrierId: CarrierId,
  serviceLevelId: string
) => {
  const carrier = getCarrierById(carrierId);
  return carrier?.serviceLevels.find(service => service.id === serviceLevelId);
};

/*
Get a specific service level by carrier code and service ID
*/
export const getServiceLevelByCarrierCode = (
  carrierCode: string,
  serviceLevelId: string
) => {
  const carrier = getCarrierByCode(carrierCode);
  return carrier?.serviceLevels.find(service => service.id === serviceLevelId);
};

/*
Find carrier by service level ID
*/
export const getCarrierByServiceId = (serviceId: string) => {
  return CARRIERS.find(carrier =>
    carrier.serviceLevels.some(service => service.id === serviceId)
  );
};

export const getCarrierBasicInfoByServiceId = (serviceId: string) => {
  const carrier = getCarrierByServiceId(serviceId);
  if (!carrier) return null;
  return {
    id: carrier.id,
    fullName: carrier.fullName,
    shortName: carrier.shortName,
    code: carrier.code,
  };
};

export const getCarrierAccountIdByServiceId = (serviceId: string) => {
  const carrier = getCarrierByServiceId(serviceId);
  if (!carrier) return null;
  return carrier.id;
};

/*
Find carrier by predefined package ID
*/
export const getCarrierByPackageId = (packageId: string) => {
  return CARRIERS.find(carrier =>
    carrier.predefinedPackages.some(pkg => pkg.id === packageId)
  );
};

/*
Get all service IDs of all carriers in a single array
*/
export const getAllServiceIds = () => {
  return CARRIERS.flatMap(carrier =>
    carrier.serviceLevels.map(service => service.id)
  );
};

/*
Get all predefined packages for a specific service ID
*/
export const filterPredefinedPackagesByServiceId = (serviceId?: string) => {
  // if no service ID, return all predefined packages
  if (!serviceId)
    return CARRIERS.map(({ id, predefinedPackages, shortName }) => ({
      id,
      predefinedPackages,
      shortName,
    }));

  const carrier = getCarrierByServiceId(serviceId);
  if (!carrier) return [];

  return CARRIERS.filter(c => c.id === carrier.id).map(
    ({ id, predefinedPackages, shortName }) => ({
      id,
      predefinedPackages,
      shortName,
    })
  );
};
