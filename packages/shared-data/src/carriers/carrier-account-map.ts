import { CarrierAccountMapping } from "src/types/carriers";

export const CARRIER_ACCOUNT_MAP: CarrierAccountMapping = {
  USPS: {
    default: "ca_086d820568284ce789098d2f1bcfe15b",
  },
  FedExDefault: {
    default: "ca_11d88125bbe84e719502276533e624d5",
  },
  DHL: {
    default: "ca_e1d261a3eb254a12868be06ccc8fcbc1",
  },
  UPS: {
    default: "ca_e98e28fcaa2a4be5ba00194ef57658d2", // Default to the 1-9lb account
    accounts: {
      lightweight: {
        id: "ca_e98e28fcaa2a4be5ba00194ef57658d2",
        condition: "1 to 9lb",
      },
      heavy: {
        id: "ca_d34b12a50e274a35b93e701ab4895e80",
        condition: "9lb and above",
      },
      international: {
        id: "ca_fcb1138825364cabb6374c5afc57eaad",
        condition: "International shipments",
      },
    },
  },
};
