export const CARRIER_ACCOUNTS = [
  {
    id: "ca_086d820568284ce789098d2f1bcfe15b",
    object: "CarrierAccount",
    type: "UspsAccount",
    clone: false,
    created_at: "2025-05-06T17:15:14Z",
    updated_at: "2025-06-02T15:18:38Z",
    description: "USPS Account",
    reference: null,
    billing_type: "easypost",
    readable: "USPS",
    logo: null,
    fields: {
      credentials: {
        company_name: {
          visibility: "visible",
          label: "Account Holder Name",
          value: "",
        },
        address_street: {
          visibility: "visible",
          label: "Street Address",
          value: "",
        },
        address_city: {
          visibility: "visible",
          label: "City",
          value: "",
        },
        address_state: {
          visibility: "visible",
          label: "State",
          value: "",
        },
        address_zip: {
          visibility: "visible",
          label: "ZIP Code",
          value: "",
        },
        phone: {
          visibility: "visible",
          label: "Phone Number",
          value: "",
        },
        email: {
          visibility: "visible",
          label: "Email Address",
          value: null,
        },
        shipper_id: {
          visibility: "visible",
          label: "Shipper Identifier (Optional)",
          value: "",
        },
      },
    },
    credentials: {
      company_name: "",
      address_street: "",
      address_city: "",
      address_state: "",
      address_zip: "",
      phone: "",
      email: null,
      shipper_id: "",
    },
    test_credentials: null,
  },
  {
    id: "ca_e1d261a3eb254a12868be06ccc8fcbc1",
    object: "CarrierAccount",
    type: "DhlExpressDefaultAccount",
    clone: false,
    created_at: "2025-05-06T17:15:14Z",
    updated_at: "2025-05-06T17:15:14Z",
    description: "DHL Express Account",
    reference: null,
    billing_type: "easypost",
    readable: "DHL Express",
    logo: null,
    fields: {
      credentials: {},
    },
    credentials: {},
    test_credentials: null,
  },
  {
    id: "ca_11d88125bbe84e719502276533e624d5",
    object: "CarrierAccount",
    type: "FedexDefaultAccount",
    clone: false,
    created_at: "2025-05-06T17:15:14Z",
    updated_at: "2025-05-06T17:15:14Z",
    description: "FedEx Default Account",
    reference: null,
    billing_type: "easypost",
    readable: "FedEx Default",
    logo: null,
    fields: {
      credentials: {},
    },
    credentials: {},
    test_credentials: null,
  },
  {
    id: "ca_d34b12a50e274a35b93e701ab4895e80",
    object: "CarrierAccount",
    type: "UpsAccount",
    clone: false,
    created_at: "2025-07-02T09:29:32Z",
    updated_at: "2025-07-02T09:29:32Z",
    description: "9lb and above ",
    reference: "UPS - 019F74 > 9lb",
    billing_type: "carrier",
    readable: "UPS",
    logo: null,
    fields: {
      credentials: {
        account_number: {
          visibility: "readonly",
          label: "UPS Account Number",
          value: "019F74",
        },
        oauth_status: {
          visibility: "readonly",
          label: "OAuth Authorization Status",
          value: "approved",
        },
        partner_oauth_url: {
          visibility: "readonly",
          label: "OAuth Portal URL",
          value: null,
        },
      },
    },
    credentials: {
      account_number: "019F74",
      oauth_status: "approved",
      partner_oauth_url: null,
    },
    test_credentials: null,
  },
  {
    id: "ca_e98e28fcaa2a4be5ba00194ef57658d2",
    object: "CarrierAccount",
    type: "UpsAccount",
    clone: false,
    created_at: "2025-07-02T09:33:10Z",
    updated_at: "2025-07-02T09:33:11Z",
    description: "1 to 9lb  ",
    reference: "UPS - B35V70 < 9lb ",
    billing_type: "carrier",
    readable: "UPS",
    logo: null,
    fields: {
      credentials: {
        account_number: {
          visibility: "readonly",
          label: "UPS Account Number",
          value: "B35V70",
        },
        oauth_status: {
          visibility: "readonly",
          label: "OAuth Authorization Status",
          value: "approved",
        },
        partner_oauth_url: {
          visibility: "readonly",
          label: "OAuth Portal URL",
          value: null,
        },
      },
    },
    credentials: {
      account_number: "B35V70",
      oauth_status: "approved",
      partner_oauth_url: null,
    },
    test_credentials: null,
  },
  {
    id: "ca_fcb1138825364cabb6374c5afc57eaad",
    object: "CarrierAccount",
    type: "UpsAccount",
    clone: false,
    created_at: "2025-07-02T09:34:25Z",
    updated_at: "2025-07-02T09:34:26Z",
    description: "UPS International ",
    reference: "UPS 82V589 - International ",
    billing_type: "carrier",
    readable: "UPS",
    logo: null,
    fields: {
      credentials: {
        account_number: {
          visibility: "readonly",
          label: "UPS Account Number",
          value: "82V589",
        },
        oauth_status: {
          visibility: "readonly",
          label: "OAuth Authorization Status",
          value: "approved",
        },
        partner_oauth_url: {
          visibility: "readonly",
          label: "OAuth Portal URL",
          value: null,
        },
      },
    },
    credentials: {
      account_number: "82V589",
      oauth_status: "approved",
      partner_oauth_url: null,
    },
    test_credentials: null,
  },
];
