import { Carrier } from "src/types/carriers";

export const CARRIERS_DATA: Carrier[] = [
  {
    id: "USPS",
    fullName: "United States Postal Service",
    shortName: "USPS",
    serviceLevels: [
      { id: "First", label: "First", transitTime: "1-5 business days" },
      { id: "Priority", label: "Priority", transitTime: "1-3 business days" },
      { id: "Express", label: "Express", transitTime: "1-2 days" },
      {
        id: "GroundAdvantage",
        label: "Ground Advantage",
        transitTime: "2-5 days",
      },
      {
        id: "LibraryMail",
        label: "Library Mail",
        transitTime: "2-8 business days",
      },
      {
        id: "MediaMail",
        label: "Media Mail",
        transitTime: "2-8 business days",
      },
      {
        id: "FirstClassMailInternational",
        label: "First Class Mail International",
      },
      {
        id: "FirstClassPackageInternationalService",
        label: "First Class Package International Service",
        transitTime: "7-21 days",
      },
      {
        id: "PriorityMailInternational",
        label: "Priority Mail International",
        transitTime: "6-10 business days",
      },
      {
        id: "ExpressMailInternational",
        label: "Express Mail International",
        transitTime: "3-5 business days",
      },
    ],
    predefinedPackages: [
      {
        id: "Card",
        label: "Card",
        dimensions: "6in x 4.5in x 0.016in",
      },
      {
        id: "Letter",
        label: "Letter",
        dimensions: "11.5in x 6.125in x 0.25in",
      },
      { id: "Flat", label: "Flat", dimensions: "11.5in x 6.125in x 0.75in" },
      {
        id: "FlatRateEnvelope",
        label: "Flat Rate Envelope",
        dimensions: "12.5in x 9.5in",
      },
      {
        id: "FlatRateLegalEnvelope",
        label: "Flat Rate Legal Envelope",
        dimensions: "15in x 9.5in",
      },
      {
        id: "FlatRatePaddedEnvelope",
        label: "Flat Rate Padded Envelope",
        dimensions: "12.5in x 9.5in",
      },
      // {id: 'FlatRateWindowEnvelope', label: "Flat Rate Window Envelope", dimensions: "10in x 5in, 12.5in x 9.5in"},
      // {id: 'FlatRateCardboardEnvelope', label: "Flat Rate Cardboard Envelope", dimensions: '12.5in x 9.5in'},
      {
        id: "SmallFlatRateEnvelope",
        label: "Small Flat Rate Envelope",
        dimensions: "10in x 6in",
      },
      { id: "Parcel", label: "Parcel" },
      { id: "SoftPack", label: "Soft Pack" },
      {
        id: "SmallFlatRateBox",
        label: "Small Flat Rate Box",
        dimensions: "8.6875in x 5.4375in x 1.75in",
      },
      {
        id: "MediumFlatRateBox",
        label: "Medium Flat Rate Box",
        dimensions: "11.25in x 8.75in x 6in, 14.125in x 12in x 3.5in",
      },
      {
        id: "LargeFlatRateBox",
        label: "Large Flat Rate Box",
        dimensions: "12.25in x 12in x 6in",
      },
      //  {id: "LargeFlatRateBoxAPOFPO", label: "Large Flat Rate Box APO/FPO", dimensions: "12.25in x 12.25in x 6in"},
      //  {id: 'FlatTubTrayBox', label: 'Flat Tub Tray Box'},
      //  {id: 'EMMTrayBox', label: 'EMM Tray Box'},
      //  {id: 'FullTrayBox', label: 'Full Tray Box'},
      //  {id: 'HalfTrayBox', label: 'Half Tray Box'},
      //  {id: 'PMODSack', label: 'PMOD Sack'},
    ],
  },
  {
    id: "FedExDefault",
    fullName: "FedEx",
    shortName: "FedEx",
    serviceLevels: [
      {
        id: "FEDEX_2_DAY",
        label: "FedEx 2 Day",
        transitTime: "2 business days by 4:30pm, Saturday delivery",
      },
      {
        id: "FEDEX_2_DAY_AM",
        label: "FedEx 2 Day AM",
        transitTime: "2 business days by 10:30am",
      },
      {
        id: "FEDEX_EXPRESS_SAVER",
        label: "FedEx Express Saver",
        transitTime: "3 business days by 4:30pm",
      },
      { id: "FEDEX_GROUND", label: "FedEx Ground", transitTime: "1-6 days" },
      {
        id: "FEDEX_INTERNATIONAL_CONNECT_PLUS",
        label: "FedEx International Connect Plus",
        transitTime: "2-5 business days",
      },
      {
        id: "FIRST_OVERNIGHT",
        label: "First Overnight",
        transitTime: "2-5 business days",
      },
      {
        id: "GROUND_HOME_DELIVERY",
        label: "Ground Home Delivery",
        transitTime: "1-5 days",
      },
      {
        id: "INTERNATIONAL_ECONOMY",
        label: "International Economy",
        transitTime: "4-5 business days",
      },
      {
        id: "INTERNATIONAL_FIRST",
        label: "International First",
        transitTime: "1-3 business days, time definite to select markets",
      },
      {
        id: "FEDEX_INTERNATIONAL_PRIORITY",
        label: "FedEx International Priority",
        transitTime: "1-3 business days, time definite to select markets",
      },
      {
        id: "PRIORITY_OVERNIGHT",
        label: "Priority Overnight",
        transitTime: "By 10:30am the next business day, Saturday delivery",
      },
      {
        id: "SMART_POST",
        label: "Smart Post",
        transitTime: "2-7 business days; Sunday Delivery to select markets",
      },
      {
        id: "STANDARD_OVERNIGHT",
        label: "Standard Overnight",
        transitTime: "By 4:30pm the next business day",
      },
    ],
    predefinedPackages: [
      {
        id: "FedExEnvelope",
        label: "FedEx Envelope",
        dimensions: "9.5in x 12.5in",
        description: "Up to approximately 60 pages",
      },
      { id: "FedExBox", label: "FedEx Box" },
      { id: "FedExPak", label: "FedEx Pak", dimensions: "12in x 15.5in" },
      {
        id: "FedExTube",
        label: "FedEx Tube",
        dimensions: "38in x 6in x 6in x 6in",
        description:
          "Triangular box for plans, posters, fabric rolls, charts and blueprints",
      },
      {
        id: "FedEx10kgBox",
        label: "FedEx 10kg Box",
        dimensions: "15.81in x 12.94in x 10.19in",
      },
      {
        id: "FedEx25kgBox",
        label: "FedEx 25kg Box",
        dimensions: "21.56in x 16.56in x 13.19in",
      },
      {
        id: "FedExSmallBox",
        label: "FedEx Small Box",
        dimensions: "12.25in x 10.9in x 1.5in, 8.75in x 2.63in x 11.25in",
      },
      {
        id: "FedExMediumBox",
        label: "FedEx Medium Box",
        dimensions: "13.25in x 11.5in x 2.38in, 8.75in x 4.38in x 11.25in",
      },
      {
        id: "FedExLargeBox",
        label: "FedEx Large Box",
        dimensions: "17.88in x 12.38in x 3in, 8.75in x 7.75in x 11.25in",
      },
      {
        id: "FedExExtraLargeBox",
        label: "FedEx Extra Large Box",
        dimensions: "11.88in x 10.75in x 11in, 15.25in x 14.13in x 6in",
      },
    ],
  },
  {
    id: "DHL",
    fullName: "DHL Express",
    shortName: "DHL",
    serviceLevels: [
      { id: "BreakBulkEconomy", label: "Break Bulk Economy" },
      { id: "BreakBulkExpress", label: "Break Bulk Express" },
      { id: "DomesticEconomySelect", label: "Domestic Economy Select" },
      { id: "DomesticExpress", label: "Domestic Express" },
    ],
    predefinedPackages: [
      // { id: "JumboDocument", label: "Jumbo Document" },
      // { id: "JumboParcel", label: "Jumbo Parcel" },
      // { id: "Document", label: "Document" },
      // { id: "DHLFlyer", label: "DHL Flyer" },
      // { id: "Domestic", label: "Domestic" },
      // { id: "ExpressDocument", label: "Express Document" },
      // { id: "DHLExpressEnvelope", label: "DHL Express Envelope" },
      // { id: "JumboBox", label: "Jumbo Box" },
      // { id: "JumboJuniorDocument", label: "Jumbo Junior Document" },
      // { id: "JuniorJumboBox", label: "Junior Jumbo Box" },
      // { id: "JumboJuniorParcel", label: "Jumbo Junior Parcel" },
      // { id: "OtherDHLPackaging", label: "Other DHL Packaging" },
      // { id: "Parcel", label: "Parcel" },
      // { id: "YourPackaging", label: "Your Packaging" },
    ],
  },
  {
    id: "UPS",
    fullName: "United Parcel Service",
    shortName: "UPS",
    serviceLevels: [
      { id: "Ground", label: "Ground", transitTime: "	" },
      {
        id: "UPSStandard",
        label: "UPS Standard",
        transitTime: "1-5 Business Days",
      },
      {
        id: "UPSSaver",
        label: "UPS Saver",
        transitTime: "Next Day to 4 Business Days",
      },
      {
        id: "Express",
        label: "Express",
        transitTime: "Next Day to 3 Business Days",
      },
      {
        id: "ExpressPlus",
        label: "ExpressPlus",
        transitTime: "Next Day to 2 Business Days",
      },
      {
        id: "Expedited",
        label: "Expedited",
        transitTime: "3 to 5 Business Days",
      },
      {
        id: "NextDayAir",
        label: "Next Day Air",
        transitTime: "Next Business Day by 10:30am",
      },
      {
        id: "NextDayAirSaver",
        label: "Next Day Air Saver",
        transitTime: "Next Business Day by 3:00pm",
      },
      {
        id: "NextDayAirEarlyAM",
        label: "Next Day Air Early AM",
        transitTime: "Next Business Days by 8:00am",
      },
      {
        id: "2ndDayAir",
        label: "2nd Day Air",
        transitTime: "2 Business Days by end of day",
      },
      { id: "2ndDayAirAM", label: "2nd Day Air AM" },
      {
        id: "3DaySelect",
        label: "3 Day Select",
        transitTime: "3 Business Days by end of day",
      },
    ],
    predefinedPackages: [
      {
        id: "UPSLetter",
        label: "UPS Letter",
        dimensions: "12.5in - 15in x 9.5in",
      },
      {
        id: "UPSExpressBox",
        label: "UPS Express Box",
        dimensions: "12.5in x 3.75in x 18in",
      },
      {
        id: "UPS25kgBox",
        label: "UPS 25kg Box",
        dimensions: "19.75in x 17.75in x 13.25in",
      },
      {
        id: "UPS10kgBox",
        label: "UPS 10kg Box",
        dimensions: "16.5in x 13.25in x 10.75in",
      },
      { id: "Tube", label: "Tube", dimensions: "38in x 6in x 6in" },
      { id: "Pak", label: "Pak", dimensions: "16in x 12.75in" },
      {
        id: "SmallExpressBox",
        label: "Small Express Box",
        dimensions: "13in x 11in x 2in",
      },
      {
        id: "MediumExpressBox",
        label: "Medium Express Box",
        dimensions: "16in x 11in x 3in",
      },
      {
        id: "LargeExpressBox",
        label: "Large Express Box",
        dimensions: "18in x 13in x 3in",
      },
    ],
  },
];
