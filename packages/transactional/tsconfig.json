{
  "compilerOptions": {
    "target": "ESNext",
    "module": "ESNext",
    "moduleResolution": "node",
    "declaration": true,
    "strict": true,
    "noImplicitAny": true /* Raise error on expressions and declarations with an implied 'any' type. */,
    "strictNullChecks": true /* Enable strict null checks. */,
    "strictFunctionTypes": true /* Enable strict checking of function types. */,
    "noUnusedLocals": true /* Report errors on unused locals. */,
    "noUnusedParameters": true /* Report errors on unused parameters. */,
    "noImplicitReturns": true /* Report error when not all code paths in function return a value. */,
    "noFallthroughCasesInSwitch": true /* Report errors for fallthrough cases in switch statement. */,
    "importHelpers": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "experimentalDecorators": true,
    "forceConsistentCasingInFileNames": true /* Disallow inconsistently-cased references to the same file. */,
    "baseUrl": ".",
    "rootDir": ".",
    "outDir": "./dist/esm",
    "lib": ["ESNext", "DOM"],
    "jsx": "react-jsx",
    "pretty": true,
    "paths": {
      "@/*": ["./src/*"],
      "@/emails/*": ["./emails/*"],
      "@/components/*": ["./components/*"]
    }
  },
  "typedocOptions": {
    "entryPoints": ["./emails/"],
    "out": "typedoc"
  },
  "include": ["emails/**/*"],
  "exclude": ["node_modules"]
}
