import {
  <PERSON>,
  But<PERSON>,
  Container,
  Head,
  <PERSON>ing,
  Hr,
  Html,
  Img,
  Preview,
  Section,
  Text,
} from "@react-email/components";

interface UserInviteEmailProps {
  userInviteEmailLink: string;
  inviteSenderName: string;
  inviteSenderOrganizationName: string;
}

const baseUrl = process.env.VERCEL_URL
  ? `https://${process.env.VERCEL_URL}`
  : "http://localhost:3000";

export default function UserInviteEmail({
  userInviteEmailLink,
  inviteSenderName,
  inviteSenderOrganizationName,
}: UserInviteEmailProps) {
  return (
    <Html>
      <Head />
      <Body style={main}>
        <Preview>Dropright Invite</Preview>
        <Container style={container}>
          <Section style={coverSection}>
            <Section style={upperSection}>
              <Section style={{ display: "flex", justifyContent: "end" }}>
                <Img
                  src={`${baseUrl}/logos/primary-light.svg`}
                  alt="Logo"
                  width="100"
                />
              </Section>
              <Heading style={h1}>You've been invited to a Dropright.</Heading>
              <Hr />
              <Text style={mainText}>Hi</Text>
              <Text style={mainText}>
                {inviteSenderName} with {inviteSenderOrganizationName} has
                invited you to use Dropright to collaborate with them. Use the
                button below to set up your account and get started:
              </Text>
              <Button href={userInviteEmailLink} style={button}>
                Set up account
              </Button>
            </Section>
          </Section>
        </Container>
      </Body>
    </Html>
  );
}

UserInviteEmail.PreviewProps = {
  inviteSenderName: "name",
  inviteSenderOrganizationName: "organization name",
  userInviteEmailLink: "/auth/invite?token=",
} satisfies UserInviteEmailProps;

const main = {
  backgroundColor: "#fff",
  color: "#212121",
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
};

const container = {
  padding: "20px",
  margin: "0 auto",
  backgroundColor: "#eee",
};

const h1 = {
  color: "#333",
  fontSize: "32px",
  fontWeight: "bold",
  marginBottom: "15px",
};

const text = {
  color: "#333",
  fontSize: "14px",
  margin: "20px 0",
};

const coverSection = { backgroundColor: "#fff", borderRadius: "8px" };

const upperSection = { padding: "25px 35px" };

const mainText = { ...text, marginBottom: "14px" };

const button = {
  backgroundColor: "#1457FF",
  borderRadius: "3px",
  fontWeight: "600",
  color: "#fff",
  fontSize: "15px",
  textDecoration: "none",
  textAlign: "center" as const,
  display: "block",
  padding: "11px 23px",
};
