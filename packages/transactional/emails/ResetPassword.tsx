import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  <PERSON>,
  <PERSON><PERSON>,
  Hr,
  Html,
  Img,
  Preview,
  Section,
  Text,
} from "@react-email/components";

interface ResetPasswordProps {
  resetPasswordLink: string;
}

const baseUrl = process.env.VERCEL_URL
  ? `https://${process.env.VERCEL_URL}`
  : "http://localhost:3000";

export default function ResetPassword({
  resetPasswordLink,
}: ResetPasswordProps) {
  return (
    <Html>
      <Head />
      <Body style={main}>
        <Preview>Dropright Password Reset</Preview>
        <Container style={container}>
          <Section style={coverSection}>
            <Section style={upperSection}>
              <Section style={{ display: "flex", justifyContent: "end" }}>
                <Img
                  src={`${baseUrl}/logos/primary-light.svg`}
                  alt="Logo"
                  width="100"
                />
              </Section>
              <Heading style={h1}>Password Reset</Heading>
              <Hr />
              <Text style={mainText}>Hi</Text>
              <Text style={mainText}>
                Someone recently requested a password change for your Dropright
                account. If this was you, you can set a new password here
              </Text>
              <Button href={resetPasswordLink} style={button}>
                Reset password
              </Button>
              <Text style={mainText}>
                If you don't want to change your password or didn't request
                this, just ignore and delete this message.
              </Text>
              <Text style={mainText}>
                To keep your account secure, please don't forward this email to
                anyone.
              </Text>
            </Section>
          </Section>
        </Container>
      </Body>
    </Html>
  );
}

ResetPassword.PreviewProps = {
  resetPasswordLink: "/auth/forgot-password?token=",
} satisfies ResetPasswordProps;

const main = {
  backgroundColor: "#fff",
  color: "#212121",
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
};

const container = {
  padding: "20px",
  margin: "0 auto",
  backgroundColor: "#eee",
};

const h1 = {
  color: "#333",
  fontSize: "32px",
  fontWeight: "bold",
  marginBottom: "15px",
};

const text = {
  color: "#333",
  fontSize: "14px",
  margin: "20px 0",
};

const coverSection = { backgroundColor: "#fff", borderRadius: "8px" };

const upperSection = { padding: "25px 35px" };

const mainText = { ...text, marginBottom: "14px" };

const button = {
  backgroundColor: "#1457FF",
  borderRadius: "3px",
  fontWeight: "600",
  color: "#fff",
  fontSize: "15px",
  textDecoration: "none",
  textAlign: "center" as const,
  display: "block",
  padding: "11px 23px",
};
