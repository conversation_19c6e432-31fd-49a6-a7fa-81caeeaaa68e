import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  <PERSON><PERSON>,
  Hr,
  Html,
  Img,
  Preview,
  Section,
  Text,
} from "@react-email/components";

interface VerifyEmailProps {
  email: string;
  verificationLink: string;
}

const baseUrl = process.env.VERCEL_URL
  ? `https://${process.env.VERCEL_URL}`
  : "http://localhost:3000";

export default function VerifyEmail({
  verificationLink,
  email,
}: VerifyEmailProps) {
  return (
    <Html>
      <Head />
      <Body style={main}>
        <Preview>Drops Email Verification</Preview>
        <Container style={container}>
          <Section style={coverSection}>
            <Section style={upperSection}>
              <Section style={{ display: "flex", justifyContent: "center" }}>
                <Img
                  src={`${baseUrl}/logos/primary-light.svg`}
                  alt="Logo"
                  width="100"
                />
              </Section>
              <Heading style={h1}>Verify your email address</Heading>
              <Hr />
              <Text style={mainText}>
                Please confirm that you want to use this as your Dropright
                account email address.
              </Text>
              <Section style={buttonContainer}>
                <Button href={verificationLink} style={button}>
                  Verify {email}
                </Button>
              </Section>
            </Section>
          </Section>
        </Container>
      </Body>
    </Html>
  );
}

VerifyEmail.PreviewProps = {
  email: "<EMAIL>",
  verificationLink: "/auth/new-verification?token=abc123",
} satisfies VerifyEmailProps;

const main = {
  backgroundColor: "#fff",
  color: "#212121",
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  textAlign: "center" as const,
};

const container = {
  padding: "20px",
  margin: "0 auto",
  backgroundColor: "#eee",
};

const h1 = {
  color: "#333",
  fontSize: "32px",
  fontWeight: "bold",
  marginBottom: "15px",
};

const text = {
  color: "#333",
  fontSize: "14px",
  margin: "24px 0",
};

const coverSection = { backgroundColor: "#fff", borderRadius: "8px" };

const upperSection = { padding: "25px 35px" };

const mainText = { ...text, marginBottom: "14px", padding: "0 24px" };

const buttonContainer = {
  padding: "27px 0 0px",
};

const button = {
  backgroundColor: "#1457FF",
  borderRadius: "3px",
  fontWeight: "600",
  color: "#fff",
  fontSize: "15px",
  textDecoration: "none",
  textAlign: "center" as const,
  display: "block",
  padding: "11px 23px",
};
