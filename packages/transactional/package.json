{"name": "transactional", "version": "1.0.0", "description": "Transactional email templates using React Email", "main": "index.js", "scripts": {"email-dev": "email dev --port 3001", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@types/node": "^22.15.23", "@types/react": "^19", "@types/react-dom": "^19", "react-email": "4.0.15"}, "dependencies": {"@react-email/components": "0.0.41", "react": "^19.0.0", "react-dom": "^19.0.0"}}