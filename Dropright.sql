CREATE TYPE "UserStatus" AS ENUM (
  'active',
  'deactivated'
);

CREATE TYPE "UserInviteStatus" AS ENUM (
  'pending',
  'rejected'
);

CREATE TYPE "PaymentType" AS ENUM (
  'credit_card',
  'ach',
  'wire_transfer',
  'paypal'
);

CREATE TYPE "PaymentStatus" AS ENUM (
  'pending',
  'processing',
  'completed',
  'failed',
  'refunded'
);

CREATE TYPE "WalletTransactionType" AS ENUM (
  'top_up',
  'payment',
  'refund',
  'adjustment',
  'fee'
);

CREATE TYPE "ProcessingMethod" AS ENUM (
  'stipe',
  'manual_ach',
  'manual_wire'
);

CREATE TYPE "Store" AS ENUM (
  'dropright',
  'shipstation'
);

CREATE TYPE "OrderStatus" AS ENUM (
  'open',
  'processing',
  'shipped',
  'cancelled'
);

CREATE TYPE "BatchStatus" AS ENUM (
  'open',
  'shipped',
  'cancelled'
);

CREATE TYPE "StoreProvider" AS ENUM (
  'dropright',
  'shipstation',
  'shopify',
  'woocommerce',
  'etsy',
  'amazon',
  'ebay',
  'tiktok'
);

CREATE TYPE "StoreStatus" AS ENUM (
  'active',
  'inactive'
);

CREATE TYPE "ShipmentStatus" AS ENUM (
  'unknown',
  'pre_transit',
  'in_transit',
  'out_for_delivery',
  'delivered',
  'available_for_pickup',
  'return_to_sender',
  'failure',
  'cancelled',
  'error'
);

CREATE TYPE "ClaimStatus" AS ENUM (
  'submitted',
  'in_review',
  'approved',
  'rejected',
  'approved_partial',
  'cancelled',
  'needs_action'
);

CREATE TYPE "ClaimType" AS ENUM (
  'loss',
  'theft',
  'damage'
);

CREATE TYPE "ClaimAttachmentType" AS ENUM (
  'email_evidence',
  'invoice',
  'supporting_documents'
);

CREATE TYPE "RefundStatus" AS ENUM (
  'submitted',
  'refunded',
  'rejected'
);

CREATE TABLE "Organization" (
  "id" varchar PRIMARY KEY NOT NULL,
  "orgCode" varchar UNIQUE NOT NULL,
  "name" varchar NOT NULL,
  "phone" varchar NOT NULL,
  "shipmentVolume" varchar NOT NULL,
  "billingAddress" "BillingAddress",
  "createdAt" datetime NOT NULL,
  "updatedAt" datetime NOT NULL
);

CREATE TABLE "OrganizationCarrierPricing" (
  "id" varchar PRIMARY KEY NOT NULL,
  "organizationId" varchar NOT NULL,
  "carrierId" varchar NOT NULL,
  "serviceOverrides" varchar,
  "percentageMarkup" decimal NOT NULL,
  "createdAt" datetime NOT NULL,
  "updatedAt" datetime NOT NULL
);

CREATE TABLE "OrganizationCarrierServicePricing" (
  "id" varchar PRIMARY KEY NOT NULL,
  "organizationCarrierPricingId" varchar NOT NULL,
  "serviceId" varchar NOT NULL,
  "servicePercentageMarkup" decimal NOT NULL,
  "createdAt" datetime NOT NULL,
  "updatedAt" datetime NOT NULL
);

CREATE TABLE "User" (
  "id" varchar,
  "organizationId" varchar NOT NULL,
  "firstName" varchar NOT NULL,
  "lastName" varchar NOT NULL,
  "email" varchar UNIQUE NOT NULL,
  "emailVerified" datetime,
  "image" varchar,
  "password" varchar,
  "status" "UserStatus" NOT NULL,
  "createdAt" datetime NOT NULL,
  "updatedAt" datetime NOT NULL
);

CREATE TABLE "Account" (
  "userId" "User",
  "type" varchar NOT NULL,
  "provider" varchar NOT NULL,
  "providerAccountId" varchar NOT NULL,
  "refreshToken" varchar,
  "accessToken" varchar,
  "expiresAt" datetime,
  "scope" varchar,
  "idToken" varchar,
  "sessionState" varchar,
  "createdAt" datetime NOT NULL,
  "updatedAt" datetime NOT NULL
);

CREATE TABLE "Permission" (
  "id" varchar PRIMARY KEY,
  "name" varchar UNIQUE NOT NULL,
  "description" varchar NOT NULL
);

CREATE TABLE "UserPermission" (
  "id" varchar PRIMARY KEY NOT NULL,
  "userId" varchar,
  "permissionId" varchar
);

CREATE TABLE "UserInvite" (
  "id" varchar PRIMARY KEY NOT NULL,
  "organizationId" varchar NOT NULL,
  "email" varchar NOT NULL,
  "token" varchar UNIQUE NOT NULL,
  "expires" date NOT NULL,
  "status" "UserInviteStatus" NOT NULL,
  "invitedByUserId" "User" NOT NULL,
  "createdAt" date NOT NULL
);

CREATE TABLE "UserInvitePermission" (
  "id" varchar PRIMARY KEY NOT NULL,
  "userInviteId" "UserInvite",
  "permissionId" "Permission"
);

CREATE TABLE "VerificationToken" (
  "id" varchar PRIMARY KEY NOT NULL,
  "email" varchar NOT NULL,
  "token" varchar UNIQUE NOT NULL,
  "expires" date NOT NULL
);

CREATE TABLE "PasswordResetToken" (
  "id" varchar PRIMARY KEY NOT NULL,
  "email" varchar NOT NULL,
  "token" varchar UNIQUE NOT NULL,
  "expires" date NOT NULL
);

CREATE TABLE "Wallet" (
  "id" varchar PRIMARY KEY NOT NULL,
  "organizationId" varchar UNIQUE NOT NULL,
  "balance" Int DEFAULT 0,
  "reserved" Int DEFAULT 0,
  "createdAt" date NOT NULL,
  "updatedAt" date NOT NULL
);

CREATE TABLE "RechargePlan" (
  "id" varchar PRIMARY KEY NOT NULL,
  "walletId" varchar NOT NULL,
  "enabled" boolean NOT NULL,
  "threshold" Int,
  "amount" Int,
  "paymentMethodId" varchar,
  "maxPerDay" int,
  "createdAt" date NOT NULL,
  "updatedAt" date NOT NULL
);

CREATE TABLE "WalletTransaction" (
  "id" varchar PRIMARY KEY NOT NULL,
  "walletId" varchar NOT NULL,
  "type" "WalletTransactionType" NOT NULL,
  "amount" Int NOT NULL,
  "previousBalance" Int NOT NULL,
  "newBalance" Int NOT NULL,
  "paymentId" varchar,
  "createdAt" date NOT NULL,
  "updatedAt" date NOT NULL
);

CREATE TABLE "Payment" (
  "id" varchar PRIMARY KEY NOT NULL,
  "organizationId" varchar NOT NULL,
  "userId" varchar NOT NULL,
  "status" "PaymentStatus" DEFAULT 'pending',
  "type" "PaymentType" NOT NULL,
  "processingMethod" "ProcessingMethod" NOT NULL,
  "paymentMethodId" varchar,
  "amount" Int NOT NULL,
  "fee" Int NOT NULL,
  "totalAmount" Int NOT NULL,
  "stripePaymenIntenttId" varchar,
  "wireReceivedAmount" Int,
  "wireFeeAmount" Int,
  "achTraceNo" varchar,
  "metadata" json,
  "description" varchar,
  "initiatedAt" datetime,
  "completedAt" datetime,
  "failedAt" datetime,
  "createdAt" date NOT NULL,
  "updatedAt" date NOT NULL
);

CREATE TABLE "PaymentMethod" (
  "id" varchar PRIMARY KEY NOT NULL,
  "organizationId" varchar,
  "type" "PaymentType",
  "isActive" Boolean NOT NULL,
  "stripeCustomerId" varchar,
  "stripePaymentMethodId" varchar,
  "cardLast4" varchar,
  "cardBrand" varchar,
  "cardExpMonth" Int,
  "cardExpYear" Int,
  "bankName" varchar,
  "bankLast4" varchar,
  "bankAccountName" varchar,
  "bankAccountNo" varchar,
  "bankRoutingNo" varchar,
  "bankAccountType" varchar,
  "bankHolderType" varchar,
  "authorizationText" varchar,
  "authorizedBy" varchar,
  "authorizedAt" datetime,
  "status" string,
  "createdAt" date NOT NULL,
  "updatedAt" date NOT NULL
);

CREATE TABLE "Order" (
  "id" varchar NOT NULL,
  "orderNo" varchar NOT NULL,
  "organizationId" varchar NOT NULL,
  "epShipmentId" varchar UNIQUE,
  "batchId" varchar,
  "storeId" varchar NOT NULL,
  "orderDate" datetime NOT NULL,
  "status" "OrderStatus" NOT NULL,
  "carrier" varchar,
  "service" varchar,
  "fromAddressId" varchar NOT NULL,
  "toAddress" json NOT NULL,
  "orderItems" "OrderItem[]" NOT NULL,
  "notes" varchar,
  "parcel" json NOT NULL,
  "insurance" decimal,
  "signature" string,
  "tax" decimal,
  "isSplit" boolean DEFAULT false,
  "parentOrderId" varchar,
  "createdAt" date NOT NULL,
  "updatedAt" date NOT NULL
);

CREATE TABLE "OrderItem" (
  "id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "orderId" int NOT NULL,
  "productId" varchar NOT NULL,
  "quantity" int NOT NULL,
  "price" float
);

CREATE TABLE "Batch" (
  "id" varchar PRIMARY KEY NOT NULL,
  "organizationId" varchar NOT NULL,
  "batchNo" varchar NOT NULL,
  "name" varchar NOT NULL,
  "notes" varchar,
  "status" "BatchStatus" NOT NULL DEFAULT 'open',
  "createdAt" datetime NOT NULL,
  "updatedAt" datetime NOT NULL
);

CREATE TABLE "Store" (
  "id" varchar PRIMARY KEY NOT NULL,
  "organizationId" varcar NOT NULL,
  "name" varchar NOT NULL,
  "provider" "StoreProvider" NOT NULL,
  "status" "StoreStatus",
  "storeUrl" varchar,
  "apiKey" varchar,
  "apiSecret" varchar,
  "createdAt" datetime NOT NULL,
  "updatedAt" datetime NOT NULL
);

CREATE TABLE "Product" (
  "id" varchar PRIMARY KEY NOT NULL,
  "organizationId" varchar NOT NULL,
  "storeId" varchar NOT NULL,
  "sku" varchar NOT NULL,
  "name" varchar NOT NULL,
  "description" varchar,
  "basePrice" float,
  "weight" float,
  "imageUrl" varchar,
  "createdAt" datetime NOT NULL,
  "updatedAt" datetime NOT NULL
);

CREATE TABLE "StoreProduct" (
  "id" varchar PRIMARY KEY NOT NULL,
  "storeId" varchar NOT NULL,
  "productId" varchar NOT NULL,
  "externalId" varchar NOT NULL,
  "externalSku" varchar NOT NULL,
  "createdAt" datetime NOT NULL,
  "updatedAt" datetime NOT NULL
);

CREATE TABLE "BillingAddress" (
  "id" varchar PRIMARY KEY NOT NULL,
  "organizationId" "Organization" NOT NULL,
  "name" varchar,
  "company" varchar,
  "street1" varchar NOT NULL,
  "street2" varchar,
  "city" varchar NOT NULL,
  "state" varchar,
  "country" varchar NOT NULL,
  "zip" varchar NOT NULL,
  "phone" varchar,
  "email" varchar,
  "createdAt" datetime NOT NULL,
  "updatedAt" datetime NOT NULL
);

CREATE TABLE "Parcel" (
  "id" varchar PRIMARY KEY NOT NULL,
  "organizationId" organization NOT NULL,
  "name" varchar NOT NULL,
  "length" decimal,
  "width" decimal,
  "height" decimal,
  "weight" decimal NOT NULL,
  "predefinedPackage" varchat,
  "createdAt" datetime NOT NULL,
  "updatedAt" datetime NOT NULL
);

CREATE TABLE "Address" (
  "id" varchar PRIMARY KEY NOT NULL,
  "epAddressId" varchar UNIQUE NOT NULL,
  "organizationId" "Organization" NOT NULL,
  "name" varchar NOT NULL,
  "company" varchar,
  "street1" varchar NOT NULL,
  "street2" varchar,
  "city" varchar NOT NULL,
  "state" varchar NOT NULL,
  "country" varchar NOT NULL,
  "zip" varchar NOT NULL,
  "phone" varchar NOT NULL,
  "email" varchar NOT NULL,
  "residential" boolean NOT NULL,
  "verified" boolean NOT NULL,
  "createdAt" datetime NOT NULL,
  "updatedAt" datetime NOT NULL
);

CREATE TABLE "Presets" (
  "id" varchar PRIMARY KEY NOT NULL,
  "fromAddressId" varchar,
  "parcel" json,
  "service" varchar,
  "confirmation" varchar,
  "hasInsurance" boolean
);

CREATE TABLE "Shipment" (
  "id" varchar PRIMARY KEY NOT NULL,
  "organizationId" varchar NOT NULL,
  "shipmentNo" varchar NOT NULL,
  "orderId" varchar NOT NULL,
  "epShipmentId" varchar NOT NULL,
  "status" "ShipmentStatus" NOT NULL,
  "fromAddressId" varchar NOT NULL,
  "toAddress" json NOT NULL,
  "returnAddress" json NOT NULL,
  "parcel" json NOT NULL,
  "carrier" varchar NOT NULL,
  "carrierAccountId" varchar NOT NULL,
  "service" varchar NOT NULL,
  "rate" varchar NOT NULL,
  "labelPrinted" datetime,
  "trackingCode" varchar NOT NULL,
  "shipDate" datetime,
  "postageLabel" json NOT NULL,
  "insurance" varchar,
  "fees" json,
  "createdAt" datetime NOT NULL,
  "updatedAt" datetime NOT NULL
);

CREATE TABLE "Return" (
  "id" varchar PRIMARY KEY,
  "organizationId" varchar NOT NULL,
  "rma" varchar NOT NULL,
  "orderId" varchar NOT NULL,
  "epShipmentId" varchar NOT NULL,
  "status" "ShipmentStatus" NOT NULL,
  "fromAddress" json NOT NULL,
  "toAddressId" varchar NOT NULL,
  "parcel" json NOT NULL,
  "carrier" varchar NOT NULL,
  "carrierAccountId" varchar NOT NULL,
  "service" varchar NOT NULL,
  "returnItem" "ReturnItem[]",
  "rate" varchar NOT NULL,
  "labelPrinted" datetime,
  "trackingCode" varchar,
  "shipDate" datetime,
  "postageLabel" json NOT NULL,
  "insurance" varchar,
  "fees" json,
  "createdAt" datetime NOT NULL,
  "updatedAt" datetime NOT NULL
);

CREATE TABLE "ReturnItem" (
  "id" int PRIMARY KEY NOT NULL,
  "returnId" int NOT NULL,
  "productId" varchar NOT NULL,
  "quantity" int NOT NULL,
  "price" float,
  "reason" varchar
);

CREATE TABLE "Claim" (
  "id" varchar NOT NULL,
  "organizationId" varchar NOT NULL,
  "userId" varchar NOT NULL,
  "shipmentId" varchar,
  "returnId" varchar,
  "reference" varchar,
  "epClaimId" varchar NOT NULL,
  "epInsuranceId" varchar NOT NULL,
  "trackingCode" varchar NOT NULL,
  "status" "ClaimStatus" NOT NULL,
  "type" "ClaimType" NOT NULL,
  "description" varchar NOT NULL,
  "requestedAmount" Float NOT NULL,
  "insuranceAmount" Float NOT NULL,
  "approvedAmount" Float NOT NULL,
  "createdAt" datetime NOT NULL,
  "updatedAt" datetime NOT NULL
);

CREATE TABLE "ClaimAttachments" (
  "id" varchar NOT NULL,
  "claimId" varchar NOT NULL,
  "type" "ClaimAttachmentType" NOT NULL,
  "fileUrl" varchar NOT NULL,
  "mimeType" varchar NOT NULL,
  "fileName" varchar,
  "fileSize" int,
  "key" varchar NOT NULL
);

CREATE TABLE "Refund" (
  "id" varchar NOT NULL,
  "organizationId" varchar NOT NULL,
  "epReturnId" varchar NOT NULL,
  "shipmentId" varchar,
  "returnId" varchar,
  "trackingCode" varchar NOT NULL,
  "status" "RefundStatus",
  "carrier" varchar NOT NULL,
  "confirmationNumber" varchar,
  "createdAt" datetime NOT NULL,
  "updatedAt" datetime NOT NULL
);

CREATE TABLE "Counter" (
  "id" varchar NOT NULL,
  "organizationId" varchar NOT NULL,
  "date" date NOT NULL,
  "count" integer NOT NULL,
  PRIMARY KEY ("id")
);

CREATE INDEX ON "OrganizationCarrierPricing" ("organizationId");

CREATE INDEX ON "OrganizationCarrierServicePricing" ("organizationCarrierPricingId");

CREATE INDEX ON "User" ("organizationId");

CREATE INDEX ON "User" ("email");

CREATE INDEX ON "VerificationToken" ("expires");

CREATE INDEX ON "PasswordResetToken" ("expires");

CREATE INDEX ON "Order" ("organizationId");

CREATE UNIQUE INDEX ON "Order" ("organizationId", "orderNo");

CREATE UNIQUE INDEX ON "OrderItem" ("orderId", "productId");

CREATE INDEX ON "Batch" ("organizationId");

CREATE INDEX ON "Store" ("organizationId");

CREATE INDEX ON "Store" ("provider");

CREATE INDEX ON "Product" ("organizationId");

CREATE INDEX ON "BillingAddress" ("organizationId");

CREATE INDEX ON "Parcel" ("organizationId");

CREATE INDEX ON "Address" ("organizationId");

CREATE INDEX ON "Shipment" ("organizationId");

CREATE INDEX ON "Return" ("organizationId");

CREATE UNIQUE INDEX ON "ReturnItem" ("returnId", "productId");

COMMENT ON COLUMN "PaymentMethod"."cardBrand" IS 'visa, mastercard, etc.';

COMMENT ON COLUMN "PaymentMethod"."bankAccountType" IS 'checking or savings';

COMMENT ON COLUMN "PaymentMethod"."bankHolderType" IS 'personal or business';

COMMENT ON COLUMN "Order"."orderNo" IS 'user specifies or else autogenerate eg: DR-mmddyyyy-xxxxx';

COMMENT ON COLUMN "Order"."tax" IS 'Not sure if this should be here';

COMMENT ON COLUMN "Batch"."batchNo" IS 'BCH-mmyyyy-xxxxx';

COMMENT ON COLUMN "Shipment"."shipmentNo" IS 'SHP-mmddyyyy-xxxxx';

COMMENT ON COLUMN "Shipment"."rate" IS 'After markup rate';

COMMENT ON COLUMN "Shipment"."postageLabel" IS 'labelUrl, labelPdfUrl, labelZplUrl, labelDate';

COMMENT ON COLUMN "Return"."rma" IS 'user specifies or else autogenerate eg: RMA-mmddyyyy-xxxxx';

COMMENT ON COLUMN "Return"."rate" IS 'After markup rate';

COMMENT ON COLUMN "Return"."postageLabel" IS 'labelUrl, labelPdfUrl, labelZplUrl, labelDate';

ALTER TABLE "Address" ADD CONSTRAINT "fk_Address_organizationId_Organization_id" FOREIGN KEY ("organizationId") REFERENCES "Organization" ("id");

ALTER TABLE "Order" ADD CONSTRAINT "fk_Batch_id_Order_batchId" FOREIGN KEY ("batchId") REFERENCES "Batch" ("id");

ALTER TABLE "Order" ADD CONSTRAINT "fk_Order_id_OrderItem_orderId" FOREIGN KEY ("id") REFERENCES "OrderItem" ("orderId");

ALTER TABLE "Order" ADD CONSTRAINT "fk_Order_id_Return_orderId" FOREIGN KEY ("id") REFERENCES "Return" ("orderId");

ALTER TABLE "Order" ADD CONSTRAINT "fk_Order_id_Shipment_orderId" FOREIGN KEY ("id") REFERENCES "Shipment" ("orderId");

ALTER TABLE "Organization" ADD CONSTRAINT "fk_Organization_id_Batch_organizationId" FOREIGN KEY ("id") REFERENCES "Batch" ("organizationId");

ALTER TABLE "Organization" ADD CONSTRAINT "fk_Organization_id_Order_organizationId" FOREIGN KEY ("id") REFERENCES "Order" ("organizationId");

ALTER TABLE "Organization" ADD CONSTRAINT "fk_Organization_id_Product_organizationId" FOREIGN KEY ("id") REFERENCES "Product" ("organizationId");

ALTER TABLE "Organization" ADD CONSTRAINT "fk_Organization_id_Return_organizationId" FOREIGN KEY ("id") REFERENCES "Return" ("organizationId");

ALTER TABLE "Organization" ADD CONSTRAINT "fk_Organization_id_Shipment_organizationId" FOREIGN KEY ("id") REFERENCES "Shipment" ("organizationId");

ALTER TABLE "Organization" ADD CONSTRAINT "fk_Organization_id_User_organizationId" FOREIGN KEY ("id") REFERENCES "User" ("organizationId");

ALTER TABLE "Parcel" ADD CONSTRAINT "fk_Parcel_organizationId_Organization_id" FOREIGN KEY ("organizationId") REFERENCES "Organization" ("id");

ALTER TABLE "Product" ADD CONSTRAINT "fk_Product_id_OrderItem_productId" FOREIGN KEY ("id") REFERENCES "OrderItem" ("productId");

ALTER TABLE "Product" ADD CONSTRAINT "fk_Product_id_ReturnItem_productId" FOREIGN KEY ("id") REFERENCES "ReturnItem" ("productId");

ALTER TABLE "Return" ADD CONSTRAINT "fk_Return_id_ReturnItem_returnId" FOREIGN KEY ("id") REFERENCES "ReturnItem" ("returnId");

ALTER TABLE "UserPermission" ADD CONSTRAINT "fk_UserPermission_permissionId_Permission_id" FOREIGN KEY ("permissionId") REFERENCES "Permission" ("id");

ALTER TABLE "UserPermission" ADD CONSTRAINT "fk_UserPermission_userId_User_id" FOREIGN KEY ("userId") REFERENCES "User" ("id");

ALTER TABLE "User" ADD FOREIGN KEY ("id") REFERENCES "Account" ("userId");

ALTER TABLE "Organization" ADD FOREIGN KEY ("id") REFERENCES "UserInvite" ("organizationId");

ALTER TABLE "User" ADD FOREIGN KEY ("id") REFERENCES "UserInvite" ("invitedByUserId");

ALTER TABLE "UserInvite" ADD FOREIGN KEY ("id") REFERENCES "UserInvitePermission" ("userInviteId");

ALTER TABLE "Permission" ADD FOREIGN KEY ("id") REFERENCES "UserInvitePermission" ("permissionId");

ALTER TABLE "Organization" ADD FOREIGN KEY ("id") REFERENCES "BillingAddress" ("organizationId");

ALTER TABLE "Address" ADD FOREIGN KEY ("id") REFERENCES "Order" ("fromAddressId");

ALTER TABLE "Store" ADD FOREIGN KEY ("id") REFERENCES "Order" ("storeId");

ALTER TABLE "Store" ADD FOREIGN KEY ("id") REFERENCES "StoreProduct" ("storeId");

ALTER TABLE "Product" ADD FOREIGN KEY ("id") REFERENCES "StoreProduct" ("productId");

ALTER TABLE "OrganizationCarrierPricing" ADD FOREIGN KEY ("organizationId") REFERENCES "Organization" ("id");

ALTER TABLE "OrganizationCarrierServicePricing" ADD FOREIGN KEY ("organizationCarrierPricingId") REFERENCES "OrganizationCarrierPricing" ("id");

ALTER TABLE "Order" ADD FOREIGN KEY ("parentOrderId") REFERENCES "Order" ("id");

ALTER TABLE "Organization" ADD FOREIGN KEY ("id") REFERENCES "Wallet" ("organizationId");

ALTER TABLE "Wallet" ADD FOREIGN KEY ("id") REFERENCES "WalletTransaction" ("walletId");

ALTER TABLE "Organization" ADD FOREIGN KEY ("id") REFERENCES "Payment" ("organizationId");

ALTER TABLE "User" ADD FOREIGN KEY ("id") REFERENCES "Payment" ("userId");

ALTER TABLE "Organization" ADD FOREIGN KEY ("id") REFERENCES "PaymentMethod" ("organizationId");

ALTER TABLE "Wallet" ADD FOREIGN KEY ("id") REFERENCES "RechargePlan" ("walletId");

ALTER TABLE "Payment" ADD FOREIGN KEY ("id") REFERENCES "WalletTransaction" ("paymentId");

ALTER TABLE "Organization" ADD FOREIGN KEY ("id") REFERENCES "Claim" ("organizationId");

ALTER TABLE "Shipment" ADD FOREIGN KEY ("id") REFERENCES "Claim" ("shipmentId");

ALTER TABLE "Return" ADD FOREIGN KEY ("id") REFERENCES "Claim" ("returnId");

ALTER TABLE "ClaimAttachments" ADD FOREIGN KEY ("claimId") REFERENCES "Claim" ("id");

ALTER TABLE "Shipment" ADD FOREIGN KEY ("id") REFERENCES "Refund" ("shipmentId");

ALTER TABLE "Return" ADD FOREIGN KEY ("id") REFERENCES "Refund" ("returnId");

ALTER TABLE "Organization" ADD FOREIGN KEY ("id") REFERENCES "Refund" ("organizationId");
